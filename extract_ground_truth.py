#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取前20个案例的标准答案从MIMIC_HCC_GroundTruth.csv
"""

import pandas as pd
import os
from hcc_analyzer_simple import load_classification_data

def examine_csv_structure():
    """查看CSV文件的结构"""
    csv_file = "MIMIC_HCC_GroundTruth(MIMIC_HCC_GroundTruth).csv"
    
    try:
        print("📊 正在读取CSV文件...")
        # 先读取前几行来了解结构
        df_sample = pd.read_csv(csv_file, nrows=5)
        
        print(f"📋 CSV文件结构:")
        print(f"总列数: {len(df_sample.columns)}")
        print("\n列名:")
        for i, col in enumerate(df_sample.columns):
            print(f"  {i:2d}: {col}")
        
        print(f"\n前5行数据预览:")
        print(df_sample)
        
        # 读取完整文件来获取总行数
        print("\n📊 正在读取完整文件...")
        df_full = pd.read_csv(csv_file)
        print(f"总行数: {len(df_full)}")
        
        return df_full
        
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return None

def get_20_case_ids():
    """获取前20个案例的ID列表"""
    try:
        classification_data = load_classification_data()
        case_ids = list(classification_data.keys())[:20]
        print(f"📋 前20个案例ID: {case_ids}")
        return case_ids
    except Exception as e:
        print(f"❌ 获取案例ID失败: {e}")
        return []

def extract_ground_truth_for_20_cases():
    """提取前20个案例的标准答案"""
    
    # 获取前20个案例ID
    case_ids = get_20_case_ids()
    if not case_ids:
        print("❌ 无法获取案例ID列表")
        return
    
    # 读取CSV文件
    df = examine_csv_structure()
    if df is None:
        return
    
    # 查找案例ID对应的列
    print(f"\n🔍 查找案例ID匹配...")
    
    # 寻找可能包含案例ID的列
    id_columns = []
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['id', 'case', 'note']):
            id_columns.append(col)
    
    print(f"可能包含ID的列: {id_columns}")
    
    # 检查每个可能的ID列
    matched_cases = {}
    for col in id_columns:
        print(f"\n检查列 '{col}':")
        sample_values = df[col].head(10).tolist()
        print(f"  样本值: {sample_values}")
        
        # 检查是否有我们的案例ID
        for case_id in case_ids:
            matching_rows = df[df[col] == case_id]
            if not matching_rows.empty:
                matched_cases[case_id] = matching_rows.index.tolist()
                print(f"  ✅ 找到案例 {case_id} 在行 {matching_rows.index.tolist()}")
    
    if not matched_cases:
        # 尝试部分匹配
        print(f"\n🔍 尝试部分匹配...")
        for col in id_columns:
            for case_id in case_ids:
                # 提取案例ID的主要部分（去掉-DS-后缀）
                case_main_id = case_id.split('-')[0] if '-' in case_id else case_id
                matching_rows = df[df[col].astype(str).str.contains(case_main_id, na=False)]
                if not matching_rows.empty:
                    matched_cases[case_id] = matching_rows.index.tolist()
                    print(f"  ✅ 部分匹配案例 {case_id} (搜索 {case_main_id}) 在行 {matching_rows.index.tolist()}")
                    break
    
    print(f"\n📊 匹配结果总结:")
    print(f"找到 {len(matched_cases)} 个案例的匹配项")
    
    if matched_cases:
        # 提取匹配案例的所有数据
        print(f"\n📋 提取标准答案数据...")
        
        ground_truth_data = {}
        for case_id, row_indices in matched_cases.items():
            # 如果有多行，取第一行
            row_idx = row_indices[0]
            case_data = df.iloc[row_idx].to_dict()
            ground_truth_data[case_id] = case_data
            
            print(f"\n案例 {case_id} (行 {row_idx}):")
            # 显示前10个非空列的数据
            non_empty_data = {k: v for k, v in case_data.items() if pd.notna(v) and str(v).strip() != ''}
            for i, (k, v) in enumerate(non_empty_data.items()):
                if i < 10:  # 只显示前10个
                    print(f"  {k}: {v}")
                elif i == 10:
                    print(f"  ... 还有 {len(non_empty_data)-10} 个字段")
                    break
        
        return ground_truth_data
    
    else:
        print("❌ 没有找到匹配的案例")
        return {}

if __name__ == "__main__":
    ground_truth = extract_ground_truth_for_20_cases()
    
    if ground_truth:
        print(f"\n✅ 成功提取 {len(ground_truth)} 个案例的标准答案")
        
        # 保存到文件
        output_file = "ground_truth_20cases.json"
        import json
        with open(output_file, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型
            clean_data = {}
            for case_id, data in ground_truth.items():
                clean_data[case_id] = {}
                for k, v in data.items():
                    if pd.isna(v):
                        clean_data[case_id][k] = None
                    else:
                        clean_data[case_id][k] = str(v)
            
            json.dump(clean_data, f, indent=2, ensure_ascii=False)
        
        print(f"📁 标准答案已保存到: {output_file}")
    else:
        print("❌ 未能提取标准答案") 