#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的Azure OpenAI客户端
解决超时、重试和错误处理问题
"""

import requests
import json
import time
import random
from functools import wraps
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ImprovedAzureClient:
    def __init__(self, api_key, endpoint, deployment_name):
        self.api_key = api_key
        self.endpoint = endpoint
        self.deployment_name = deployment_name
        self.session = self.create_robust_session()
        
    def create_robust_session(self):
        """创建具有重试功能的session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=5,
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=2,
            allowed_methods=["POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    @retry_with_backoff(max_retries=5, base_delay=2, max_delay=300)
    def analyze_content(self, content, filename=None):
        """分析内容的主要方法"""
        
        headers = {
            "api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        data = {
            "messages": [
                {"role": "system", "content": "You are a medical expert..."},
                {"role": "user", "content": content}
            ],
            "max_tokens": 3000,
            "temperature": 0.1
        }
        
        try:
            print("🤖 发送API请求...", end="", flush=True)
            
            response = self.session.post(
                f"{self.endpoint}/openai/deployments/{self.deployment_name}/chat/completions?api-version=2024-02-15-preview",
                headers=headers,
                json=data,
                timeout=(30, 600)  # 30秒连接，600秒读取
            )
            
            if response.status_code == 200:
                result = response.json()
                print(" ✅", flush=True)
                return self.process_response(result)
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:100]}"
                print(f" ❌ {error_msg}", flush=True)
                raise requests.exceptions.RequestException(error_msg)
                
        except Exception as e:
            print(f" ❌ {str(e)[:50]}...", flush=True)
            raise e
    
    def process_response(self, result):
        """处理API响应"""
        if 'choices' not in result or len(result['choices']) == 0:
            raise ValueError("Invalid response format")
        
        gpt_response = result['choices'][0]['message']['content'].strip()
        
        try:
            # 解析JSON响应
            start = gpt_response.find('{')
            end = gpt_response.rfind('}') + 1
            
            if start != -1 and end != -1:
                json_content = gpt_response[start:end]
                parsed = json.loads(json_content)
                return parsed.get('results', [])
            else:
                raise json.JSONDecodeError("No valid JSON found", gpt_response, 0)
                
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析错误: {e}")
            return self.create_fallback_results(f"JSON parsing error: {e}")

def retry_with_backoff(max_retries=5, base_delay=2, max_delay=300):
    """带指数退避的重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (requests.exceptions.Timeout, 
                        requests.exceptions.ConnectionError,
                        json.JSONDecodeError,
                        requests.exceptions.RequestException) as e:
                    
                    if attempt == max_retries - 1:
                        print(f"❌ 最终失败 (尝试 {max_retries} 次): {e}")
                        raise e
                    
                    # 计算延迟时间（指数退避 + 随机抖动）
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.1, 0.5) * delay
                    wait_time = delay + jitter
                    
                    print(f"⏰ 重试 {attempt + 1}/{max_retries}，等待 {wait_time:.1f}秒...")
                    time.sleep(wait_time)
                    
            return None
        return wrapper
    return decorator
