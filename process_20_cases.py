#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process the 20 specific cases with HCC analyzer simple
"""

import subprocess
import sys
import time

# List of 20 cases to process
CASES = [
    "10056223-DS-5",
    "10151324-DS-16", 
    "10151324-DS-18",
    "10225793-DS-33",
    "10225793-DS-34",
    "10225793-DS-36",
    "10262565-DS-19",
    "10262565-DS-20",
    "10388675-DS-19",
    "10388675-DS-20",
    "10666715-DS-8",
    "10747475-DS-4",
    "10760122-DS-21",
    "10880579-DS-11",
    "10902714-DS-17",
    "10923555-DS-9",
    "10960817-DS-14",
    "11102747-DS-2",
    "11198012-DS-6",
    "11198012-DS-7"
]

def main():
    print(f"Processing {len(CASES)} cases with HCC analyzer simple...")
    
    successful = 0
    failed = 0
    
    for i, case_id in enumerate(CASES, 1):
        print(f"\n[{i}/{len(CASES)}] Processing {case_id}...")
        
        try:
            # Run the HCC analyzer simple on this case
            result = subprocess.run([
                "python3", "hcc_analyzer_simple.py", 
                "--file", f"txts/{case_id}.txt"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {case_id} processed successfully")
                successful += 1
            else:
                print(f"❌ {case_id} failed with return code {result.returncode}")
                print(f"Error: {result.stderr}")
                failed += 1
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {case_id} timed out")
            failed += 1
        except Exception as e:
            print(f"❌ {case_id} failed with exception: {e}")
            failed += 1
        
        # Small delay between cases to avoid overwhelming the API
        if i < len(CASES):
            time.sleep(2)
    
    print(f"\n📊 Processing complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {successful/len(CASES)*100:.1f}%")

if __name__ == "__main__":
    main()
