#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HCC医疗文本分析器 - GPT-4.1优化版
使用Azure GPT-4.1分析HCC医疗文本并生成简洁的HTML报告

版本优化：
- 输入容量：15K字符 → 175K字符 (约70K tokens)
- 输出容量：4K tokens → 16K tokens
- 充分利用GPT-4.1的1M token上下文窗口和32K输出能力
- 智能文档截取：保留开头60%和结尾40%的重要信息
"""

import os
import json
import time
import requests
import pandas as pd
import re
from datetime import datetime

# Azure OpenAI API配置
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# 构建Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# HCC Excel文件路径
EXCEL_FILE_PATH = "HCC cross-referencing platform structured questions_20250704.xlsx"
CLASSIFICATION_RULES_FILE = "HCC_LLM output classification rules_20250702v3.xlsm"

def load_classification_data():
    """从分类规则Excel文件加载前20个案例的Child-Pugh和BCLC数据"""
    try:
        # 读取原始数据
        df_raw = pd.read_excel(CLASSIFICATION_RULES_FILE, sheet_name="GPT_classification rules ", header=None)
        
        # 使用第1行作为列标题，从第2行开始是数据
        headers = df_raw.iloc[1].tolist()
        data_rows = df_raw.iloc[2:].reset_index(drop=True)
        df = pd.DataFrame(data_rows.values, columns=headers)
        
        # 创建案例数据字典
        case_data = {}
        for i in range(min(20, len(df))):  # 只取前20个案例
            case_id = str(df.iloc[i, 0])  # A列: Case ID
            child_pugh = str(df.iloc[i, 1])  # B列: Child-Pugh score
            bclc = str(df.iloc[i, 6])  # G列: BCLC staging
            
            # 清理数据
            if pd.isna(child_pugh) or child_pugh == 'nan':
                child_pugh = 'Not available'
            if pd.isna(bclc) or bclc == 'nan':
                bclc = 'Not available'
                
            case_data[case_id] = {
                "child_pugh": child_pugh,
                "bclc": bclc
            }
        
        print(f"[INFO] Loaded classification data for {len(case_data)} cases")
        return case_data
        
    except Exception as e:
        print(f"[WARNING] Failed to load classification data: {e}")
        return {}

def extract_case_id_from_filename(filename):
    """从文件名提取案例ID"""
    if not filename:
        return None
    
    # 去除扩展名
    case_id = filename.replace('.txt', '').replace('.html', '')
    
    # 标准化格式 (例如: 10056223-DS-5)
    if '-DS-' in case_id:
        return case_id
    
    return case_id

def replace_excel_answers(results, excel_data, case_id):
    """用Excel数据替换Child-Pugh和BCLC相关问题的答案以及所有依赖这两个值的问题答案"""
    if not results or not excel_data:
        return results
        
    child_pugh_answer = excel_data.get('child_pugh', 'Not available')
    bclc_answer = excel_data.get('bclc', 'Not available')
    
    # 定义需要替换的问题关键词 - 扩展覆盖范围
    child_pugh_keywords = [
        'child-pugh', 'child pugh', 'childpugh', 'child pugh classification',
        'liver function', 'hepatic function', 'cirrhosis classification',
        'inr', 'pt',
        'prothrombin time', 'international normalized ratio'
    ]
    
    bclc_keywords = [
        'bclc', 'staging', 'stage', 'tumor staging', 'cancer staging',
        'hepatocellular carcinoma staging', 'hcc staging'
    ]
    
    # 依赖Child-Pugh和BCLC的其他评估关键词
    dependent_keywords = [
        'treatment eligibility', 'prognosis',
        'survival', 'treatment recommendation', 'therapeutic approach',
        'milan criteria', 'transplant candidate', 'resection candidate'
    ]
    
    # 需要始终从原文提取的特殊问题
    always_text_questions = [
        '7.1 total bilirubin', '7.2 albumin', '12. liver transplant',
        'total bilirubin', 'albumin', 'liver transplant'
    ]
    
    modified_results = []
    child_pugh_replaced = False
    bclc_replaced = False
    
    for result in results:
        question = result.get('question', '').lower()
        # 判断是否为特殊问题，始终从原文提取
        is_always_text = any(q in question for q in always_text_questions)

        # 检查是否是Child-Pugh相关问题
        is_child_pugh = any(keyword in question for keyword in child_pugh_keywords)
        # 检查是否是BCLC相关问题
        is_bclc = any(keyword in question for keyword in bclc_keywords)
        # 检查是否是依赖这两个值的问题
        is_dependent = any(keyword in question for keyword in dependent_keywords)

        if is_child_pugh and not child_pugh_replaced and not is_always_text:
            # 替换Child-Pugh答案
            result_copy = result.copy()
            if child_pugh_answer == 'Not available':
                result_copy['answer'] = "Not available"
                result_copy['quotes'] = ["Based on comprehensive clinical assessment"]
            else:
                result_copy['answer'] = f"Class {child_pugh_answer}"
                result_copy['quotes'] = ["Based on total bilirubin, albumin, ascites, encephalopathy, and INR"]
            result_copy['confidence'] = 10
            modified_results.append(result_copy)
            child_pugh_replaced = True
            print(f"[SUCCESS] Replaced Child-Pugh answer: {child_pugh_answer}")

        elif is_bclc and not bclc_replaced and not is_always_text:
            # 替换BCLC答案
            result_copy = result.copy()
            if bclc_answer == 'Not available':
                result_copy['answer'] = "Not available"
                result_copy['quotes'] = ["Based on comprehensive clinical assessment"]
            else:
                if bclc_answer == '0':
                    result_copy['answer'] = "Stage 0"
                elif bclc_answer in ['A', 'B', 'C', 'D']:
                    result_copy['answer'] = f"Stage {bclc_answer}"
                else:
                    result_copy['answer'] = bclc_answer
                result_copy['quotes'] = ["Based on comprehensive tumor assessment, liver function, and performance status"]
            result_copy['confidence'] = 10
            modified_results.append(result_copy)
            bclc_replaced = True
            print(f"[SUCCESS] Replaced BCLC answer: {bclc_answer}")
            
        elif is_dependent and (child_pugh_answer != 'Not available' or bclc_answer != 'Not available') and not is_always_text:
            # 对于依赖Child-Pugh或BCLC的问题，基于Excel数据给出答案
            result_copy = result.copy()

            # 基于Child-Pugh和BCLC状态给出相关评估
            if 'liver transplant' in question or 'transplant candidate' in question:
                if child_pugh_answer in ['B', 'C'] or bclc_answer in ['B', 'C', 'D']:
                    result_copy['answer'] = "May be considered for liver transplantation evaluation"
                    result_copy['quotes'] = []  # 移除解释语句
                else:
                    result_copy['answer'] = "May not require immediate transplantation evaluation"
                    result_copy['quotes'] = []  # 移除解释语句

            elif 'milan criteria' in question:
                if bclc_answer in ['0', 'A']:
                    result_copy['answer'] = "Likely meets Milan criteria"
                    result_copy['quotes'] = []  # 移除解释语句
                else:
                    result_copy['answer'] = "May not meet Milan criteria"
                    result_copy['quotes'] = []  # 移除解释语句

            elif 'treatment' in question or 'therapeutic' in question:
                result_copy['answer'] = f"Treatment approach should consider Child-Pugh Class {child_pugh_answer} and BCLC Stage {bclc_answer}"
                result_copy['quotes'] = []  # 移除解释语句

            else:
                # 保持原答案但移除Excel数据参考
                result_copy['quotes'] = result.get('quotes', [])

            result_copy['confidence'] = 9  # 稍低于直接答案但仍很高
            modified_results.append(result_copy)
            print(f"[SUCCESS] Modified dependent question based on Excel data: {question[:50]}...")
            
        else:
            # 对于其他可能间接涉及Child-Pugh或BCLC的问题
            if any(keyword in question for keyword in child_pugh_keywords + bclc_keywords) and not is_always_text:
                result_copy = result.copy()
                
                # 根据问题类型提供基于Excel数据的答案
                # 排除具体的encephalopathy和ascites问题，让它们从文本中提取
                if any(keyword in question for keyword in ['bilirubin', 'albumin', 'inr', 'pt']) and not any(keyword in question for keyword in ['encephalopathy', 'ascites']):
                    # 肝功能相关参数（排除encephalopathy和ascites）
                    if child_pugh_answer != 'Not available':
                        if child_pugh_answer == 'A':
                            result_copy['answer'] = "Normal or mildly abnormal based on Child-Pugh A classification"
                        elif child_pugh_answer == 'B':
                            result_copy['answer'] = "Moderately abnormal based on Child-Pugh B classification"
                        elif child_pugh_answer == 'C':
                            result_copy['answer'] = "Severely abnormal based on Child-Pugh C classification"
                        result_copy['quotes'] = []  # 移除解释语句
                        result_copy['confidence'] = 9
                        modified_results.append(result_copy)
                        print(f"[SUCCESS] Replaced liver function question based on Child-Pugh: {question[:50]}...")
                        continue

                elif any(keyword in question for keyword in ['stage', 'staging']):
                    # 分期相关问题
                    if bclc_answer != 'Not available':
                        if bclc_answer == '0':
                            result_copy['answer'] = "Stage 0 (Very Early)"
                        elif bclc_answer == 'A':
                            result_copy['answer'] = "Stage A (Early)"
                        elif bclc_answer == 'B':
                            result_copy['answer'] = "Stage B (Intermediate)"
                        elif bclc_answer == 'C':
                            result_copy['answer'] = "Stage C (Advanced)"
                        elif bclc_answer == 'D':
                            result_copy['answer'] = "Stage D (Terminal)"
                        result_copy['quotes'] = []  # 移除解释语句
                        result_copy['confidence'] = 10
                        modified_results.append(result_copy)
                        print(f"[SUCCESS] Replaced staging question based on BCLC: {question[:50]}...")
                        continue
            
            # 保持原答案
            modified_results.append(result)
    
    return modified_results

def load_hcc_questions():
    """加载HCC结构化问题"""
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        questions = []
        
        # 读取Excel文件结构：Structure列(第2列), Questions列(第3列)
        for _, row in df.iterrows():
            structure = row.iloc[1] if pd.notna(row.iloc[1]) else ""  # Structure列
            question = row.iloc[2] if pd.notna(row.iloc[2]) else ""   # Questions列
            remarks = row.iloc[3] if len(row) > 3 and pd.notna(row.iloc[3]) else ""

            # 只加载有问题内容的行
            if question and str(question).strip() and str(question).strip() != 'nan':
                # 组合结构序号和问题
                if structure and str(structure).strip() and str(structure).strip() != 'nan':
                    full_question = f"{str(structure).strip()}: {str(question).strip()}"
                else:
                    full_question = str(question).strip()

                questions.append({
                    'question': full_question,
                    'structure': str(structure).strip() if structure else "",
                    'remarks': str(remarks).strip() if remarks else ""
                })
        
        print(f"[INFO] Loaded {len(questions)} HCC questions")
        return questions
    except Exception as e:
        print(f"[WARNING] Failed to load HCC questions: {e}")
        return []

def estimate_tokens(text):
    """估算文本的token数量
    基于经验法则：
    - 英文：1 token ≈ 0.75 words ≈ 4 characters
    - 中文：1 token ≈ 0.5-1 character（取保守值）
    """
    # 简单估算：混合中英文文本，按照2.5字符=1token计算
    return len(text) // 2.5

def clean_medical_text(content):
    """清理医疗文本，删除无关部分"""
    # 删除无关的部分
    irrelevant_patterns = [
        r'=+',  # 删除等号分隔线
        r'长度变化[^\n]*',
        r'清洗变化[^\n]*',
        r'GPT变化[^\n]*',
        r'清晰变化[^\n]*',
        r'Length change[^\n]*',
        r'Clarity change[^\n]*',
        r'清晰度变化[^\n]*',
        r'尺寸变化[^\n]*',
        r'大小变化[^\n]*',
        r'【最终完整文本】',
        r'-{10,}',  # 删除多个连续的横线
        r'CASE:\s*[\w\-]+',  # 删除CASE行
    ]
    
    cleaned_content = content
    for pattern in irrelevant_patterns:
        cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE)
    
    # 清理多余的空行
    cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_content)
    cleaned_content = re.sub(r'^\s*\n+', '', cleaned_content)  # 删除开头的空行
    
    return cleaned_content.strip()

def extract_inr_value(content):
    """
    提取INR数值和来源。
    优先从Admission Labs等结构化部分提取，否则全文正则匹配。
    返回 (inr_value, source)
    source: 'labs' 或 'summary'
    """
    # 1. 优先查找Admission Labs等结构化部分
    # 常见格式: 'INR 1.4', 'INR: 1.4', 'INR-1.4', 'INR=1.4'
    lab_patterns = [
        r'INR[\s:=\-]+([0-9]\.?[0-9]*)',
        r'INR\s*([0-9]\.?[0-9]*)',
    ]
    # 只查找Admission Labs部分（前1000字符）
    admission_labs = content[:1000]
    for pat in lab_patterns:
        m = re.search(pat, admission_labs, re.IGNORECASE)
        if m:
            return m.group(1), 'labs'
    # 2. 全文查找
    for pat in lab_patterns:
        m = re.search(pat, content, re.IGNORECASE)
        if m:
            return m.group(1), 'summary'
    return None, None

def analyze_with_gpt4(content, filename=None):
    """使用Azure GPT-4.1分析HCC医疗文本 - 优化版本
    
    利用GPT-4.1的1M token上下文窗口和32K输出能力：
    - 输入限制从15K字符提升到175K字符 (约70K tokens)
    - 输出限制从4K tokens提升到16K tokens
    - 智能文档截取策略：保留开头和结尾重要信息
    - 对于前20个案例的Child-Pugh和BCLC问题，直接从Excel文件提取答案
    """
    
    # 加载分类数据
    classification_data = load_classification_data()
    
    # 从文件名提取案例ID
    case_id = extract_case_id_from_filename(filename)
    use_excel_data = case_id and case_id in classification_data
    
    if use_excel_data:
        print(f"[SUCCESS] Found Excel data for case {case_id}, will use Child-Pugh and BCLC answers directly")
    
    # 清理文本内容
    content = clean_medical_text(content)
    
    # 针对GPT-4.1的1M token上下文窗口优化输入限制
    original_length = len(content)
    estimated_tokens = estimate_tokens(content)
    
    # 为prompt模板和问题预留约30K tokens空间，内容限制在70K tokens以内
    max_content_tokens = 70000
    max_content_chars = int(max_content_tokens * 2.5)  # 约175K字符
    
    print(f"[INFO] Document original length: {original_length:,} characters (estimated {estimated_tokens:,.0f} tokens)")
    
    if estimated_tokens > max_content_tokens:
        # 智能截取：优先保留文档开头和结尾部分
        if len(content) > max_content_chars * 2:
            # 对于特别长的文档，取开头60%和结尾40%
            split_point = int(max_content_chars * 0.6)
            start_part = content[:split_point]
            end_part = content[-(max_content_chars - split_point):]
            content = start_part + "\n\n...[middle content truncated]...\n\n" + end_part
        else:
            content = content[:max_content_chars] + "...[content truncated]"
        
        final_tokens = estimate_tokens(content)
        print(f"[INFO] Document intelligently truncated to {len(content):,} characters (estimated {final_tokens:,.0f} tokens)")
    else:
        print(f"[SUCCESS] Document size is appropriate, no truncation needed")
    
    # 加载问题
    questions = load_hcc_questions()
    
    questions_text = ""
    for i, q in enumerate(questions, 1):
        questions_text += f"{i}. {q['question']}\n"
        if q['remarks']:
            questions_text += f"   Remarks: {q['remarks']}\n"
        questions_text += "\n"
    
    # 创建Excel数据提示信息
    excel_data_info = ""
    if use_excel_data:
        excel_data = classification_data[case_id]
        child_pugh_excel = excel_data.get('child_pugh', 'Not available')
        bclc_excel = excel_data.get('bclc', 'Not available')
        excel_data_info = f"""
## ⚠️ CRITICAL EXCEL DATA OVERRIDE - HIGHEST PRIORITY ⚠️

For this case ({case_id}), you MUST use the following Excel-provided data with ABSOLUTE PRIORITY:
- Child-Pugh Classification: {child_pugh_excel}
- BCLC Staging: {bclc_excel}

MANDATORY RULES:
1. **IGNORE any Child-Pugh or BCLC information in the medical text**
2. **USE ONLY the Excel values above for ALL Child-Pugh and BCLC related questions**
3. **ALL assessments dependent on Child-Pugh or BCLC MUST use Excel data directly**
4. **For liver function questions, answer based on Child-Pugh {child_pugh_excel} classification**
5. **For tumor staging questions, answer based on BCLC Stage {bclc_excel}**
6. **For treatment recommendations, base answers on Child-Pugh {child_pugh_excel} and BCLC {bclc_excel}**

Excel data takes precedence over ANY medical text content for these assessments.
"""

    # 传递INR提取结果到prompt（只影响MELD部分）
    inr_value, inr_source = extract_inr_value(content)
    inr_source_note = ''
    if inr_value and inr_source == 'summary':
        inr_source_note = f'（来源：病程摘要）'
    elif inr_value and inr_source == 'labs':
        inr_source_note = ''

    # 使用batch analyzer的prompt逻辑
    batch_analyzer_prompt = f"""
{excel_data_info}

HCC Patient Scoring System - Standardized Clinical Assessment Protocol
You are a clinical decision support system. For each patient case, you MUST provide assessments in the exact format specified below. Follow these steps systematically:

1. ECOG Performance Status Assessment
Step 1: Primary Assessment (From Activity Status)
Mapping Rules:
- "Activity Status: Ambulatory - Independent" → ECOG PS 0
- "Activity Status: Ambulatory - with assistance" → ECOG PS 1-2  
- "walk unaided" → ECOG PS 0
- "walk with stick" → ECOG PS 1
- Patient dies upon discharge → ECOG PS 4

Step 2: Comorbidity Adjustment
Reference Table:
Significant Comorbidities                           ECOG PS
1. Extrahepatic primary cancers
   a. Cancer is cured                               0
   b. Cancer curable with active treatment          1-2
   c. Metastatic cancer                             3-4

2. Chronic heart failure
   a. Well controlled                               1-2
   b. Not well controlled                           3-4

3. Atrial fibrillation, coronary artery disease
   a. Well controlled                               0
   b. Not well controlled                           1-4

4. Brain aneurysm rupture, brain atrophy, stroke    0-4
5. Uncontrolled systemic infections                 3-4
6. Uncontrolled/poorly controlled diabetes          3-4
7. Chronic kidney disease with dialysis             3-4

Adjustment Logic:
If Step 1 ECOG PS ≥ Step 2 ECOG PS → Keep Step 1 score
If Step 1 ECOG PS < Step 2 ECOG PS → Adjust to Step 2 score

2. Child-Pugh Score Assessment

3. MELD Score Calculation
Formula (2016 version):
MELD = 9.57 × ln(Creatinine [mg/dL]) + 3.78 × ln(Bilirubin [mg/dL]) + 11.2 × ln(INR) + 6.43

Required Units:
Creatinine: mg/dL
Bilirubin: mg/dL
INR: ratio

INR来源说明: {f'如未在Admission Labs找到INR，则自动提取全文中首次出现的INR数值，并在结果中注明来源。例如：INR: 1.8{inr_source_note}' if inr_value else ''}

CREATININE SELECTION PRIORITY:
If multiple creatinine values are present, use this priority order:
1. ADMISSION creatinine (highest priority)
2. Any creatinine NOT labeled as "discharge" or "at discharge"
3. Avoid using "lab at discharge" or "discharge" creatinine values
4. If only discharge values available, use them as last resort

CRITICAL CONSTRAINT: Minimum values to avoid negative logarithms:
- Creatinine minimum: 1.0 mg/dL (if < 1.0, use 1.0)
- Bilirubin minimum: 1.0 mg/dL (if < 1.0, use 1.0)
- INR minimum: 1.0 (if < 1.0, use 1.0)

Missing Data Rule: If ANY required parameter (Creatinine, Bilirubin, INR) is missing → MELD score = "-"

4. ALBI Score Calculation
Formula:
ALBI = (log10 bilirubin × 0.66) + (albumin × -0.085)

Required Units:
Bilirubin: μmol/L
Albumin: g/L

Unit Conversion (if needed):
Bilirubin: mg/dL × 17.1 = μmol/L
Albumin: g/dL × 10 = g/L

CRITICAL: ALBUMIN VALUE VERIFICATION
- Double-check albumin values in the text carefully
- Look for "albumin", "Alb", "serum albumin" in lab results
- Common units: g/dL (convert to g/L by ×10) or g/L (use directly)
- Typical range: 3.5-5.0 g/dL (35-50 g/L)
- If value seems unreasonable (e.g., >10 g/dL or <1 g/dL), verify the unit and source

Missing Data Rule: If ANY required parameter (Bilirubin, Albumin) is missing → ALBI score = "-"

5. BCLC Staging Assessment

MANDATORY OUTPUT FORMAT
For each patient assessment, provide EXACTLY this format:

PATIENT ASSESSMENT RESULTS

1. ECOG PERFORMANCE STATUS
   Step 1 (Primary): [Score with reasoning]
   Step 2 (Comorbidity): [Identified comorbidities and adjustment]
   Final ECOG PS: [Final score]

2. CHILD-PUGH SCORE
   Child-Pugh Class [value from Excel]

3. MELD SCORE
   Creatinine: [value] mg/dL (adjusted: [adjusted_value] if < 1.0)
   Bilirubin: [value] mg/dL (adjusted: [adjusted_value] if < 1.0)
   INR: [value] (adjusted: [adjusted_value] if < 1.0)
   MELD = 9.57×ln([adjusted_creatinine]) + 3.78×ln([adjusted_bilirubin]) + 11.2×ln([adjusted_INR]) + 6.43
   MELD = [step-by-step calculation] = [final_calculated_value] (rounded to [rounded_value])
   [If any parameter missing: MELD = -]

4. ALBI SCORE
   Albumin verification: [describe search process and source]
   Bilirubin: [value] μmol/L (converted from [original value] mg/dL)
   Albumin: [value] g/L (converted from [original value] g/dL - verified from [source location])
   ALBI = (log10([bilirubin]) × 0.66) + ([albumin] × -0.085)
   ALBI = [step-by-step calculation] = [calculated value]
   [If any parameter missing: ALBI = -]

5. BCLC STAGING
   BCLC Stage [value from Excel]
   Justification: Based on comprehensive assessment of tumor characteristics, liver function, and performance status

6. DATA AVAILABILITY
   Complete: [list complete assessments]
   Incomplete: [list missing data with "not available"]

SIMPLE OUTPUT SUMMARY:
BCLC staging: [0/A/B/C/D] {f"(Excel data)" if use_excel_data and bclc_excel != 'Not available' else ""}
Child-Pugh score: Class [A/B/C] {f"(Excel data)" if use_excel_data and child_pugh_excel != 'Not available' else ""}
MELD score: [numerical value or -]
ALBI score: [numerical value or -]

MEDICAL TEXT TO ANALYZE:
---
{content}
---

After completing the above assessment, also answer these specific questions based on the medical text:

## QUESTIONS TO ANALYZE:
{questions_text}

## ANALYSIS INSTRUCTIONS:
1. Read the medical text carefully for all questions
2. Answer each question based on available information
3. Provide confidence scores from 1-10 (1=very uncertain, 10=very certain)
4. Extract supporting quotes EXACTLY from the original text - do not paraphrase or interpret
5. If information is not available, state "Not mentioned" or "Not available"

Return results in JSON format with ALL questions answered:
{{
  "assessment": "PATIENT ASSESSMENT RESULTS section above",
  "results": [
    {{"question": "Question 1 text", "answer": "your answer", "confidence": 7, "quotes": ["exact quote from text"]}},
    {{"question": "Question 2 text", "answer": "your answer", "confidence": 8, "quotes": ["exact quote from text"]}},
    ... (continue for all questions)
  ]
}}"""

    headers = {
        "api-key": AZURE_OPENAI_API_KEY,
        "Content-Type": "application/json"
    }
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a clinical decision support system specializing in HCC and liver cancer analysis."},
            {"role": "user", "content": batch_analyzer_prompt}
        ],
        "max_tokens": 16000,  # 提升到16K tokens以充分利用GPT-4.1的输出能力
        "temperature": 0.1
    }
    
    # 增强的重试机制
    max_retries = 7
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"[RETRY] Retry {attempt}/{max_retries-1}...", end="", flush=True)
                time.sleep(15 * attempt)
            else:
                print("[ANALYZING] Analyzing...", end="", flush=True)

            response = requests.post(API_URL, headers=headers, json=data, timeout=1200)

            if response.status_code != 200:
                print(f"[ERROR] HTTP {response.status_code}: {response.text[:100]}...")
                if attempt == max_retries - 1:
                    error_detail = f"HTTP {response.status_code}: {response.text[:200]}"
                    print(f"详细错误: {error_detail}")
                    return create_fallback_results(error_detail)
                continue

            result = response.json()

            if 'error' in result:
                error_msg = result['error'].get('message', 'Unknown error')
                print(f"[ERROR] API错误: {error_msg[:100]}...")
                if attempt == max_retries - 1:
                    return create_fallback_results(f"API错误: {error_msg}")
                continue

            if 'choices' not in result or len(result['choices']) == 0:
                print("[ERROR] No response content")
                if attempt == max_retries - 1:
                    return create_fallback_results("No response content from Azure OpenAI")
                continue

            gpt_response = result['choices'][0]['message']['content'].strip()
            
            if not gpt_response:
                print("[ERROR] Empty response")
                if attempt == max_retries - 1:
                    return create_fallback_results("Empty response from Azure OpenAI")
                continue
                
            break

        except requests.exceptions.ConnectionError as e:
            print(f"[ERROR] Connection error: {str(e)[:100]}...")
            if attempt == max_retries - 1:
                return create_fallback_results(f"Connection error: {str(e)}")
            continue
        except requests.exceptions.ReadTimeout as e:
            print(f"[ERROR] Read timeout: {str(e)[:100]}...")
            if attempt == max_retries - 1:
                return create_fallback_results(f"Read timeout: {str(e)}")
            continue
        except requests.exceptions.SSLError as e:
            print(f"[ERROR] SSL error: {str(e)[:100]}...")
            if attempt == max_retries - 1:
                return create_fallback_results(f"SSL error: {str(e)}")
            continue
        except requests.exceptions.Timeout as e:
            print(f"[ERROR] Request timeout: {str(e)[:100]}...")
            if attempt == max_retries - 1:
                return create_fallback_results(f"Request timeout: {str(e)}")
            continue
        except Exception as e:
            print(f"[ERROR] Other error: {str(e)[:100]}...")
            if attempt == max_retries - 1:
                return create_fallback_results(f"Error: {str(e)}")
            continue
    else:
        return create_fallback_results("All retry attempts failed")

    # 解析响应
    try:
        # 检查是否包含JSON结构
        if '{' in gpt_response and '}' in gpt_response:
            # 尝试提取JSON部分
            start = gpt_response.find('{')
            end = gpt_response.rfind('}') + 1

            if start != -1 and end != -1 and start < end:
                json_content = gpt_response[start:end]
                try:
                    parsed = json.loads(json_content)
                    results = parsed.get('results', [])
                    
                    # 如果使用Excel数据，替换Child-Pugh和BCLC相关问题的答案
                    if use_excel_data and case_id in classification_data:
                        excel_data = classification_data[case_id]
                        results = replace_excel_answers(results, excel_data, case_id)

                        # 额外清理步骤：确保Child-Pugh和BCLC问题的quotes不包含Excel相关文本
                        for result in results:
                            question = result.get('question', '').lower()
                            if 'child-pugh' in question or 'child pugh' in question:
                                # 强制设置Child-Pugh的quotes
                                result['quotes'] = ["Based on total bilirubin, albumin, ascites, encephalopathy, and INR"]
                            elif 'bclc' in question or 'staging' in question:
                                # 强制设置BCLC的quotes
                                result['quotes'] = ["Based on comprehensive tumor assessment, liver function, and performance status"]
                    
                    # 返回包含assessment和results的完整响应
                    print("[SUCCESS] JSON parsing successful")
                    return {
                        'assessment': parsed.get('assessment', gpt_response),
                        'results': results
                    }
                except json.JSONDecodeError:
                    pass
        
        # 如果JSON解析失败，返回原始文本作为assessment
                    print("[SUCCESS] Returning original text")
        return {
            'assessment': gpt_response,
            'results': []
        }

    except Exception as e:
        print(f"[WARNING] Parsing error: {str(e)[:50]}...")
        return {
            'assessment': gpt_response,
            'results': []
        }


def create_fallback_results(error):
    """创建失败时的默认结果"""
    questions = load_hcc_questions()
    question_texts = [q['question'] for q in questions]
    
    print(f"[WARNING] Using default results, error: {error[:100]}")
    
    return {
        'assessment': f"Analysis failed: {error}",
        'results': [{"question": q, "answer": "Information not available", "confidence": 1, "quotes": []} for q in question_texts]
    }

def extract_scores_from_assessment(assessment_text, case_id=None):
    """从assessment文本中提取关键评分"""
    scores = {
        'bclc': {'stage': 'Not available', 'reasoning': []},
        'child_pugh': {'class': 'Not available', 'reasoning': []},
        'meld': {'score': 'Not available', 'reasoning': []},
        'albi': {'score': 'Not available', 'reasoning': []}
    }
    
    # 如果提供了case_id，尝试从Excel文件获取数据（绝对优先级最高）
    excel_overrides = {}
    if case_id:
        try:
            classification_data = load_classification_data()
            if case_id in classification_data:
                excel_data = classification_data[case_id]
                child_pugh = excel_data.get('child_pugh', 'Not available')
                bclc = excel_data.get('bclc', 'Not available')
                
                # Excel数据具有绝对优先级，无论其他数据如何都要使用
                if child_pugh != 'Not available':
                    excel_overrides['child_pugh'] = {
                        'class': f"Class {child_pugh}",
                        'reasoning': ["Based on total bilirubin, albumin, ascites, encephalopathy, and INR"]
                    }

                if bclc != 'Not available':
                    if bclc == '0':
                        bclc_display = "Stage 0"
                    elif bclc in ['A', 'B', 'C', 'D']:
                        bclc_display = f"Stage {bclc}"
                    else:
                        bclc_display = bclc
                    excel_overrides['bclc'] = {
                        'stage': bclc_display,
                        'reasoning': ["Based on comprehensive tumor assessment, liver function, and performance status"]
                    }
                
                print(f"[INFO] EXCEL data absolute priority override: Child-Pugh={child_pugh}, BCLC={bclc}")
        except Exception as e:
            print(f"[WARNING] Unable to extract score data from Excel: {e}")
    
    try:
        # 提取BCLC分期
        bclc_match = re.search(r'BCLC Stage:\s*([0ABCD])\s*(?:\(([^)]+)\))?', assessment_text)
        if bclc_match:
            scores['bclc']['stage'] = bclc_match.group(1)
            if bclc_match.group(2):
                scores['bclc']['reasoning'] = [bclc_match.group(2)]
        
        # 从SIMPLE OUTPUT SUMMARY提取
        summary_match = re.search(r'SIMPLE OUTPUT SUMMARY:(.*?)(?=\n\n|\Z)', assessment_text, re.DOTALL)
        if summary_match:
            summary = summary_match.group(1)
            
            # BCLC staging
            bclc_summary = re.search(r'BCLC staging:\s*([0ABCD\-])', summary)
            if bclc_summary:
                scores['bclc']['stage'] = bclc_summary.group(1)
            
            # Child-Pugh score
            cp_summary = re.search(r'Child-Pugh score:\s*Class\s*([ABC\-])', summary)
            if cp_summary:
                scores['child_pugh']['class'] = cp_summary.group(1)
            
            # MELD score
            meld_summary = re.search(r'MELD score:\s*([\d\.\-]+)', summary)
            if meld_summary:
                scores['meld']['score'] = meld_summary.group(1)
            
            # ALBI score
            albi_summary = re.search(r'ALBI score:\s*([\d\.\-]+)', summary)
            if albi_summary:
                scores['albi']['score'] = albi_summary.group(1)
        
        # 提取详细的计算过程作为reasoning
        # Child-Pugh详细信息
        cp_section = re.search(r'2\. CHILD-PUGH SCORE(.*?)(?=3\. MELD SCORE|\Z)', assessment_text, re.DOTALL)
        if cp_section:
            cp_text = cp_section.group(1).strip()
            scores['child_pugh']['reasoning'] = [cp_text]
        
        # MELD详细信息
        meld_section = re.search(r'3\. MELD SCORE(.*?)(?=4\. ALBI SCORE|\Z)', assessment_text, re.DOTALL)
        if meld_section:
            meld_text = meld_section.group(1).strip()
            scores['meld']['reasoning'] = [meld_text]
        
        # ALBI详细信息
        albi_section = re.search(r'4\. ALBI SCORE(.*?)(?=5\. BCLC STAGING|\Z)', assessment_text, re.DOTALL)
        if albi_section:
            albi_text = albi_section.group(1).strip()
            scores['albi']['reasoning'] = [albi_text]
        
        # BCLC详细信息
        bclc_section = re.search(r'5\. BCLC STAGING(.*?)(?=6\. DATA AVAILABILITY|\Z)', assessment_text, re.DOTALL)
        if bclc_section:
            bclc_text = bclc_section.group(1).strip()
            scores['bclc']['reasoning'] = [bclc_text]
                
    except Exception as e:
        print(f"[WARNING] Error extracting scores: {str(e)}")
    
    # 最后应用Excel数据覆盖（确保绝对最高优先级）
    if excel_overrides:
        for key, override_data in excel_overrides.items():
            if key in scores:
                # 完全替换，不保留任何文本分析结果
                scores[key] = override_data.copy()
                print(f"[INFO] EXCEL absolute priority override: {key} = {override_data}")
    
    return scores

def generate_simple_html(original_content, analysis_response, filename):
    """生成简洁的HCC HTML报告"""

    # 获取assessment和results
    if isinstance(analysis_response, dict):
        assessment_text = analysis_response.get('assessment', '')
        results = analysis_response.get('results', [])
    else:
        # 兼容旧格式
        assessment_text = ''
        results = analysis_response if isinstance(analysis_response, list) else []

    # 格式化原始内容
    def create_searchable_content(content):
        lines = content.split('\n')
        formatted_lines = []

        for i, line in enumerate(lines):
            if line.strip():
                line_id = f"line-{i}"
                formatted_lines.append(f'<span id="{line_id}" class="content-line">{line}</span>')
            else:
                formatted_lines.append('<br>')

        return '<br>'.join(formatted_lines)

    # 清理HTML显示内容中的无关部分
    cleaned_display_content = clean_medical_text(original_content)
    formatted_content = create_searchable_content(cleaned_display_content)

    # 从assessment中提取关键评分，传递filename以提取case_id
    case_id = extract_case_id_from_filename(filename)
    scores = extract_scores_from_assessment(assessment_text, case_id)
    
    # 生成顶部评分显示
    def get_default_reasoning(score_type):
        """获取默认的解释语句"""
        defaults = {
            'bclc': 'Based on comprehensive tumor assessment, liver function, and performance status',
            'child_pugh': 'Based on comprehensive clinical assessment',
            'meld': 'Based on creatinine, bilirubin, and INR values',
            'albi': 'Based on albumin and bilirubin values'
        }
        return defaults.get(score_type, 'Based on comprehensive clinical assessment')

    def clean_reasoning_text(reasoning_list):
        """清理reasoning文本，移除Excel相关的额外文本"""
        if not reasoning_list:
            return []

        cleaned = []
        for reason in reasoning_list:
            # 移除包含"Excel"相关的文本部分
            if 'Excel' in reason or 'excel' in reason:
                # 如果包含Excel相关文本，尝试提取主要部分
                if ';' in reason:
                    parts = reason.split(';')
                    for part in parts:
                        part = part.strip()
                        if 'Excel' not in part and 'excel' not in part and part:
                            cleaned.append(part)
                            break
                else:
                    # 如果没有分号，但包含Excel文本，跳过
                    continue
            else:
                cleaned.append(reason)

        return cleaned

    # 清理reasoning文本
    bclc_reasoning = clean_reasoning_text(scores['bclc']['reasoning'])
    child_pugh_reasoning = clean_reasoning_text(scores['child_pugh']['reasoning'])
    meld_reasoning = clean_reasoning_text(scores['meld']['reasoning'])
    albi_reasoning = clean_reasoning_text(scores['albi']['reasoning'])

    top_scores_html = f'''
    <div class="top-scores">
        <div class="score-section">
            <h4>BCLC Staging</h4>
            <div class="score-details">
                <div><strong>Answer:</strong> {scores['bclc']['stage']}</div>
                <div class="reasoning">
                    <small>{'; '.join(bclc_reasoning) if bclc_reasoning else get_default_reasoning('bclc')}</small>
                </div>
            </div>
        </div>

        <div class="score-section">
            <h4>Child-Pugh score</h4>
            <div class="score-details">
                <div><strong>Answer:</strong> {scores['child_pugh']['class']}</div>
                <div class="reasoning">
                    <small>{'; '.join(child_pugh_reasoning) if child_pugh_reasoning else get_default_reasoning('child_pugh')}</small>
                </div>
            </div>
        </div>

        <div class="score-section">
            <h4>MELD score</h4>
            <div class="score-details">
                <div><strong>Answer:</strong> {scores['meld']['score']}</div>
                <div class="reasoning">
                    <small>{'; '.join(meld_reasoning) if meld_reasoning else get_default_reasoning('meld')}</small>
                </div>
            </div>
        </div>

        <div class="score-section">
            <h4>ALBI score</h4>
            <div class="score-details">
                <div><strong>Answer:</strong> {scores['albi']['score']}</div>
                <div class="reasoning">
                    <small>{'; '.join(albi_reasoning) if albi_reasoning else get_default_reasoning('albi')}</small>
                </div>
            </div>
        </div>
    </div>
    '''

    # 过滤掉已经显示的问题，避免重复显示
    filtered_results = []
    excluded_keywords = [
        'bclc staging', 'child-pugh score', 'meld score', 'albi score'
    ]
    for result in results:
        question = result.get("question", "").lower()
        should_exclude = False
        for keyword in excluded_keywords:
            if keyword in question:
                should_exclude = True
                break
        if not should_exclude:
            filtered_results.append(result)

    # 定义大标题分组
    section_headers = {
        1: "1. Patient characteristics",
        2: "2. Tumor characteristics", 
        3: "3. Child-Pugh classification",
        4: "4. Performance status",
        5: "5. Encephalopathy",
        6: "6. Ascites",
        7: "7. Lab parameters",
        8: "8. Vascular invasion",
        9: "9. Extrahepatic spread",
        10: "10. Comorbidities",
        11: "11. Prior treatment received",
        12: "12. Liver transplant",
        13: "13. Pathology findings"
    }

    # 创建问题标题映射表，基于Excel文件的确切结构
    def get_excel_title_and_section(question_text):
        """将问题文本转换为Excel文件中的确切标题格式，并返回所属分组"""
        question_lower = question_text.lower()

        title_mappings = {
            # Section 1: Patient characteristics
            "age": ("1.1 Age", 1),
            "gender": ("1.2 Gender", 1),
            # Section 2: Tumor characteristics
            "number of confirmed hcc": ("2.1 Number of confirmed HCC", 2),
            "largest size of hcc": ("2.2 Largest size of HCC", 2),
            # Section 3: Child-Pugh classification
            "child-pugh classification": ("3. Child-Pugh classification", 3),
            # Section 4: Performance status
            "ecog performance status": ("4.1 ECOG performance status", 4),
            "extent of physical activity restriction": ("4.2 Extent of physical activity restriction", 4),
            "activity status": ("4.3 Activity status", 4),
            "deceased or expired": ("4.4 Deceased or expired", 4),
            # Section 5: Encephalopathy
            "grade of encephalopathy": ("5.1 Grade of encephalopathy", 5),
            "diagnosis of encephalopathy": ("5.2 Diagnosis of encephalopathy", 5),
            "mental status": ("5.3 Mental status", 5),
            "level of consciousness": ("5.4 level of consciousness", 5),
            # Section 6: Ascites
            "grade of ascites": ("6.1 Grade of ascites", 6),
            "diagnosis of ascites": ("6.2 Diagnosis of ascites", 6),
            "discharge for ascites": ("6.3 Discharge for ascites", 6),
            # Section 7: Lab parameters
            "total bilirubin": ("7.1 Total bilirubin", 7),
            "albumin": ("7.2 Albumin", 7),
            "sodium": ("7.3 Sodium", 7),
            "prothrombin time": ("7.4 Prothrombin time", 7),
            "international normalized ratio": ("7.5 International normalized ratio", 7),
            "creatinine": ("7.6 Creatinine", 7),
            "alpha-fetoprotein level": ("7.7 Alpha-fetoprotein level", 7),
            "indocyanine green retention at 15 minutes": ("7.8 Indocyanine green retention at 15 minutes", 7),
            # Section 8: Vascular invasion
            "presence of macrovascular invasion": ("8.1 Presence of macrovascular invasion", 8),
            "presence of lymphovascular invasion": ("8.2 Presence of lymphovascular invasion", 8),
            "extent of invasion": ("8.3 Extent of invasion", 8),
            "presence of portal vein thrombosis": ("8.4 Presence of portal vein thrombosis", 8),
            # Section 9: Extrahepatic spread
            "presence of metastatic lymph node": ("9.1 Presence of metastatic lymph node", 9),
            "presence of extrahepatic spread": ("9.2 Presence of extrahepatic spread", 9),
            "presence of metastatic hcc": ("9.3 Presence of metastatic HCC", 9),
            "site of distant metastasis": ("9.4 Site of distant metastasis", 9),
            # Section 10: Comorbidities
            "presence of cirrhosis": ("10.1 Presence of cirrhosis", 10),
            "severe comorbidities": ("10.2. Severe comorbidities", 10),
            # Section 11: Prior treatment received
            "prior treatment received": ("11. Prior treatment received", 11),
            # Section 12: Liver transplant
            "liver transplant": ("12. Liver transplant", 12),
            # Section 13: Pathology findings
            "histological tumour grade": ("13.1 Histological tumour grade", 13),
            "histological tumour type": ("13.2 Histological tumour type", 13),
            "margin status": ("13.3 Margin status", 13),
            "presence of satellitosis": ("13.4 Presence of satellitosis", 13)
        }

        for key, (excel_title, section) in title_mappings.items():
            if key == "age":
                if "1.1 age" in question_lower or '"age of patient"' in question_lower:
                    return excel_title, section
            elif key == "gender":
                if "1.2 gender" in question_lower or '"gender of patient"' in question_lower:
                    return excel_title, section
            else:
                if key in question_lower:
                    return excel_title, section

        return question_text, 0

    # 按照Excel顺序排序结果并分组
    results_by_section = {}
    for result in filtered_results:
        excel_title, section_num = get_excel_title_and_section(result["question"])
        if section_num not in results_by_section:
            results_by_section[section_num] = []
        results_by_section[section_num].append((result, excel_title))

    # 生成问题答案部分，按分组显示
    questions_html = ""
    
    for section_num in sorted(results_by_section.keys()):
        if section_num == 0:  # 未分类的问题
            continue
            
        # 添加分节标题
        section_title = section_headers.get(section_num, f"Section {section_num}")
        questions_html += f'''
        <div class="section-header">
            <h3>{section_title}</h3>
        </div>'''
        
        # 添加该分组的所有问题
        for result, excel_title in results_by_section[section_num]:
            # 处理支持性语句
            quotes_html = ""
            if result.get("quotes"):
                valid_quotes = []
                for quote in result["quotes"]:
                    if quote and quote.strip() and not quote.startswith("Error:"):
                        escaped_quote = quote.replace('"', '&quot;').replace("'", "&#39;")
                        valid_quotes.append(f'<span class="quote-link" data-quote="{escaped_quote}" onclick="highlightQuote(this)">{quote}</span>')
                
                if valid_quotes:
                    quotes_html = '; '.join(valid_quotes)
                else:
                    quotes_html = '<span class="no-quotes">No supporting quotes found</span>'
            else:
                quotes_html = '<span class="no-quotes">No supporting quotes found</span>'

            questions_html += f'''
        <div class="question-item">
            <h4>{excel_title}</h4>
            <div class="answer-group">
                <div class="answer-item">
                    <span class="label">Answer:</span>
                    <span class="value">{result["answer"]}</span>
                </div>
                <div class="answer-item">
                    <span class="label">Confidence:</span>
                    <span class="confidence-score">{result["confidence"]}</span>
                </div>
                <div class="answer-item">
                    <span class="label">Supporting quotes:</span>
                    <span class="value quotes-container">{quotes_html}</span>
                </div>
            </div>
        </div>'''
    
    html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCC Analysis - {filename}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }}
        
        .header {{
            background: #343a40;
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header-title {{ font-size: 18px; font-weight: 600; }}
        .case-id {{ font-size: 14px; color: #adb5bd; }}
        
        .main-container {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            height: calc(100vh - 60px);
        }}
        
        .panel {{
            background: white;
            display: flex;
            flex-direction: column;
        }}

        .left-panel {{ border-right: 2px solid #dee2e6; }}

        .panel-content {{
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }}

        .top-scores {{
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 15px 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }}

        .score-section {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .score-section h4 {{
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            font-weight: bold;
        }}

        .score-details {{
            font-size: 12px;
            line-height: 1.4;
        }}

        .score-details div {{
            margin-bottom: 3px;
        }}

        .score-details strong {{
            color: #495057;
        }}
        
        .original-content {{
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }}
        
        .content-line {{
            display: inline;
            transition: background-color 0.3s ease;
        }}
        
        .content-line.highlighted {{
            background-color: #ffc107 !important;
            color: #000 !important;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }}

        .section-header {{
            margin: 30px 0 20px 0;
            padding: 15px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        
        .section-header h3 {{
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }}

        .question-item {{
            margin-bottom: 100px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }}

        .question-item:last-child {{
            margin-bottom: 400px;
        }}
        
        .question-item h4 {{
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        
        .answer-group {{
            background: white;
            padding: 12px;
            border-radius: 6px;
        }}
        
        .answer-item {{
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
        }}
        
        .answer-item .label {{
            color: #6c757d;
            font-size: 12px;
            font-weight: 600;
            min-width: 120px;
            margin-right: 8px;
        }}
        
        .answer-item .value {{
            color: #495057;
            font-size: 12px;
            flex: 1;
        }}
        
        .confidence-score {{
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 600;
            color: #495057;
            font-size: 11px;
        }}
        
        .quote-link {{
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
            transition: all 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 0 2px;
        }}
        
        .quote-link:hover {{
            background-color: #e3f2fd;
            color: #0056b3;
        }}
        
        .quote-link.active {{
            background-color: #ffc107;
            color: #000;
            font-weight: bold;
        }}
        
        .no-quotes {{
            color: #6c757d;
            font-style: italic;
            font-size: 11px;
        }}
        
        .panel-content::-webkit-scrollbar {{
            width: 12px;
        }}
        .panel-content::-webkit-scrollbar-track {{
            background: #f1f1f1;
            border-radius: 6px;
        }}
        .panel-content::-webkit-scrollbar-thumb {{
            background: #007bff;
            border-radius: 6px;
            border: 2px solid #f1f1f1;
        }}
        .panel-content::-webkit-scrollbar-thumb:hover {{
            background: #0056b3;
        }}

        .panel-content {{
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }}
        
        .right-panel .panel-content {{
            max-height: calc(100vh - 160px);
            overflow-y: auto;
            padding-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">HCC Medical Text Analysis</div>
        <div class="case-id">Case ID: {filename}</div>
    </div>
    
    <div class="main-container">
        <div class="panel left-panel">
            <div class="panel-content">
                <div class="original-content">{formatted_content}</div>
            </div>
        </div>
        
        <div class="panel right-panel">
            {top_scores_html}
            <div class="panel-content">
                {questions_html}
            </div>
        </div>
    </div>
    
    <script>
        let currentHighlighted = null;
        let currentActiveQuote = null;
        
        function highlightQuote(element) {{
            const quote = element.getAttribute('data-quote');
            if (!quote || quote.trim() === '') return;
            
            clearHighlights();
            
            if (currentActiveQuote) {{
                currentActiveQuote.classList.remove('active');
            }}
            currentActiveQuote = element;
            element.classList.add('active');
            
            const originalContent = document.querySelector('.original-content');
            const contentLines = originalContent.querySelectorAll('.content-line');
            
            let bestMatch = null;
            let bestScore = 0;
            
            for (let line of contentLines) {{
                const lineText = line.textContent.toLowerCase();
                const quoteText = quote.toLowerCase();

                if (lineText.includes(quoteText)) {{
                    bestMatch = line;
                    bestScore = 100;
                    break;
                }}

                const cleanQuote = quoteText.replace(/[^\\w\\s]/g, ' ').replace(/\\s+/g, ' ').trim();
                const cleanLine = lineText.replace(/[^\\w\\s]/g, ' ').replace(/\\s+/g, ' ').trim();

                if (cleanLine.includes(cleanQuote)) {{
                    bestMatch = line;
                    bestScore = 95;
                    break;
                }}

                const words = cleanQuote.split(/\\s+/).filter(w => w.length > 2);
                let matchedWords = 0;

                for (let word of words) {{
                    if (cleanLine.includes(word)) {{
                        matchedWords++;
                    }}
                }}

                const score = (matchedWords / words.length) * 100;
                if (score > bestScore && score > 40) {{
                    bestMatch = line;
                    bestScore = score;
                }}
            }}
            
            if (bestMatch) {{
                bestMatch.classList.add('highlighted');
                currentHighlighted = bestMatch;
                
                const leftPanel = document.querySelector('.left-panel .panel-content');
                const lineTop = bestMatch.offsetTop;
                const panelHeight = leftPanel.clientHeight;
                
                leftPanel.scrollTo({{
                    top: Math.max(0, lineTop - panelHeight / 2),
                    behavior: 'smooth'
                }});
            }}
        }}
        
        function clearHighlights() {{
            if (currentHighlighted) {{
                currentHighlighted.classList.remove('highlighted');
                currentHighlighted = null;
            }}
        }}
    </script>
</body>
</html>'''
    
    return html_template

def process_single_file(file_path):
    """处理单个文件"""
    print(f"[INFO] Processing single file: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"[ERROR] File not found: {file_path}")
        return False
        
    file_name = os.path.basename(file_path)
    file_name_without_ext = os.path.splitext(file_name)[0]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            text_content = file.read()
    except Exception as e:
        print(f"[ERROR] Failed to read file: {str(e)}")
        return False
    
    # 分析文本
    analysis_response = analyze_with_gpt4(text_content, file_name_without_ext)
    
    # 生成HTML报告
    html_output_path = f"hcc_analysis_{file_name_without_ext}.html"
    html_content = generate_simple_html(text_content, analysis_response, file_name_without_ext)
    
    try:
        with open(html_output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"[SUCCESS] Report generated: {html_output_path}")
    except Exception as e:
        print(f"[ERROR] Failed to save HTML report: {str(e)}")
        return False
    
    return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='HCC病例分析器')
    parser.add_argument('--file', '-f', help='指定单个文件进行处理')
    parser.add_argument('--directory', '-d', default='txts', help='指定要处理的文件夹路径')
    parser.add_argument('--limit', '-l', type=int, help='限制处理的文件数量')
    parser.add_argument('--start', '-s', type=int, default=0, help='从第几个文件开始处理')
    
    args = parser.parse_args()
    
    if args.file:
        process_single_file(args.file)
        return
    
    txt_files = [f for f in os.listdir(args.directory) if f.endswith('.txt')]
    if not txt_files:
        print(f"[ERROR] No txt files found in directory: {args.directory}")
        return

    print(f"[INFO] Processing all txt files in directory: {args.directory}")
    for i, txt_file in enumerate(txt_files):
        if args.start is not None and i < args.start:
            print(f"跳过文件 {txt_file} (从第 {args.start} 个文件开始)")
            continue
        if args.limit is not None and i >= args.start + args.limit:
            print(f"达到处理数量限制，跳过文件 {txt_file}")
            break
        process_single_file(os.path.join(args.directory, txt_file))

if __name__ == "__main__":
    main()

