#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较格式化前后的MELD和ALBI分数准确性
专门分析这两个关键评分的改进情况
"""

import os
import re
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime
from meld_albi_ground_truth import MELD_ALBI_GROUND_TRUTH, get_ground_truth, get_all_cases

# 目录设置
ORIGINAL_DIR = 'html_reports_20250715'  # 原始报告
FORMATTED_DIR = 'html_reports_20250715_v2'  # 格式化后报告

# 要分析的问题
TARGET_QUESTIONS = ["MELD score", "ALBI score"]

def extract_meld_albi_from_html(html_file):
    """从HTML文件中提取MELD和ALBI分数"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        scores = {}
        
        # 方法1: 从顶部评分区域提取
        top_scores = soup.find('div', class_='top-scores')
        if top_scores:
            score_sections = top_scores.find_all('div', class_='score-section')
            for section in score_sections:
                h4_tag = section.find('h4')
                if h4_tag:
                    title = h4_tag.get_text().strip()
                    score_details = section.find('div', class_='score-details')
                    if score_details:
                        answer_div = score_details.find('div')
                        if answer_div:
                            answer_text = answer_div.get_text().strip()
                            # 提取Answer:后面的内容
                            if "Answer:" in answer_text:
                                answer = answer_text.split("Answer:")[-1].strip()
                                if "MELD" in title:
                                    scores["MELD score"] = answer
                                elif "ALBI" in title:
                                    scores["ALBI score"] = answer
        
        # 方法2: 从问题列表中提取（如果顶部没有找到）
        if not scores:
            question_items = soup.find_all('div', class_='question-item')
            for item in question_items:
                h4_tag = item.find('h4')
                if h4_tag:
                    question_text = h4_tag.get_text().strip()
                    answer_span = item.find('span', class_='value')
                    if answer_span:
                        answer_text = answer_span.get_text().strip()
                        
                        if "MELD" in question_text and "MELD score" not in scores:
                            scores["MELD score"] = answer_text
                        elif "ALBI" in question_text and "ALBI score" not in scores:
                            scores["ALBI score"] = answer_text
        
        return scores
        
    except Exception as e:
        print(f"⚠️ 解析HTML文件失败 {html_file}: {e}")
        return {}

def normalize_score(score_text, score_type):
    """标准化分数格式"""
    if not score_text or score_text.lower() in ['n/a', 'not available', 'not mentioned', '-', '']:
        return "N/A"
    
    score_text = str(score_text).strip()
    
    if score_type == "MELD score":
        # 提取数字
        meld_match = re.search(r'(\d+\.?\d*)', score_text)
        if meld_match:
            return str(int(float(meld_match.group(1))))  # 转换为整数
        return "N/A"
        
    elif score_type == "ALBI score":
        # 提取负数或正数（ALBI通常是负数）
        albi_match = re.search(r'(-?\d+\.?\d*)', score_text)
        if albi_match:
            return str(round(float(albi_match.group(1)), 2))  # 保留2位小数
        return "N/A"
    
    return score_text

def calculate_score_accuracy(predicted, ground_truth, score_type):
    """计算分数准确性"""
    pred_norm = normalize_score(predicted, score_type)
    gt_norm = normalize_score(ground_truth, score_type)
    
    if gt_norm == "N/A" or pred_norm == "N/A":
        return None  # 无法评估
    
    try:
        pred_val = float(pred_norm)
        gt_val = float(gt_norm)
        
        if score_type == "MELD score":
            # MELD分数：绝对误差≤1认为准确，≤3认为部分准确
            error = abs(pred_val - gt_val)
            if error <= 1:
                return 1.0  # 完全准确
            elif error <= 3:
                return 0.5  # 部分准确
            else:
                return 0.0  # 不准确
                
        elif score_type == "ALBI score":
            # ALBI分数：绝对误差≤0.1认为准确，≤0.3认为部分准确
            error = abs(pred_val - gt_val)
            if error <= 0.1:
                return 1.0  # 完全准确
            elif error <= 0.3:
                return 0.5  # 部分准确
            else:
                return 0.0  # 不准确
    except:
        return 0.0
    
    return 0.0

def compare_meld_albi_reports():
    """比较原始报告和格式化后报告的MELD和ALBI准确性"""
    all_cases = get_all_cases()
    
    print(f"📊 开始分析 {len(all_cases)} 个案例的MELD和ALBI分数")
    
    comparison_results = []
    
    for case_id in all_cases:
        print(f"🔍 分析案例: {case_id}")
        
        # 原始报告文件路径
        orig_file = f"hcc_analysis_{case_id}.html"
        orig_path = os.path.join(ORIGINAL_DIR, orig_file)
        
        # 格式化后报告文件路径
        formatted_file = f"hcc_analysis_{case_id}.html"
        formatted_path = os.path.join(FORMATTED_DIR, formatted_file)
        
        # 提取原始报告的分数
        orig_scores = {}
        if os.path.exists(orig_path):
            orig_scores = extract_meld_albi_from_html(orig_path)
        else:
            print(f"⚠️ 原始报告文件不存在: {orig_path}")
        
        # 提取格式化后报告的分数
        formatted_scores = {}
        if os.path.exists(formatted_path):
            formatted_scores = extract_meld_albi_from_html(formatted_path)
        else:
            print(f"⚠️ 格式化报告文件不存在: {formatted_path}")
        
        # 对每个分数类型进行比较
        for score_type in TARGET_QUESTIONS:
            ground_truth = get_ground_truth(case_id, score_type)
            orig_answer = orig_scores.get(score_type, "N/A")
            formatted_answer = formatted_scores.get(score_type, "N/A")
            
            # 计算准确性
            orig_accuracy = calculate_score_accuracy(orig_answer, ground_truth, score_type)
            formatted_accuracy = calculate_score_accuracy(formatted_answer, ground_truth, score_type)
            
            # 计算改进
            improvement = None
            if orig_accuracy is not None and formatted_accuracy is not None:
                improvement = formatted_accuracy - orig_accuracy
            
            comparison_results.append({
                'case_id': case_id,
                'score_type': score_type,
                'ground_truth': ground_truth,
                'original_answer': orig_answer,
                'original_normalized': normalize_score(orig_answer, score_type),
                'original_accuracy': orig_accuracy,
                'formatted_answer': formatted_answer,
                'formatted_normalized': normalize_score(formatted_answer, score_type),
                'formatted_accuracy': formatted_accuracy,
                'improvement': improvement
            })
            
            # 打印每个案例的结果
            print(f"  📋 {score_type}")
            print(f"     标准答案: {ground_truth}")
            print(f"     原始: {orig_answer} -> {normalize_score(orig_answer, score_type)} (准确性: {orig_accuracy})")
            print(f"     格式化: {formatted_answer} -> {normalize_score(formatted_answer, score_type)} (准确性: {formatted_accuracy})")
            if improvement is not None:
                print(f"     改进: {improvement:+.2f}")
            print()
    
    return comparison_results

def generate_meld_albi_report(results):
    """生成MELD和ALBI比较报告"""
    if not results:
        print("❌ 没有比较结果")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 计算总体统计
    print("\n📊 MELD和ALBI分数准确性比较报告")
    print("=" * 80)
    
    for score_type in TARGET_QUESTIONS:
        score_results = df[df['score_type'] == score_type]
        
        if len(score_results) == 0:
            continue
        
        # 统计有效结果
        orig_scores = [r for r in score_results['original_accuracy'] if r is not None]
        formatted_scores = [r for r in score_results['formatted_accuracy'] if r is not None]
        improvements = [r for r in score_results['improvement'] if r is not None]
        
        orig_avg = sum(orig_scores) / len(orig_scores) if orig_scores else 0
        formatted_avg = sum(formatted_scores) / len(formatted_scores) if formatted_scores else 0
        avg_improvement = sum(improvements) / len(improvements) if improvements else 0
        
        # 统计完全准确的案例数
        orig_perfect = sum(1 for s in orig_scores if s == 1.0)
        formatted_perfect = sum(1 for s in formatted_scores if s == 1.0)
        
        # 统计部分准确的案例数
        orig_partial = sum(1 for s in orig_scores if s == 0.5)
        formatted_partial = sum(1 for s in formatted_scores if s == 0.5)
        
        print(f"\n📋 {score_type}")
        print(f"   评估案例数: {len(score_results)}")
        print(f"   原始准确性: {orig_avg:.2%} (完全准确: {orig_perfect}, 部分准确: {orig_partial})")
        print(f"   格式化后准确性: {formatted_avg:.2%} (完全准确: {formatted_perfect}, 部分准确: {formatted_partial})")
        print(f"   平均改进: {avg_improvement:+.2%}")
        
        # 改进案例统计
        improved_cases = sum(1 for i in improvements if i > 0)
        degraded_cases = sum(1 for i in improvements if i < 0)
        unchanged_cases = sum(1 for i in improvements if i == 0)
        
        print(f"   改进案例: {improved_cases} 个")
        print(f"   退化案例: {degraded_cases} 个")
        print(f"   无变化案例: {unchanged_cases} 个")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'meld_albi_accuracy_comparison_{timestamp}.csv'
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n📄 详细结果已保存到: {output_file}")
    
    # 生成总结报告
    summary_report = f"""# MELD和ALBI分数准确性比较报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 比较范围
- 原始报告目录: {ORIGINAL_DIR}
- 格式化报告目录: {FORMATTED_DIR}
- 分析案例数: {len(get_all_cases())}
- 分析指标: MELD score, ALBI score

## 总体统计
"""
    
    for score_type in TARGET_QUESTIONS:
        score_results = df[df['score_type'] == score_type]
        orig_scores = [r for r in score_results['original_accuracy'] if r is not None]
        formatted_scores = [r for r in score_results['formatted_accuracy'] if r is not None]
        improvements = [r for r in score_results['improvement'] if r is not None]
        
        orig_avg = sum(orig_scores) / len(orig_scores) if orig_scores else 0
        formatted_avg = sum(formatted_scores) / len(formatted_scores) if formatted_scores else 0
        avg_improvement = sum(improvements) / len(improvements) if improvements else 0
        
        orig_perfect = sum(1 for s in orig_scores if s == 1.0)
        formatted_perfect = sum(1 for s in formatted_scores if s == 1.0)
        
        improved_cases = sum(1 for i in improvements if i > 0)
        degraded_cases = sum(1 for i in improvements if i < 0)
        
        summary_report += f"""
### {score_type}
- 原始准确性: {orig_avg:.2%} ({orig_perfect}/{len(orig_scores)} 完全准确)
- 格式化后准确性: {formatted_avg:.2%} ({formatted_perfect}/{len(formatted_scores)} 完全准确)
- 平均改进: {avg_improvement:+.2%}
- 改进案例: {improved_cases} 个
- 退化案例: {degraded_cases} 个
"""
    
    summary_file = f'meld_albi_summary_report_{timestamp}.md'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_report)
    
    print(f"📄 总结报告已保存到: {summary_file}")
    
    return df

def main():
    """主函数"""
    print("🔍 开始MELD和ALBI分数准确性比较分析")
    
    # 检查目录
    if not os.path.exists(ORIGINAL_DIR):
        print(f"❌ 原始报告目录不存在: {ORIGINAL_DIR}")
        return
    
    if not os.path.exists(FORMATTED_DIR):
        print(f"❌ 格式化报告目录不存在: {FORMATTED_DIR}")
        return
    
    # 进行比较
    results = compare_meld_albi_reports()
    
    if results:
        df = generate_meld_albi_report(results)
        print("✅ MELD和ALBI分数准确性比较完成")
        return df
    else:
        print("❌ 没有生成比较结果")
        return None

if __name__ == "__main__":
    main() 