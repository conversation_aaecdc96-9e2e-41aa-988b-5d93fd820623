#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MELD和ALBI分数的标准答案数据
"""

# 用户提供的标准答案数据
MELD_ALBI_GROUND_TRUTH = {
    "10056223-DS-5": {
        "MELD score": "13",
        "ALBI score": "-1.37"
    },
    "10151324-DS-18": {
        "MELD score": "9", 
        "ALBI score": "-2.79"
    },
    "10225793-DS-34": {
        "MELD score": "14",
        "ALBI score": "-1.66"
    },
    "10747475-DS-4": {
        "MELD score": "13",
        "ALBI score": "-1.43"
    },
    "10880579-DS-11": {
        "MELD score": "19",
        "ALBI score": "-1.02"
    },
    "10960817-DS-14": {
        "MELD score": "22",
        "ALBI score": "-2.35"
    },
    "11102747-DS-2": {
        "MELD score": "12",
        "ALBI score": "-1.91"
    },
    "11198012-DS-6": {
        "MELD score": "11",
        "ALBI score": "-2.32"
    },
    "11265636-DS-13": {
        "MELD score": "16",
        "ALBI score": "-2.23"
    },
    "11327487-DS-19": {
        "MELD score": "9",
        "ALBI score": "-2.22"
    },
    "11329198-DS-5": {
        "MELD score": "16",
        "ALBI score": "-1.6"
    },
    "11349875-DS-8": {
        "MELD score": "8",
        "ALBI score": "-2.59"
    },
    "11417994-DS-9": {
        "MELD score": "24",
        "ALBI score": "-0.3"
    },
    "11419849-DS-24": {
        "MELD score": "9",
        "ALBI score": "-2.33"
    },
    "11455644-DS-15": {
        "MELD score": "11",
        "ALBI score": "-1.64"
    },
    "11714491-DS-4": {
        "MELD score": "16",
        "ALBI score": "-1.27"
    },
    "11914986-DS-12": {
        "MELD score": "24",
        "ALBI score": "-0.68"
    },
    "11960904-DS-5": {
        "MELD score": "14",
        "ALBI score": "-1.91"
    },
    "12032388-DS-16": {
        "MELD score": "14",
        "ALBI score": "-1.49"
    },
    "12344358-DS-12": {
        "MELD score": "15",
        "ALBI score": "-2.85"
    }
}

def get_ground_truth(case_id, question_type):
    """获取指定案例和问题类型的标准答案"""
    if case_id in MELD_ALBI_GROUND_TRUTH:
        return MELD_ALBI_GROUND_TRUTH[case_id].get(question_type, "N/A")
    return "N/A"

def get_all_cases():
    """获取所有案例ID"""
    return list(MELD_ALBI_GROUND_TRUTH.keys()) 