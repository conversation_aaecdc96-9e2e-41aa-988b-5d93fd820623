#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较格式化前后的HCC分析准确性
专门比较前4个主要问题的准确性变化
"""

import os
import re
import json
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime

# 目录设置
ORIGINAL_DIR = 'html_reports_20250715'  # 原始报告
FORMATTED_DIR = 'html_reports_20250715_v2'  # 格式化后报告
GROUND_TRUTH_FILE = 'ground truth_20 MIMIC_20250714(Sheet2).csv'

# 前4个主要问题（基于CSV中的关键问题）
MAIN_QUESTIONS = [
    "Number of confirmed HCC",  # Question No. 7
    "Largest size of HCC",     # Question No. 8
    "Child-Pugh classification", # Question No. 2
    "BCLC staging"              # Question No. 1
]

def load_ground_truth():
    """加载ground truth数据"""
    try:
        # 尝试不同的编码，跳过第一行（不是真正的列标题）
        for encoding in ['utf-8', 'latin-1', 'cp1252', 'gb2312']:
            try:
                df = pd.read_csv(GROUND_TRUTH_FILE, encoding=encoding, skiprows=1)
                print(f"✅ 使用 {encoding} 编码成功读取文件")
                break
            except UnicodeDecodeError:
                continue
        else:
            raise Exception("无法使用任何编码读取文件")
            
        ground_truth = {}
        
        # 按案例ID分组数据
        for _, row in df.iterrows():
            case_id = str(row['Note ID']) if pd.notna(row['Note ID']) else None
            question = str(row['Question']) if pd.notna(row['Question']) else None
            answer = str(row['Answer to each question']) if pd.notna(row['Answer to each question']) else "N/A"
            
            if not case_id or case_id == 'nan':
                continue
            
            if case_id not in ground_truth:
                ground_truth[case_id] = {}
            
            # 根据问题类型保存答案
            if question and question in MAIN_QUESTIONS:
                ground_truth[case_id][question] = answer
        
        print(f"📊 加载了 {len(ground_truth)} 个案例的ground truth数据")
        return ground_truth
        
    except Exception as e:
        print(f"⚠️ 无法加载ground truth文件: {e}")
        return {}

def extract_answers_from_html(html_file):
    """从HTML文件中提取前4个问题的答案"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        answers = {}
        
        # 查找问题项
        question_items = soup.find_all('div', class_='question-item')
        
        for item in question_items:
            h4_tag = item.find('h4')
            if not h4_tag:
                continue
                
            question_text = h4_tag.get_text().strip()
            
            # 检查是否是我们关注的前4个问题之一
            answer_span = item.find('span', class_='value')
            if answer_span:
                answer_text = answer_span.get_text().strip()
                
                # 标准化问题标题匹配
                if "Number of confirmed HCC" in question_text or "2.1" in question_text:
                    answers["Number of confirmed HCC"] = answer_text
                elif "Largest size of HCC" in question_text or "2.2" in question_text:
                    answers["Largest size of HCC"] = answer_text
                elif "Child-Pugh classification" in question_text or "Child-Pugh" in question_text:
                    answers["Child-Pugh classification"] = answer_text
                elif "BCLC staging" in question_text or "BCLC" in question_text:
                    answers["BCLC staging"] = answer_text
        
        return answers
        
    except Exception as e:
        print(f"⚠️ 解析HTML文件失败 {html_file}: {e}")
        return {}

def normalize_answer(answer, question_type):
    """标准化答案格式以便比较"""
    if not answer or answer.lower() in ['n/a', 'not available', 'not mentioned', '']:
        return "N/A"
    
    answer = str(answer).strip().lower()
    
    if question_type == "Number of confirmed HCC":
        # 提取数字
        numbers = re.findall(r'\d+', answer)
        return numbers[0] if numbers else "N/A"
        
    elif question_type == "Largest size of HCC":
        # 提取尺寸信息
        size_match = re.search(r'(\d+\.?\d*)\s*(cm|mm)', answer)
        if size_match:
            size = float(size_match.group(1))
            unit = size_match.group(2)
            if unit == 'mm':
                size = size / 10  # 转换为cm
            return f"{size:.1f}cm"
        return answer
        
    elif question_type == "Child-Pugh classification":
        # 标准化Child-Pugh分类
        if 'class a' in answer or 'grade a' in answer or answer.strip().upper() == 'A':
            return "A"
        elif 'class b' in answer or 'grade b' in answer or answer.strip().upper() == 'B':
            return "B"
        elif 'class c' in answer or 'grade c' in answer or answer.strip().upper() == 'C':
            return "C"
        return answer
        
    elif question_type == "BCLC staging":
        # 标准化BCLC分期
        if 'stage a' in answer or 'stage early' in answer or answer.strip().upper() == 'A':
            return "A"
        elif 'stage b' in answer or 'intermediate' in answer or answer.strip().upper() == 'B':
            return "B"
        elif 'stage c' in answer or 'advanced' in answer or answer.strip().upper() == 'C':
            return "C"
        elif 'stage d' in answer or 'terminal' in answer or answer.strip().upper() == 'D':
            return "D"
        elif 'stage 0' in answer or 'very early' in answer or answer.strip() == '0':
            return "0"
        return answer
    
    return answer

def calculate_accuracy(predicted, ground_truth, question_type):
    """计算单个答案的准确性"""
    pred_norm = normalize_answer(predicted, question_type)
    gt_norm = normalize_answer(ground_truth, question_type)
    
    if gt_norm == "N/A":
        return None  # 无法评估
    
    if pred_norm == gt_norm:
        return 1.0  # 完全正确
    
    # 对于数值类型，计算相似度
    if question_type in ["Number of confirmed HCC", "Largest size of HCC"]:
        try:
            pred_val = float(re.findall(r'\d+\.?\d*', pred_norm)[0]) if re.findall(r'\d+\.?\d*', pred_norm) else 0
            gt_val = float(re.findall(r'\d+\.?\d*', gt_norm)[0]) if re.findall(r'\d+\.?\d*', gt_norm) else 0
            
            if gt_val == 0:
                return 0.0
            
            error_rate = abs(pred_val - gt_val) / gt_val
            if error_rate <= 0.1:  # 10%误差内认为正确
                return 1.0
            elif error_rate <= 0.2:  # 20%误差内认为部分正确
                return 0.5
            else:
                return 0.0
        except:
            return 0.0
    
    return 0.0  # 不匹配

def compare_reports():
    """比较原始报告和格式化后报告的准确性"""
    ground_truth = load_ground_truth()
    
    if not ground_truth:
        print("❌ 无法加载ground truth，终止比较")
        return
    
    # 获取要比较的案例文件
    original_files = [f for f in os.listdir(ORIGINAL_DIR) if f.endswith('.html')]
    formatted_files = [f for f in os.listdir(FORMATTED_DIR) if f.endswith('.html')] if os.path.exists(FORMATTED_DIR) else []
    
    print(f"📂 原始报告: {len(original_files)} 个文件")
    print(f"📂 格式化报告: {len(formatted_files)} 个文件")
    
    comparison_results = []
    
    for orig_file in original_files:
        # 提取案例ID
        case_id_match = re.search(r'hcc_analysis_(.+)\.html', orig_file)
        if not case_id_match:
            continue
        
        case_id = case_id_match.group(1)
        formatted_file = f"hcc_analysis_{case_id}.html"
        
        if case_id not in ground_truth:
            print(f"⚠️ 案例 {case_id} 没有ground truth数据")
            continue
        
        # 提取原始报告答案
        orig_answers = extract_answers_from_html(os.path.join(ORIGINAL_DIR, orig_file))
        
        # 提取格式化后报告答案（如果存在）
        formatted_answers = {}
        if formatted_file in formatted_files:
            formatted_answers = extract_answers_from_html(os.path.join(FORMATTED_DIR, formatted_file))
        
        # 计算准确性
        gt_data = ground_truth[case_id]
        
        for question in MAIN_QUESTIONS:
            q_key = question  # 直接使用问题作为键
            
            orig_answer = orig_answers.get(q_key, "N/A")
            formatted_answer = formatted_answers.get(q_key, "N/A")
            ground_truth_answer = gt_data.get(q_key, "N/A")
            
            orig_accuracy = calculate_accuracy(orig_answer, ground_truth_answer, q_key)
            formatted_accuracy = calculate_accuracy(formatted_answer, ground_truth_answer, q_key) if formatted_answers else None
            
            comparison_results.append({
                'case_id': case_id,
                'question': question,
                'ground_truth': ground_truth_answer,
                'original_answer': orig_answer,
                'original_accuracy': orig_accuracy,
                'formatted_answer': formatted_answer,
                'formatted_accuracy': formatted_accuracy,
                'improvement': (formatted_accuracy - orig_accuracy) if (orig_accuracy is not None and formatted_accuracy is not None) else None
            })
    
    return comparison_results

def generate_comparison_report(results):
    """生成比较报告"""
    if not results:
        print("❌ 没有比较结果")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 计算总体统计
    print("\n📊 准确性比较报告")
    print("=" * 60)
    
    for question in MAIN_QUESTIONS:
        question_results = df[df['question'] == question]
        
        if len(question_results) == 0:
            continue
        
        orig_scores = [r for r in question_results['original_accuracy'] if r is not None]
        formatted_scores = [r for r in question_results['formatted_accuracy'] if r is not None]
        
        orig_avg = sum(orig_scores) / len(orig_scores) if orig_scores else 0
        formatted_avg = sum(formatted_scores) / len(formatted_scores) if formatted_scores else 0
        improvement = formatted_avg - orig_avg if formatted_scores else 0
        
        print(f"\n📋 {question}")
        print(f"   原始准确性: {orig_avg:.2%} ({len(orig_scores)} 个案例)")
        print(f"   格式化后准确性: {formatted_avg:.2%} ({len(formatted_scores)} 个案例)")
        print(f"   改进: {improvement:+.2%}")
    
    # 保存详细结果
    output_file = f'accuracy_comparison_formatting_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n📄 详细结果已保存到: {output_file}")
    
    # 生成总结报告
    summary_report = f"""# 格式化前后准确性比较报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 比较范围
- 原始报告目录: {ORIGINAL_DIR}
- 格式化报告目录: {FORMATTED_DIR}
- 比较问题: 前4个主要问题

## 总体统计
"""
    
    total_cases = len(df['case_id'].unique())
    summary_report += f"- 比较案例数: {total_cases}\n"
    summary_report += f"- 比较问题数: {len(MAIN_QUESTIONS)}\n\n"
    
    for question in MAIN_QUESTIONS:
        question_results = df[df['question'] == question]
        orig_scores = [r for r in question_results['original_accuracy'] if r is not None]
        formatted_scores = [r for r in question_results['formatted_accuracy'] if r is not None]
        
        orig_avg = sum(orig_scores) / len(orig_scores) if orig_scores else 0
        formatted_avg = sum(formatted_scores) / len(formatted_scores) if formatted_scores else 0
        improvement = formatted_avg - orig_avg if formatted_scores else 0
        
        summary_report += f"### {question}\n"
        summary_report += f"- 原始准确性: {orig_avg:.2%}\n"
        summary_report += f"- 格式化后准确性: {formatted_avg:.2%}\n" 
        summary_report += f"- 改进: {improvement:+.2%}\n\n"
    
    summary_file = f'accuracy_comparison_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.md'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_report)
    
    print(f"📄 总结报告已保存到: {summary_file}")

def main():
    """主函数"""
    print("🔍 开始比较格式化前后的准确性")
    
    # 检查目录
    if not os.path.exists(ORIGINAL_DIR):
        print(f"❌ 原始报告目录不存在: {ORIGINAL_DIR}")
        return
    
    if not os.path.exists(FORMATTED_DIR):
        print(f"⚠️ 格式化报告目录不存在: {FORMATTED_DIR}")
        print("📋 将只分析原始报告的准确性")
    
    # 进行比较
    results = compare_reports()
    
    if results:
        generate_comparison_report(results)
        print("✅ 准确性比较完成")
    else:
        print("❌ 没有生成比较结果")

if __name__ == "__main__":
    main() 