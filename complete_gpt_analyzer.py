#!/usr/bin/env python3
"""
Complete GPT-based Medical Text Analyzer for HCC Cases
Analyzes medical text and answers all structured questions with confidence scores and supporting quotes
"""

import json
import re
import pandas as pd
from typing import Dict, List, Tuple, Optional

class CompleteHCCAnalyzer:
    def __init__(self, text_file_path: str, excel_file_path: str):
        """
        Initialize the complete HCC analyzer
        """
        self.text_file_path = text_file_path
        self.excel_file_path = excel_file_path
        self.medical_text = self._load_medical_text()
        self.questions = self._load_questions()
        
    def _load_medical_text(self) -> str:
        """Load the medical text from file"""
        try:
            with open(self.text_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error loading medical text: {e}")
            return ""
    
    def _load_questions(self) -> List[Dict]:
        """Load questions from Excel file"""
        try:
            df = pd.read_excel(self.excel_file_path)
            questions = []
            
            for index, row in df.iterrows():
                if pd.notna(row.iloc[2]):  # Column C (Questions)
                    question_data = {
                        'id': index + 1,
                        'structure': row.iloc[1] if pd.notna(row.iloc[1]) else "",  # Column B
                        'question': row.iloc[2],  # Column C
                        'remarks': row.iloc[3] if pd.notna(row.iloc[3]) else ""  # Column D
                    }
                    questions.append(question_data)
            
            return questions
        except Exception as e:
            print(f"Error loading questions: {e}")
            return []
    
    def find_exact_supporting_quotes(self, answer: str, question: str) -> List[str]:
        """
        Find exact supporting quotes from the original text
        """
        quotes = []
        text_lines = self.medical_text.split('\n')
        
        # Define search patterns based on question type
        search_patterns = self._get_search_patterns(answer, question)
        
        for pattern in search_patterns:
            for line in text_lines:
                line = line.strip()
                if not line:
                    continue
                    
                if re.search(pattern, line, re.IGNORECASE):
                    if line not in quotes:
                        quotes.append(line)
        
        return quotes[:3]  # Return top 3 most relevant quotes
    
    def _get_search_patterns(self, answer: str, question: str) -> List[str]:
        """Get search patterns based on question type"""
        patterns = []
        question_lower = question.lower()
        
        if 'gender' in question_lower or 'sex' in question_lower:
            patterns.append(r'sex:\s*[mf]')
        
        if 'age' in question_lower:
            patterns.extend([r'\d+\s*years?\s*old', r'age:\s*\d+'])
        
        if 'number' in question_lower and 'hcc' in question_lower:
            patterns.extend([
                r'two\s+arterially[- ]enhancing\s+lesions',
                r'single\s+lesion',
                r'multiple\s+lesions',
                r'\d+\s+lesions?'
            ])
        
        if 'size' in question_lower:
            patterns.extend([
                r'\d+\.?\d*\s*cm',
                r'measures\s+up\s+to\s+\d+\.?\d*\s*cm',
                r'larger\s+measures'
            ])
        
        if 'bilirubin' in question_lower:
            patterns.append(r'total\s+bilirubin:\s*\d+\.?\d*')
        
        if 'albumin' in question_lower:
            patterns.append(r'albumin:\s*\d+\.?\d*')
        
        if 'sodium' in question_lower:
            patterns.append(r'sodium:\s*\d+')
        
        if 'creatinine' in question_lower:
            patterns.append(r'creatinine:\s*\d+\.?\d*')
        
        if 'inr' in question_lower or 'international normalized ratio' in question_lower:
            patterns.append(r'inr:\s*\d+\.?\d*')
        
        if 'cirrhosis' in question_lower:
            patterns.extend([r'cirrhosis', r'hepatitis\s+c\s+cirrhosis'])
        
        if 'activity' in question_lower:
            patterns.extend([r'ambulatory', r'independent', r'activity'])
        
        if 'mental' in question_lower:
            patterns.extend([r'clear\s+and\s+coherent', r'mental\s+status'])
        
        if 'consciousness' in question_lower:
            patterns.extend([r'alert\s+and\s+interactive', r'level\s+of\s+consciousness'])
        
        if 'ascites' in question_lower:
            patterns.extend([r'ascites', r'portal\s+hypertension'])
        
        if 'treatment' in question_lower:
            patterns.extend([r'rfa', r'tace', r'radiofrequency', r'chemoembolization'])
        
        return patterns
    
    def analyze_comprehensive_question(self, question_data: Dict) -> Dict:
        """
        Comprehensive analysis of a single question
        """
        question = question_data['question']
        question_lower = question.lower()
        
        # Generate answer based on comprehensive text analysis
        answer = self._generate_comprehensive_answer(question, question_data.get('remarks', ''))
        
        # Find supporting quotes
        supporting_quotes = self.find_exact_supporting_quotes(answer, question)
        
        # Calculate confidence
        confidence = self._calculate_comprehensive_confidence(answer, question, supporting_quotes)
        
        return {
            'question_id': question_data['id'],
            'structure': question_data['structure'],
            'question': question,
            'answer': answer,
            'confidence': confidence,
            'supporting_quotes': supporting_quotes,
            'remarks': question_data.get('remarks', '')
        }
    
    def _generate_comprehensive_answer(self, question: str, remarks: str) -> str:
        """
        Generate comprehensive answer based on medical text analysis
        """
        question_lower = question.lower()
        
        # Demographics
        if 'age' in question_lower:
            age_patterns = [
                r'(\d+)\s*years?\s*old',
                r'age:\s*(\d+)',
                r'(\d+)[-\s]*year[-\s]*old'
            ]
            for pattern in age_patterns:
                match = re.search(pattern, self.medical_text, re.IGNORECASE)
                if match:
                    return match.group(1)
            return "Not explicitly mentioned"
        
        if 'gender' in question_lower or 'sex' in question_lower:
            if re.search(r'sex:\s*m', self.medical_text, re.IGNORECASE):
                return "Male"
            elif re.search(r'sex:\s*f', self.medical_text, re.IGNORECASE):
                return "Female"
            return "Not specified"
        
        # Tumor characteristics
        if 'number' in question_lower and 'hcc' in question_lower:
            if re.search(r'two\s+arterially[- ]enhancing\s+lesions', self.medical_text, re.IGNORECASE):
                return "2"
            elif re.search(r'single\s+lesion', self.medical_text, re.IGNORECASE):
                return "1"
            elif re.search(r'multiple\s+lesions', self.medical_text, re.IGNORECASE):
                return "Multiple"
            return "Not clearly specified"
        
        if 'size' in question_lower and ('hcc' in question_lower or 'lesion' in question_lower):
            size_match = re.search(r'measures\s+up\s+to\s+(\d+\.?\d*)\s*cm', self.medical_text, re.IGNORECASE)
            if size_match:
                return f"{size_match.group(1)} cm"
            size_match = re.search(r'(\d+\.?\d*)\s*cm', self.medical_text)
            if size_match:
                return f"{size_match.group(1)} cm"
            return "Not specified"
        
        # Child-Pugh and performance status
        if 'child-pugh' in question_lower:
            if re.search(r'child[- ]pugh\s+[abc]', self.medical_text, re.IGNORECASE):
                match = re.search(r'child[- ]pugh\s+([abc])', self.medical_text, re.IGNORECASE)
                return f"Child-Pugh {match.group(1).upper()}"
            return "Not specified"
        
        if 'ecog' in question_lower:
            ecog_match = re.search(r'ecog\s+(\d+)', self.medical_text, re.IGNORECASE)
            if ecog_match:
                return f"ECOG {ecog_match.group(1)}"
            return "Not explicitly mentioned"
        
        if 'activity status' in question_lower:
            if re.search(r'ambulatory[- ]independent', self.medical_text, re.IGNORECASE):
                return "Ambulatory - Independent"
            return "Not specified"
        
        # Mental status and consciousness
        if 'mental status' in question_lower:
            if re.search(r'clear\s+and\s+coherent', self.medical_text, re.IGNORECASE):
                return "Clear and coherent"
            return "Not specified"
        
        if 'consciousness' in question_lower:
            if re.search(r'alert\s+and\s+interactive', self.medical_text, re.IGNORECASE):
                return "Alert and interactive"
            return "Not specified"
        
        # Lab values
        if 'bilirubin' in question_lower:
            bilirubin_match = re.search(r'total\s+bilirubin:\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if bilirubin_match:
                return f"{bilirubin_match.group(1)} mg/dL"
            return "Not reported"
        
        if 'albumin' in question_lower:
            albumin_match = re.search(r'albumin:\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if albumin_match:
                return f"{albumin_match.group(1)} g/dL"
            return "Not reported"
        
        if 'sodium' in question_lower:
            sodium_match = re.search(r'sodium:\s*(\d+)', self.medical_text, re.IGNORECASE)
            if sodium_match:
                return f"{sodium_match.group(1)} mEq/L"
            return "Not reported"
        
        if 'creatinine' in question_lower:
            creatinine_match = re.search(r'creatinine:\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if creatinine_match:
                return f"{creatinine_match.group(1)} mg/dL"
            return "Not reported"
        
        if 'inr' in question_lower or 'international normalized ratio' in question_lower:
            inr_match = re.search(r'inr:\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if inr_match:
                return inr_match.group(1)
            return "Not reported"
        
        # Clinical conditions
        if 'cirrhosis' in question_lower:
            if re.search(r'cirrhosis', self.medical_text, re.IGNORECASE):
                return "Yes"
            return "No evidence mentioned"
        
        if 'ascites' in question_lower:
            if re.search(r'ascites', self.medical_text, re.IGNORECASE):
                return "Present"
            return "Not mentioned"
        
        # Treatment history
        if 'treatment' in question_lower or 'prior' in question_lower:
            treatments = []
            if re.search(r'rfa|radiofrequency\s+ablation', self.medical_text, re.IGNORECASE):
                treatments.append("RFA (Radiofrequency Ablation)")
            if re.search(r'tace|transarterial\s+chemoembolization', self.medical_text, re.IGNORECASE):
                treatments.append("TACE (Transarterial Chemoembolization)")
            if treatments:
                return ", ".join(treatments)
            return "Not specified"
        
        if 'transplant' in question_lower:
            if re.search(r'transplant', self.medical_text, re.IGNORECASE):
                return "Yes"
            return "No"
        
        # Default response
        return "Information not available in the provided text"
    
    def _calculate_comprehensive_confidence(self, answer: str, question: str, supporting_quotes: List[str]) -> int:
        """
        Calculate comprehensive confidence score
        """
        confidence = 50  # Base confidence
        
        # Increase confidence based on supporting quotes
        if supporting_quotes:
            confidence += min(len(supporting_quotes) * 20, 40)
        
        # Increase confidence for specific, measurable answers
        if re.search(r'\d+\.?\d*\s*(cm|mg/dL|mEq/L|g/dL)', answer):
            confidence += 25
        
        # Increase confidence for clear categorical answers
        categorical_answers = ['male', 'female', 'yes', 'no', 'present', 'absent']
        if any(cat in answer.lower() for cat in categorical_answers):
            confidence += 20
        
        # Decrease confidence for uncertain language
        uncertain_terms = ['not mentioned', 'not explicitly', 'unclear', 'possibly', 'not available']
        if any(term in answer.lower() for term in uncertain_terms):
            confidence -= 25
        
        return max(10, min(100, confidence))
    
    def analyze_all_questions_comprehensive(self) -> List[Dict]:
        """Analyze all questions comprehensively"""
        results = []
        
        for question_data in self.questions:
            # Skip first 4 questions as requested
            if question_data['id'] <= 4:
                continue
                
            result = self.analyze_comprehensive_question(question_data)
            results.append(result)
        
        return results

if __name__ == "__main__":
    # Example usage
    analyzer = CompleteHCCAnalyzer("txts/10056223-DS-5.txt", "HCC cross-referencing platform structured questions_20250704.xlsx")
    results = analyzer.analyze_all_questions_comprehensive()
    
    print("Comprehensive Analysis Results:")
    for result in results:
        print(f"Q{result['question_id']}: {result['question']}")
        print(f"A: {result['answer']} (Confidence: {result['confidence']}%)")
        print(f"Supporting quotes: {len(result['supporting_quotes'])}")
        if result['supporting_quotes']:
            for quote in result['supporting_quotes']:
                print(f"  - {quote}")
        print("-" * 80)
