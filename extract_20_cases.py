import pandas as pd
import os
from datetime import datetime

# 定义要提取的20个case ID
target_cases = [
    "10056223-DS-5", "10151324-DS-18", "10225793-DS-34", "10747475-DS-4", 
    "10880579-DS-11", "10960817-DS-14", "11102747-DS-2", "11198012-DS-6", 
    "11265636-DS-13", "11327487-DS-19", "11329198-DS-5", "11349875-DS-8", 
    "11417994-DS-9", "11419849-DS-24", "11455644-DS-15", "11714491-DS-4", 
    "11914986-DS-12", "11960904-DS-5", "12032388-DS-16", "12344358-DS-12"
]

# 创建以今天日期命名的文件夹
today = datetime.now().strftime("%Y%m%d")
folder_name = f"extracted_cases_{today}"
os.makedirs(folder_name, exist_ok=True)
print(f"已创建文件夹: {folder_name}")

# 读取CSV文件
csv_file = "MIMIC_HCC_GroundTruth(MIMIC_HCC_GroundTruth).csv"
print("正在读取CSV文件...")

try:
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    print(f"CSV文件包含 {len(df)} 行数据")
    print(f"列名: {list(df.columns)}")
    
    # 查找匹配的case
    found_cases = []
    
    # 检查可能包含case ID的列
    possible_columns = [col for col in df.columns if 'case' in col.lower() or 'id' in col.lower() or 'hadm' in col.lower()]
    print(f"可能包含case ID的列: {possible_columns}")
    
    # 遍历每一行，查找匹配的case
    for index, row in df.iterrows():
        # 将行数据转换为字符串进行搜索
        row_str = ' '.join(str(val) for val in row.values if pd.notna(val))
        
        for case_id in target_cases:
            # 提取case的主要ID部分（去掉-DS-X后缀）
            main_id = case_id.split('-')[0]
            
            if main_id in row_str or case_id in row_str:
                found_cases.append((case_id, index, row))
                print(f"找到匹配的case: {case_id} (行 {index})")
                
                # 将这一行的数据保存为txt文件
                output_file = os.path.join(folder_name, f"{case_id}.txt")
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"Case ID: {case_id}\n")
                    f.write(f"Row Index: {index}\n")
                    f.write("="*50 + "\n")
                    
                    # 写入所有列的数据
                    for col, val in row.items():
                        if pd.notna(val):
                            f.write(f"{col}: {val}\n")
                    
                    # 如果有text列，单独显示
                    if 'text' in df.columns and pd.notna(row['text']):
                        f.write("\n" + "="*50 + "\n")
                        f.write("MEDICAL TEXT:\n")
                        f.write("="*50 + "\n")
                        f.write(str(row['text']))
                
                break  # 找到匹配后跳出内层循环
    
    print(f"\n总共找到 {len(found_cases)} 个匹配的case")
    print(f"找到的case: {[case[0] for case in found_cases]}")
    
    # 检查哪些case没有找到
    found_case_ids = [case[0] for case in found_cases]
    missing_cases = [case for case in target_cases if case not in found_case_ids]
    
    if missing_cases:
        print(f"\n未找到的case: {missing_cases}")
        
        # 保存未找到的case列表
        with open(os.path.join(folder_name, "missing_cases.txt"), 'w', encoding='utf-8') as f:
            f.write("未找到的Case ID:\n")
            for case in missing_cases:
                f.write(f"{case}\n")
    
    print(f"\n所有数据已保存到文件夹: {folder_name}")

except Exception as e:
    print(f"处理过程中出现错误: {e}")
    import traceback
    traceback.print_exc() 