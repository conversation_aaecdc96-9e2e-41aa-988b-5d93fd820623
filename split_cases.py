#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def split_batch_analysis():
    """分割batch_analysis_results.txt文件为独立的案例文件"""
    
    # 读取原始文件
    with open('batch_analysis_results.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式分割案例
    # 匹配 "--- ANALYSIS FOR: CASE_NAME ---" 模式
    case_pattern = r'--- ANALYSIS FOR: ([^-]+) ---\n(.*?)(?=--- ANALYSIS FOR:|$)'
    matches = re.findall(case_pattern, content, re.DOTALL)
    
    # 创建输出目录
    output_dir = 'split_cases'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"找到 {len(matches)} 个案例")
    
    # 为每个案例创建独立文件
    for i, (case_name, case_content) in enumerate(matches, 1):
        # 清理案例名称（移除空格和特殊字符）
        clean_case_name = case_name.strip()
        filename = f"{clean_case_name}.txt"
        filepath = os.path.join(output_dir, filename)
        
        # 写入案例内容
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"--- ANALYSIS FOR: {clean_case_name} ---\n")
            f.write(case_content.strip())
        
        print(f"已创建: {filename}")
    
    print(f"\n所有案例文件已保存到 '{output_dir}' 目录")

if __name__ == "__main__":
    split_batch_analysis() 