#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取Excel文件
df = pd.read_excel('HCC cross-referencing platform structured questions_20250704.xlsx')

print("列名:", df.columns.tolist())
print("总行数:", len(df))
print("\n前20行详细内容:")

for i in range(min(20, len(df))):
    row = df.iloc[i]
    print(f"行{i}: {row.tolist()}")

print("\n显示所有问题的结构和序号:")
for i, row in df.iterrows():
    structure = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""
    question = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""
    if structure.strip() or question.strip():
        print(f"行{i}: 结构='{structure}' | 问题='{question}'")
