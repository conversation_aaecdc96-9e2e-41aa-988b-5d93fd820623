#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗案例文本格式化器
使用Azure GPT-4.1改善医疗文本的可读性，严格保证不修改任何内容，只改格式
"""

import os
import time
import requests
import json
from datetime import datetime

# Azure OpenAI API配置 (来自hcc_analyzer_simple.py)
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# 构建Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# 输入和输出文件夹
INPUT_DIR = 'extracted_cases_20250715'
OUTPUT_DIR = 'formatted_cases_20250715'

def create_output_directory():
    """创建输出目录"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"✅ 已创建输出目录: {OUTPUT_DIR}")
    else:
        print(f"📁 输出目录已存在: {OUTPUT_DIR}")

def format_medical_text_with_gpt4(content, filename):
    """使用GPT-4.1格式化医疗文本，严格保证不修改内容"""
    
    # 估算内容长度，确保在GPT-4.1的限制范围内
    content_length = len(content)
    max_content_length = 150000  # 约60K tokens的安全范围
    
    if content_length > max_content_length:
        print(f"⚠️ 文件 {filename} 内容过长 ({content_length} 字符)，截取前 {max_content_length} 字符")
        content = content[:max_content_length] + "...[内容已截取]"
    
    prompt = f"""
你是一个专业的医疗文档格式化专家。请对以下医疗案例文本进行格式化处理，提高其可读性。

🚨 **严格要求**：
1. **绝对不能修改、增加或删除任何原始内容**
2. **所有原始文字、数字、符号必须完全保留**
3. **只能调整格式：换行、缩进、段落分隔、空白处理**
4. **不能纠正任何拼写错误或语法错误**
5. **不能添加任何标点符号或删除任何标点符号**
6. **保持所有原始的空格、下划线、特殊符号**

**格式化目标**：
- 改善段落结构，让不同部分更清晰
- 合理的换行和缩进
- 保持医疗记录的专业格式
- 让长段落更易读，但不改变任何词汇

**输出要求**：
- 直接输出格式化后的文本
- 不要添加任何说明或注释
- 确保所有原始内容100%保留

**原始医疗文本**：
{content}
"""

    headers = {
        "api-key": AZURE_OPENAI_API_KEY,
        "Content-Type": "application/json"
    }
    
    data = {
        "messages": [
            {"role": "system", "content": "你是一个专业的医疗文档格式化专家，专门负责改善文本可读性而不修改任何内容。"},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 16000,
        "temperature": 0.0  # 使用0温度确保一致性
    }
    
    # 重试机制
    max_retries = 5
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄 重试 {attempt}/{max_retries-1}...")
                time.sleep(10 * attempt)
            
            print(f"🤖 正在格式化 {filename}...", end="", flush=True)
            
            response = requests.post(API_URL, headers=headers, json=data, timeout=300)
            
            if response.status_code != 200:
                print(f"❌ HTTP {response.status_code}")
                if attempt == max_retries - 1:
                    print(f"详细错误: {response.text[:200]}")
                    return content  # 返回原始内容
                continue
            
            result = response.json()
            
            if 'error' in result:
                error_msg = result['error'].get('message', 'Unknown error')
                print(f"❌ API错误: {error_msg[:100]}")
                if attempt == max_retries - 1:
                    return content  # 返回原始内容
                continue
            
            if 'choices' not in result or len(result['choices']) == 0:
                print("❌ 无响应内容")
                if attempt == max_retries - 1:
                    return content  # 返回原始内容
                continue
            
            formatted_content = result['choices'][0]['message']['content'].strip()
            
            if not formatted_content:
                print("❌ 空响应")
                if attempt == max_retries - 1:
                    return content  # 返回原始内容
                continue
            
            print("✅ 格式化完成")
            return formatted_content
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求错误: {str(e)[:100]}")
            if attempt == max_retries - 1:
                return content  # 返回原始内容
            continue
        except Exception as e:
            print(f"❌ 其他错误: {str(e)[:100]}")
            if attempt == max_retries - 1:
                return content  # 返回原始内容
            continue
    
    return content  # 如果所有重试都失败，返回原始内容

def process_single_file(file_path, filename):
    """处理单个文件"""
    try:
        # 读取原始文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用GPT-4.1格式化
        formatted_content = format_medical_text_with_gpt4(content, filename)
        
        # 保存格式化后的内容
        output_path = os.path.join(OUTPUT_DIR, filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(formatted_content)
        
        print(f"✅ 已保存格式化文件: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 处理文件 {filename} 时出错: {str(e)}")
        return False

def create_processing_report(processed_files, failed_files):
    """创建处理报告"""
    report_content = f"""# 医疗案例格式化处理报告

## 处理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 处理统计
- 总文件数: {len(processed_files) + len(failed_files)}
- 成功处理: {len(processed_files)}
- 处理失败: {len(failed_files)}

## 成功处理的文件
"""
    
    for i, filename in enumerate(processed_files, 1):
        report_content += f"{i}. {filename}\n"
    
    if failed_files:
        report_content += f"\n## 处理失败的文件\n"
        for i, filename in enumerate(failed_files, 1):
            report_content += f"{i}. {filename}\n"
    
    report_content += f"""
## 输出位置
- 格式化文件保存在: {OUTPUT_DIR}/
- 格式化目标: 提高可读性，保持所有原始内容不变

## 说明
所有文件都经过GPT-4.1处理，严格确保：
1. 不修改任何原始内容
2. 不增加任何内容
3. 不删除任何内容
4. 只改善格式、缩进、换行、段落结构
"""
    
    report_path = os.path.join(OUTPUT_DIR, 'formatting_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 处理报告已保存: {report_path}")

def main():
    """主函数"""
    print("🚀 开始医疗案例格式化处理")
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📂 输出目录: {OUTPUT_DIR}")
    
    # 创建输出目录
    create_output_directory()
    
    # 获取所有txt文件
    if not os.path.exists(INPUT_DIR):
        print(f"❌ 输入目录不存在: {INPUT_DIR}")
        return
    
    txt_files = [f for f in os.listdir(INPUT_DIR) if f.endswith('.txt')]
    if not txt_files:
        print(f"❌ 在 {INPUT_DIR} 中没有找到txt文件")
        return
    
    print(f"📄 找到 {len(txt_files)} 个文件需要处理")
    
    # 处理每个文件
    processed_files = []
    failed_files = []
    
    for i, filename in enumerate(txt_files, 1):
        print(f"\n📝 处理文件 {i}/{len(txt_files)}: {filename}")
        file_path = os.path.join(INPUT_DIR, filename)
        
        if process_single_file(file_path, filename):
            processed_files.append(filename)
        else:
            failed_files.append(filename)
        
        # 添加延迟避免API限制
        if i < len(txt_files):
            print("⏳ 等待5秒避免API限制...")
            time.sleep(5)
    
    # 生成处理报告
    print(f"\n📊 处理完成!")
    print(f"✅ 成功: {len(processed_files)} 个文件")
    print(f"❌ 失败: {len(failed_files)} 个文件")
    
    create_processing_report(processed_files, failed_files)
    print(f"🎉 所有文件处理完成，格式化结果保存在 {OUTPUT_DIR}/ 目录中")

if __name__ == "__main__":
    main() 