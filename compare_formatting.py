#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化效果对比脚本
展示原文件和格式化后文件的差异
"""

def compare_files(original_file, formatted_file, lines_to_show=30):
    """
    对比原文件和格式化后文件的指定行数
    """
    print("=" * 80)
    print("格式化效果对比")
    print("=" * 80)
    
    try:
        # 读取原文件
        with open(original_file, 'r', encoding='utf-8') as f:
            original_lines = f.readlines()
        
        # 读取格式化文件
        with open(formatted_file, 'r', encoding='utf-8') as f:
            formatted_lines = f.readlines()
        
        print(f"\n📄 文件: {original_file}")
        print(f"📊 原文件行数: {len(original_lines)}")
        print(f"📊 格式化后行数: {len(formatted_lines)}")
        print(f"📈 增加行数: {len(formatted_lines) - len(original_lines)}")
        
        print(f"\n📖 对比前 {lines_to_show} 行内容:")
        print("-" * 40 + " 原文件 " + "-" * 40)
        for i, line in enumerate(original_lines[:lines_to_show], 1):
            print(f"{i:3d}: {line.rstrip()}")
        
        print("\n" + "-" * 38 + " 格式化后 " + "-" * 38)
        for i, line in enumerate(formatted_lines[:lines_to_show], 1):
            print(f"{i:3d}: {line.rstrip()}")
            
        # 统计改进效果
        print(f"\n📈 格式化改进统计:")
        long_lines_original = sum(1 for line in original_lines if len(line.strip()) > 100)
        long_lines_formatted = sum(1 for line in formatted_lines if len(line.strip()) > 100)
        
        print(f"  • 原文件超长行数(>100字符): {long_lines_original}")
        print(f"  • 格式化后超长行数: {long_lines_formatted}")
        print(f"  • 减少超长行: {long_lines_original - long_lines_formatted}")
        
        empty_lines_original = sum(1 for line in original_lines if not line.strip())
        empty_lines_formatted = sum(1 for line in formatted_lines if not line.strip())
        
        print(f"  • 原文件空行数: {empty_lines_original}")
        print(f"  • 格式化后空行数: {empty_lines_formatted}")
        print(f"  • 增加分隔空行: {empty_lines_formatted - empty_lines_original}")
        
    except Exception as e:
        print(f"❌ 对比失败: {str(e)}")

if __name__ == "__main__":
    # 对比第一个文件
    original = "extracted_cases_20250715/10056223-DS-5.txt"
    formatted = "formatted_cases_20250715/10056223-DS-5.txt"
    
    compare_files(original, formatted, 50)
    
    print("\n" + "=" * 80)
    print("✅ 格式化处理完成！")
    print("📁 所有格式化文件位于: formatted_cases_20250715/")
    print("📋 处理报告位于: formatted_cases_20250715/formatting_report.txt")
    print("=" * 80) 