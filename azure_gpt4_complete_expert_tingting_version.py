#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure GPT-4.1 Thyroid cancer pathology report analyzer
including 8th edition of AJCC TNM staging system and 2015 ATA risk categories
- Left: pathology report | Right: analysis results
"""

import os
import glob
import json
import time
import requests
import pandas as pd
from datetime import datetime
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Azure OpenAI API settings
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# Excel directory
EXCEL_FILE_PATH = "TCGA-THCA_GPT4.1_20250609.xlsx"

def load_age_data():
    """load age data from Excel"""
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        # mapping TCGA ID and age
        age_dict = {}
        if 'case_submitter_id' in df.columns and 'age_at_index' in df.columns:
            for _, row in df.iterrows():
                tcga_id = row['case_submitter_id']
                age = row['age_at_index']
                if pd.notna(tcga_id) and pd.notna(age):
                    age_dict[tcga_id] = int(age)
        print(f"📊 loaded {len(age_dict)} cases' age data")
        return age_dict
    except Exception as e:
        print(f"⚠️ fail to load age data: {e}")
        return {}

def get_age_from_excel(filename, age_dict):
    """obtain age data from Excel"""
    # 从文件名提取TCGA ID (去掉.txt扩展名)
    tcga_id = os.path.splitext(filename)[0]
    return age_dict.get(tcga_id, None)

def setup_session_with_retry():
    """创建带有重试机制的requests session"""
    session = requests.Session()
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    # 配置重试策略 - 更宽松的设置
    retry_strategy = Retry(
        total=5,  # 增加重试次数
        status_forcelist=[429, 500, 502, 503, 504, 522, 524],
        allowed_methods=["HEAD", "GET", "POST"],  # 新版本使用allowed_methods
        backoff_factor=2,  # 指数退避
        raise_on_status=False
    )
    
    # 配置适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=10
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 设置更长的超时时间 (连接超时, 读取超时)
    session.timeout = (60, 180)
    
    # 设置User-Agent和其他headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Connection': 'keep-alive',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate'
    })
    
    # 尝试使用系统代理
    try:
        proxies = requests.utils.getproxies()
        if proxies:
            session.proxies.update(proxies)
    except:
        pass
    
    return session

def test_network_connectivity():
    """测试网络连接性"""
    print("🔍 Network connection testing...")
    
    # 创建session用于测试
    session = setup_session_with_retry()
    
    # 测试Azure端点连接 (跳过其他测试，直接测试目标)
    try:
        print("   Azure endpoint testing...")
        response = session.get(ENDPOINT_URL, timeout=(30, 60), verify=False)
        print(f"✅ Azure端点可访问 ({response.status_code})")
        return True
    except requests.exceptions.Timeout:
        print("⚠️ Azure端点连接超时，但程序将继续尝试")
        print("💡 建议: 检查网络连接稳定性")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"⚠️ Azure端点连接错误: {str(e)[:100]}...")
        print("💡 建议:")
        print("   1. 检查防火墙设置")
        print("   2. 检查代理设置")
        print("   3. 尝试使用其他网络")
        return False
    except Exception as e:
        print(f"⚠️ Azure endpoint error: {str(e)[:100]}...")
        return False

def analyze_with_azure_gpt4_expert(content, filename=None, age_dict=None):
    """使用Azure GPT-4专家级分析病理报告"""
    
    # 截取内容，避免超过token限制
    if len(content) > 10000:
        content = content[:10000] + "...[content truncated]"
    
    # Obtain age data from excel
    excel_age = None
    if filename and age_dict:
        excel_age = get_age_from_excel(filename, age_dict)
        if excel_age:
            print(f"📅{excel_age} years old", end=" ", flush=True)
    
    prompt = f"""You are a medical expert specializing in thyroid cancer pathology report analysis. You must strictly follow the 8th Edition of AJCC TNM staging system for differentiated thyroid cancer and ATA risk stratification rules.

PATHOLOGY REPORT:
{content}

## STRUCTURED QUESTIONS FOR ANALYSIS:

**1. Type of surgery** (no specific question - general category)
**1.1 Extent of thyroidectomy**: Specify the type of thyroidectomy: total thyroidectomy, right hemithyroidectomy, left hemithyroidectomy, or a combination.
**1.2 Extent of lymph node dissection**: Identify the dissected lymph node levels or regions during the surgery.

**2. Site of tumor**: Could you please report the tumor's site (left lobe/right lobe/both lobes) and focality (unifocal/multifocal)?

**3. AJCC stage**: Determine the thyroid cancer stage (AJCC 8th edition) based on the patient's age, TNM stages, and AJCC8 rules. Return Stage I, II, III, IVA, or IVB. 
**3.1 Age of the patient**: Provide the patient's age without the unit.
**3.2 T stage**: Determine the patient's T stage according to AJCC 8th Edition guidelines. Consider the tumor size and the presence and site of gross extrathyroidal extension only and do not count microscopic or minimal extrathyroidal extension (for example, a patient with microscopic extrathyroidal extension and tumor size of 1.6cm, the patient should be T1b). Do not directly extract the T stage from the pathology report, instead, analyze the documented tumor characteristics and apply AJCC8 criteria to derive the stage. 
**3.2.1 Tumour size**: Report the largest dimension of confirmed papillary and follicular carcinoma, with the unit. Ignore unconfirmed specimens. Consider updated or supplemental findings.
**3.2.2 Extrathyroidal extension**: Does the patient have gross or microscopic extrathyroidal extension? Please return 'no extrathyroidal extension', 'microscopic extrathyroidal extension', or 'gross extrathyroidal extention'. Please also return the site of extrathyroidal extension if the patient has gross or microscopic extrathyroidal extension. Examples of microscopic extrathyroidal extension include: minimal extrathyroidal extension; extrathyroidal invades to perithyroidal fibroadipose tissue only; extrathyroidal extension is present/seen. Examples of gross extrathyroidal extension include extrathyroidal extension invading muscles, larynx, trachea, recurrent largngeal nerve, prevertebral fascia, carotid artery or mediastinal vessles, etc. Examples of no extrathyroidal extion include 'extrathyroidal extension: not identified' and 'tumor confined'
**3.3 N stage**: Determine the patient's N stage according to AJCC 8th Edition guidelines by analyzing the lymph node involvement, not directly extracting from the report.
**3.3.1 Number of lymph node identified**: Provide the total number of resected or dissected lymph nodes as an integer. Return '-' if not mentioned.
**3.3.2 Number of lymph node involved**: Total number of lymph node involved: This is the total number of lymph nodes involved in which cancer was found. Only return an integer. If there is no mention of any lymph node in the report, return -. If none of the dissected lymph nodes were positive, return 0. The number of lymph node involved may be reported in different specimen separately, please add them up.
**3.3.3 Site of lymph node involved**:  Report the location of involved lymph nodes (e.g., level 1 - 7, lateral, central, cervical). Return 'Not mentioned' if no lymph nodes are involved.
**3.3.4 Largest size of the involved lymph node**: Report the largest dimension of involved lymph nodes with the unit. Return '-' if not mentioned. DO not return answers from gross description
**3.4 M stage**: Distant metastasis staging (pM): Report 'MX', 'M0', or 'M1' if the pM is mentioned in the clinical note; otherwise, return '-'.
**3.4.1 Site of distant metastasis**: Specify the site of distant metastasis if present.

**4. ATA risk category**: What is the 2015 ATA risk category of the patient: generate low/intermediate/high
**4.1 Distant metastasis**: Same as 3.4.1
**4.2 Margins involvement**: Is margin involved?
**4.3 Histologic type and subtype**: Report the histologic type ('papillary', 'follicular','medullary', 'anaplastic') and subtype or variant.
**4.4 Vascular invasion and extensiveness**: Vascular invasion and extensiveness of vascular invasion: This is whether the cancer cells have spread through blood vessels. It is sometimes presented in the section 'Vascular invasion:' or in the sentence 'Tumor invades in blood vessel'. Return 'negative' if the vascular invasion is due to lymphovascular invasion such as 'Psammoma bodies in lymphatics' and 'fine needle aspiration'. If the note reported 'Indeterminate', ignore this and find another evidence. Return the exact sentence in this field whenever possible.
**4.5 Number of lymph node involved**: Same as 3.3.2
**4.6 Largest size of the involved lymph node**: Same as 3.3.4
**4.7 Capsular invasion**: Capsular invasion: positive examples include 'extracapsular extension is present', 'tumor encapsulation: partially surrounded', 'capsular invasion: focal invasion, present or identified'.
**4.8 Gene mutation**: Report the gene mutation type if identified, or '-'.

**5. Other information** (no specific question - general category)
**5.1 Inadvertent parathyroidectomy**: Does the patient receive parathyroidectomy (i.e. Does parathyroid gland or tissue is identified)? please return yes or no
**5.2 Lymphocytic thyroiditis**: Indicate if the patient has lymphocytic thyroiditis or thyroiditis. Please return yes or no and do not quote sentences not related to thyroiditis

## Extrathyroidal extension definitions:
    - Microscopic extrathyroidal extension:
        "EXTRATHYROIDAL EXTENSION IS SEEN"
        "...WITH MICROSCOPIC EXTRATHYROIDAL EXTENSION"
        "Extra thyroidal extension: positive for minimal extrathyroidal extension"
        "Extrathyroid Extension: Invades: peri-thyroid fibroadipose tissue"
        "Tumor with minimal extrathyroidal extension"
        "Extrathyroidal Extension: Present, minimal"
    - Gross extrathyroidal extension:
        "extentive invasion into perithyroidal soft tissues with invasion of skeletal muscle identified
        "extending beyond the thyroid capsule to Invade the trachea"
        "invading outside the thyroid into the region of the recurrent laryngeal nerve"
    - No extrathyroidal extension:
        "NO DEFINITE ANGIOLYMPHATIC INVASION OR EXTRATHYROIDAL EXTENSION"
        "Extrathyroid Extension: Not Identified"
        "CONFINED WITHIN THYROID"
        "NO ANGIOLYMPHATIC INVASION OR EXTRATHYROIDAL EXTENSION"
    - Sentences such as "EXTRACAPSULAR EXTENSION IS PRESENT", "MULTIFOCAL CAPSULAR INVASION IS IDENTIFIED", "NO CAPSULAR OR ANGIOLYMPHATIC INVASION IS SEEN" are irrelevant to extrathyroidal extension


## TNM STAGING RULES (8th Edition of AJCC TNM staging: Differentiated Thyroid Cancer)

### T (Primary Tumor) Definitions:
- Tx: Primary tumor cannot be assessed
- T0: No evidence of primary tumor
- T1: Tumor <= 2cm in greatest dimension, limited to thyroid
  - T1a: Tumor <= 1cm, limited to thyroid
  - T1b: Tumor > 1cm but <= 2cm, limited to thyroid
- T2: Tumor > 2cm but <= 4cm, limited to thyroid
- T3: Tumor > 4cm limited to thyroid, or tumor with gross extrathyroidal extension
  - T3a: Tumor > 4cm, limited to thyroid
  - T3b: Any size tumor with gross extrathyroidal extension invading only into skeletal muscle or strap muscles (sternohyoid, sternothyroid, thyroid, or omohyoid muscles)
- T4: Tumor with invasion beyond thyroid
  - T4a: Any size tumor with gross extrathyroidal extension invading into subcutaneous soft tissues, larynx, trachea, esophagus, or recurrent laryngeal nerve
  - T4b: Any size tumor with gross extrathyroidal extension invading into prevertebral fascia or encasing carotid artery or mediastinal vessels

### N (Regional Lymph Nodes) Definitions:
- Nx: Regional lymph nodes cannot be assessed
- N0: No evidence of regional lymph node metastasis
- N1: Regional lymph node metastasis
  - N1a: Metastasis to level VI or VII lymph nodes (pretracheal, paratracheal, Delphian, or upper mediastinal)
  - N1b: Metastasis to unilateral, bilateral, or contralateral cervical lymph nodes (levels I, II, III, IV, V) or retropharyngeal lymph nodes

### M (Distant Metastasis) Definitions:
- M0: No distant metastasis
- M1: Distant metastasis present

### STAGING LOGIC RULES:
**Rule A: If patient age < 55 years:**
- If M = M0, then final stage is Stage I (any T, any N)
- If M = M1, then final stage is Stage II (any T, any N)

**Rule B: If patient age >= 55 years:**
- Stage I: T1 or T2, N0 or Nx, M0
- Stage II: T1 or T2, N1, M0
- Stage II: T3a or T3b, any N, M0
- Stage III: T4a, any N, M0
- Stage IVA: T4b, any N, M0
- Stage IVB: any T, any N, M1

## ATA RISK STRATIFICATION RULES

### High Risk Criteria (Check FIRST):
Patient is High Risk if ANY of the following are true:
1. Gross extrathyroidal extension (gross ETE) into skeletal muscle or recurrent laryngeal nerve
2. Margins are involved or positive or tumor extends to the margin of the resction
3. Presence of distant metastasis
4. Postoperative pathologic N1 with any involved lymph node >= 3 cm in largest dimension
5. Follicular thyroid cancer with extensive vascular invasion (>= 4 foci of invasion)

### Intermediate Risk Criteria (Check if NOT High Risk):
Patient is Intermediate Risk if ANY of the following are true:
1. Aggressive histological subtypes (tall cell, hobnail variant, or columnar cell carcinoma)
2. Papillary thyroid cancer with vascular invasion
3. Clinically N1 OR postoperative pathologic N1 with > 5 involved lymph nodes, all < 3 cm
4. Multifocal papillary microcarcinoma with ETE AND BRAF V600E mutation
5. Microscopic extrathyroidal extension invading perithyroidal soft tissues or perithyroidal adipose tissue only

### Low Risk Criteria (Check if NOT High or Intermediate Risk):
Patient is Low Risk if ANY of the following are true:
1. Papillary thyroid cancer meeting ALL conditions:
   - No local or distant metastases
   - Complete resection (uninvolved margins)
   - No extrathyroidal extension
   - No aggressive histology
   - No vascular invasion
   - Clinical N0 OR pathologic N1 with <= 5 involved lymph nodes, all micrometastases (< 0.2 cm)
2. Intrathyroidal, encapsulated, well-differentiated follicular cancer with capsular invasion and no/minimal vascular invasion (< 4 foci)
3. Intrathyroidal, unifocal or multifocal papillary microcarcinoma

## ANALYSIS INSTRUCTIONS:
1. Answer each structured question precisely and completely
2. For age: Extract from report or use clinical data{f": {excel_age} years old" if excel_age else ""}
3. For patient aged <55 years old, the AJCC overall stage is I if M = M0 and II if M = M1.
4. Derive T/N stages based on AJCC definitions, not direct extraction from the report.
5. Determine ATA risk using a hierarchical approach (High → Intermediate → Low).
6. **CRITICAL**: ALL supporting quotes MUST be exact text from the original pathology report only
   - Find all supporting quotes for your answers from the report.
   - DO NOT include medical criteria, rules, or classifications as quotes
   - DO NOT include phrases like "Clinical N0 OR pathologic N1 with <= 5 involved lymph nodes"
   - DO NOT include ATA risk criteria definitions
   - DO NOT include TNM staging rules or explanations
   - ONLY use direct sentences/phrases that appear in the input pathology report

Return JSON with ALL questions:
{{
  "results": [
    {{"question": "1.1 Extent of thyroidectomy", "answer": "your answer", "quotes": ["quote1"]}},
    {{"question": "1.2 Extent of lymph node dissection", "answer": "your answer", "quotes": ["quote1"]}},
    {{"question": "2 Site of tumor", "answer": "your answer", "quotes": ["quote1"]}},
    {{"question": "3 AJCC stage", "answer": "Stage I/II/III/IVA/IVB", "quotes": ["quote1"]}},
    {{"question": "3.1 Age of the patient", "answer": "age number without unit", "quotes": ["quote1"]}},
    {{"question": "3.2 T stage", "answer": "T stage based on AJCC8 analysis", "quotes": ["quote1"]}},
    {{"question": "3.2.1 Tumour size", "answer": "tumor size with unit", "quotes": ["quote1"]}},
    {{"question": "3.2.2 Extrathyroidal extension", "answer": "presence and site of extension", "quotes": ["quote1"]}},
    {{"question": "3.3 N stage", "answer": "N stage based on AJCC8 analysis", "quotes": ["quote1"]}},
    {{"question": "3.3.1 Number of lymph node identified", "answer": "integer or NaN", "quotes": ["quote1"]}},
    {{"question": "3.3.2 Number of lymph node involved", "answer": "integer, 0, or NaN", "quotes": ["quote1"]}},
    {{"question": "3.3.3 Site of lymph node involved", "answer": "location or 'Not mentioned'", "quotes": ["quote1"]}},
    {{"question": "3.3.4 Largest size of the involved lymph node", "answer": "size with unit or NaN", "quotes": ["quote1"]}},
    {{"question": "3.4 M stage", "answer": "MX, M0, or M1", "quotes": ["quote1"]}},
    {{"question": "3.4.1 Site of distant metastasis", "answer": "site or none", "quotes": ["quote1"]}},
    {{"question": "4 ATA risk category", "answer": "low/intermediate/high", "quotes": ["quote1"]}},
    {{"question": "4.1 Distant metastasis", "answer": "site of distant metastasis", "quotes": ["quote1"]}},
    {{"question": "4.2 Margins involvement", "answer": "margin status", "quotes": ["quote1"]}},
    {{"question": "4.3 Histologic type and subtype", "answer": "histologic type and variants", "quotes": ["quote1"]}},
    {{"question": "4.4 Vascular invasion and extensiveness", "answer": "vascular invasion status", "quotes": ["quote1"]}},
    {{"question": "4.5 Number of lymph node involved", "answer": "integer, 0, or NaN", "quotes": ["quote1"]}},
    {{"question": "4.6 Largest size of the involved lymph node", "answer": "size with unit or NaN", "quotes": ["quote1"]}},
    {{"question": "4.7 Capsular invasion", "answer": "capsular invasion status", "quotes": ["quote1"]}},
    {{"question": "4.8 Gene mutation", "answer": "mutation type or none", "quotes": ["quote1"]}},
    {{"question": "5.1 Inadvertent parathyroidectomy", "answer": "yes/no with explanation", "quotes": ["quote1"]}},
    {{"question": "5.2 Lymphocytic thyroiditis", "answer": "yes/no with explanation", "quotes": ["quote1"]}}
  ]
}}"""

    headers = {
        "api-key": AZURE_OPENAI_API_KEY,
        "Content-Type": "application/json"
    }
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a medical expert specializing in thyroid cancer pathology with expertise in TNM staging and ATA risk stratification."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 3000,
        "temperature": 0.001
    }
    
    # 创建session
    session = setup_session_with_retry()
    
    # 重试机制
    max_retries = 5  # 增加重试次数
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄{attempt+1}", end="", flush=True)
                wait_time = min(30 * (2 ** attempt), 120)  # 指数退避，最大120秒
                time.sleep(wait_time)
            else:
                print("🤖", end="", flush=True)
            
            # 使用更长的超时时间
            response = session.post(API_URL, headers=headers, json=data, timeout=(60, 180), verify=False)
        
            if response.status_code == 401:
                print("❌invalid Azure API key", end=" ")
                return create_fallback_results("Invalid Azure API key")
            elif response.status_code == 429:
                print("❌API rate limit", end=" ")
                if attempt < max_retries - 1:
                    continue  # retry
                return create_fallback_results("API rate limit")
            elif response.status_code == 404:
                print("❌Deployment not found", end=" ")
                return create_fallback_results("Deployment not found")
            elif response.status_code != 200:
                print(f"❌HTTP{response.status_code}", end=" ")
                if attempt < max_retries - 1:
                    continue  # retry
                return create_fallback_results(f"HTTP {response.status_code}: {response.text[:100]}")
                
            result = response.json()
            
            if 'error' in result:
                print("❌API error", end=" ")
                if attempt < max_retries - 1:
                    continue  # retry
                return create_fallback_results(result['error'].get('message', 'Unknown error'))
                
            if 'choices' not in result or len(result['choices']) == 0:
                print("❌no response", end=" ")
                if attempt < max_retries - 1:
                    continue  # retry
                return create_fallback_results("No response from Azure OpenAI")
                
            gpt_response = result['choices'][0]['message']['content'].strip()
            
            # 解析JSON响应
            try:
                start = gpt_response.find('{')
                end = gpt_response.rfind('}') + 1
                
                if start != -1 and end != -1:
                    json_content = gpt_response[start:end]
                    parsed = json.loads(json_content)
                    print("✅", end=" ", flush=True)
                    return parsed.get('results', [])
                else:
                    print("⚠️No JSON in response", end=" ")
                    if attempt < max_retries - 1:
                        continue  # retry
                    return create_fallback_results("No JSON in response")
                    
            except json.JSONDecodeError:
                print("⚠️JSON parsing error", end=" ")
                if attempt < max_retries - 1:
                    continue  # retry
                return create_fallback_results("JSON parsing error")
                
        except requests.exceptions.Timeout:
            print("❌request timeout", end=" ")
            if attempt < max_retries - 1:
                continue  # retry
            return create_fallback_results("Request timeout")
        except requests.exceptions.ConnectionError:
            print("❌connection error", end=" ")
            if attempt < max_retries - 1:
                continue  # retry
            return create_fallback_results("Connection error")
        except Exception as e:
            print(f"❌{str(e)[:20]}", end=" ")
            if attempt < max_retries - 1:
                continue  # retry
            return create_fallback_results(str(e))
    
    # 如果所有重试都失败了
    return create_fallback_results("All retries failed")

def create_fallback_results(error):
    """创建失败时的默认结果 - 专家版本"""
    questions = [
        "1.1 Extent of thyroidectomy", "1.2 Extent of lymph node dissection", "2 Site of tumor",
        "3 AJCC stage", "3.1 Age of the patient", "3.2 T stage", "3.2.1 Tumour size", "3.2.2 Extrathyroidal extension",
        "3.3 N stage", "3.3.1 Number of lymph node identified", "3.3.2 Number of lymph node involved",
        "3.3.3 Site of lymph node involved", "3.3.4 Largest size of the involved lymph node",
        "3.4 M stage", "3.4.1 Site of distant metastasis", "4 ATA risk category", 
        "4.1 Distant metastasis", "4.2 Margins involvement", "4.3 Histologic type and subtype", 
        "4.4 Vascular invasion and extensiveness", "4.5 Number of lymph node involved", 
        "4.6 Largest size of the involved lymph node", "4.7 Capsular invasion", "4.8 Gene mutation", 
        "5.1 Inadvertent parathyroidectomy", "5.2 Lymphocytic thyroiditis"
    ]
    
    return [{"question": q, "answer": "Analysis failed", "quotes": [f"Error: {error}"]} for q in questions]

def generate_expert_html(original_content, results, filename):
    """生成专家级双面板HTML报告"""
    
    # 按问题类型分组
    def get_result(question_prefix):
        return [r for r in results if r["question"].startswith(question_prefix)]
    
    def get_single_result(question_prefix):
        found = next((r for r in results if r["question"].startswith(question_prefix)), None)
        return found if found else {"answer": "Not found", "quotes": []}
    
    surgery_items = get_result("1.")
    site_item = get_single_result("2")
    
    # AJCC stage 子问题
    ajcc_stage_item = get_single_result("3 AJCC")  # 新的主stage问题
    age_item = get_single_result("3.1")
    t_stage_item = get_single_result("3.2 T")
    tumor_size_item = get_single_result("3.2.1")
    extension_item = get_single_result("3.2.2")
    n_stage_item = get_single_result("3.3 N")
    ln_identified_item = get_single_result("3.3.1")
    ln_involved_item = get_single_result("3.3.2")
    ln_site_item = get_single_result("3.3.3")
    ln_size_item = get_single_result("3.3.4")
    m_stage_item = get_single_result("3.4 M")
    distant_meta_item = get_single_result("3.4.1")
    
    # ATA risk category 子问题
    ata_item = get_single_result("4 ATA")
    ata_distant_item = get_single_result("4.1")
    ata_margins_item = get_single_result("4.2")
    ata_histologic_item = get_single_result("4.3")
    ata_vascular_item = get_single_result("4.4")
    ata_ln_involved_item = get_single_result("4.5")
    ata_ln_size_item = get_single_result("4.6")
    ata_capsular_item = get_single_result("4.7")
    ata_gene_item = get_single_result("4.8")
    
    # Other information 子问题
    parathyroid_item = get_single_result("5.1")
    thyroiditis_item = get_single_result("5.2")
    
    def verify_quote_in_content(quote, content):
        """严格验证quote是否真正存在于原文中"""
        if not quote or not quote.strip():
            return False
        
        quote_clean = quote.strip()
        
        # 检查是否是系统生成的信息（年龄、医学规则等）
        system_indicators = [
            "if age not found in report, use this age from clinical data",
            "age from clinical data:",
            "clinical data:",
            "excel data:",
            "patient age from external source",
            "clinical n0 or pathologic n1",
            "≤ 5 involved lymph nodes",
            "all micrometastases",
            "< 0.2 cm",
            "high risk criteria:",
            "intermediate risk criteria:",
            "low risk criteria:",
            "ata risk",
            "tnm staging",
            "ajcc 8th edition",
            "stage i, ii, iii, iva, ivb",
            "t1a,t1b, t2, t3a, t3b, t4a, t4b",
            "n0, n1a, n1b",
            "m0, m1, mx"
        ]
        
        for indicator in system_indicators:
            if indicator.lower() in quote_clean.lower():
                return False  # 这是系统规则或生成的信息，不是原文内容
        
        # 检查是否包含医学分类术语（这些通常是规则而非原文）
        medical_classification_terms = [
            "criteria", "classification", "category", "definition",
            "according to", "based on", "meets", "fulfills",
            "stage i", "stage ii", "stage iii", "stage iva", "stage ivb",
            "high risk", "intermediate risk", "low risk",
            "papillary thyroid cancer with",
            "follicular thyroid cancer with",
            "meets criteria", "fulfills criteria",
            "rationale:", "therefore", "thus", "hence",
            "the patient meets", "this meets", "this fulfills",
            "risk stratification", "risk category",
            "ata guidelines", "ata classification",
            "tumor is >", "tumor ≤", "tumor >",
            "limited to thyroid", "extrathyroidal extension",
            "regional lymph node metastasis",
            "distant metastasis present",
            "no evidence of", "any size tumor"
        ]
        
        for term in medical_classification_terms:
            if term.lower() in quote_clean.lower():
                return False
        
        # 移除常见的GPT添加的前缀/后缀
        prefixes_to_remove = [
            "According to the report:",
            "The report states:",
            "The pathology shows:",
            "As mentioned:",
            "The text indicates:",
            "Based on the report:",
            "No mention of",
            "Not mentioned in",
            "Age information not available in report",
            "Patient age not specified in report",
            "The patient meets",
            "This meets",
            "This fulfills",
            "Rationale:",
            "Therefore",
            "Thus",
            "Hence"
        ]
        
        for prefix in prefixes_to_remove:
            if quote_clean.lower().startswith(prefix.lower()):
                quote_clean = quote_clean[len(prefix):].strip()
        
        # 移除引号和其他特殊字符
        quote_clean = quote_clean.strip('"\'""''')
        
        # 如果quote太短且不是专门的系统信息，则跳过
        if len(quote_clean) < 10:
            return False
        
        quote_lower = quote_clean.lower()
        content_lower = content.lower()
        
        # 1. 直接完全匹配（最严格）
        if quote_lower in content_lower:
            return True
        
        # 2. 移除标点符号后的精确匹配
        import re
        quote_no_punct = re.sub(r'[^\w\s]', ' ', quote_lower)
        content_no_punct = re.sub(r'[^\w\s]', ' ', content_lower)
        
        # 规范化空白字符
        quote_normalized = ' '.join(quote_no_punct.split())
        content_normalized = ' '.join(content_no_punct.split())
        
        if quote_normalized in content_normalized:
            return True
        
        # 3. 连续片段匹配（至少15个字符的连续匹配）
        if len(quote_normalized) >= 15:
            for i in range(len(quote_normalized) - 14):
                fragment = quote_normalized[i:i+15]
                if fragment in content_normalized and len(fragment.strip()) >= 12:
                    return True
        
        # 4. 严格的单词序列匹配（至少6个单词，90%匹配率）
        quote_words = quote_normalized.split()
        content_words = content_normalized.split()
        
        if len(quote_words) >= 6:  # 至少6个单词
            for i in range(len(content_words) - len(quote_words) + 1):
                content_segment = content_words[i:i+len(quote_words)]
                matches = sum(1 for q, c in zip(quote_words, content_segment) if q == c)
                if matches >= len(quote_words) * 0.9:  # 提高到90%匹配率
                    return True
        
        return False

    def render_question_item(item, title=None):
        if not title:
            title = item["question"]
        
        # 验证并过滤quotes，只保留原文中真实存在的
        valid_quote_links = []
        system_info_quotes = []
        
        for i, quote in enumerate(item["quotes"]):
            if quote and quote.strip():
                # 检查是否是系统添加的年龄信息
                is_system_info = any(indicator.lower() in quote.lower() for indicator in [
                    "if age not found in report, use this age from clinical data",
                    "age from clinical data:",
                    "clinical data:",
                    "excel data:"
                ])
                
                if is_system_info:
                    # 这是系统信息，单独处理
                    system_info_quotes.append(quote)
                elif verify_quote_in_content(quote, original_content):
                    # 这是原文中的真实内容
                    quote_id = f"quote-{abs(hash(quote + title))}_{i}"
                    escaped_quote = quote.replace('"', '&quot;').replace("'", "&#39;")
                    valid_quote_links.append(f'<span class="quote-link" data-quote="{escaped_quote}" onclick="highlightQuote(this)" title="点击定位到原文">{quote}</span>')
        
        # 构建quotes HTML
        quotes_parts = []
        
        if valid_quote_links:
            quotes_parts.append('; '.join(valid_quote_links))
        
        if system_info_quotes:
            # 为系统信息添加特殊样式
            system_quotes_html = []
            for sq in system_info_quotes:
                system_quotes_html.append(f'<span class="system-info-quote" title="信息来源：Excel数据文件">{sq}</span>')
            quotes_parts.append('; '.join(system_quotes_html))
        
        if quotes_parts:
            quotes_html = '; '.join(quotes_parts)
        else:
            quotes_html = '<span class="no-quotes">No valid supporting quotes found in original text</span>'
        
        return f'''
            <div class="question-item">
                <h4>{title}</h4>
                <div class="answer-group">
                    <div class="answer-item">
                        <span class="label">Answer</span>
                        <span class="value">{item["answer"]}</span>
                    </div>
                    <div class="answer-item">
                        <span class="label">Supporting quotes</span>
                        <span class="value quotes-container">{quotes_html}</span>
                    </div>
                </div>
            </div>'''
    
    # 创建患者总结信息
    def create_patient_summary():
        """创建患者癌症分型、TNM、AJCC STAGE 和 ATA 的总结"""
        # 提取关键信息
        histologic_type = ata_histologic_item.get("answer", "Unknown")
        t_stage = t_stage_item.get("answer", "Unknown")
        n_stage = n_stage_item.get("answer", "Unknown") 
        m_stage = m_stage_item.get("answer", "M0")
        ajcc_stage = ajcc_stage_item.get("answer", "Unknown")
        ata_risk = ata_item.get("answer", "Unknown")
        
        # 检查是否为失败的分析结果
        if "Analysis failed" in str(histologic_type):
            return f'''
            <div class="patient-summary">
                <h2>Thyroid Cancer Classification Summary</h2>
                <div class="summary-content">Analysis failed - Please check network connection and retry</div>
            </div>'''
        
        # 格式化TNM
        tnm_combined = f"{t_stage}{n_stage}{m_stage}"
        
        # 格式化AJCC stage
        ajcc_formatted = f"AJCC 8 {ajcc_stage}" if ajcc_stage != "Unknown" else "AJCC 8 stage unknown"
        
        # 格式化ATA risk
        ata_formatted = f"ATA (2015) {ata_risk} risk" if ata_risk != "Unknown" else "ATA (2015) risk unknown"
        
        summary_text = f"{histologic_type}, {tnm_combined} ({ajcc_formatted}), {ata_formatted}"
        
        return f'''
        <div class="patient-summary">
            <h2>Thyroid Cancer Classification Summary</h2>
            <div class="summary-content">{summary_text}</div>
        </div>'''
    
    # 生成右侧分析内容
    analysis_sections = f'''
    {create_patient_summary()}
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">1</div>
            <h2 class="large-header">Type of surgery</h2>
        </div>
        <div class="section-content">'''
    
    for item in surgery_items:
        analysis_sections += render_question_item(item)
    
    analysis_sections += f'''
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">2</div>
            <h2>Site of tumor</h2>
        </div>
        <div class="section-content">
            {render_question_item(site_item, "Site of tumor")}
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">3</div>
            <h2 class="large-header">AJCC 8th Edition TNM Stage</h2>
        </div>
        <div class="section-content">
            {render_question_item(ajcc_stage_item, "Overall AJCC Stage")}
            
            {render_question_item(age_item, "3.1 Age of the patient")}
            
            <div class="subsection">
                <h3 class="large-subsection-header">3.2 T stage (Primary Tumor)</h3>
                {render_question_item(t_stage_item, "T stage determination")}
                {render_question_item(tumor_size_item, "3.2.1 Tumour size")}
                {render_question_item(extension_item, "3.2.2 Extrathyroidal extension")}
            </div>
            
            <div class="subsection">
                <h3 class="large-subsection-header">3.3 N stage (Regional Lymph Nodes)</h3>
                {render_question_item(n_stage_item, "N stage determination")}
                {render_question_item(ln_identified_item, "3.3.1 Number of lymph node identified")}
                {render_question_item(ln_involved_item, "3.3.2 Number of lymph node involved")}
                {render_question_item(ln_site_item, "3.3.3 Site of lymph node involved")}
                {render_question_item(ln_size_item, "3.3.4 Largest size of the involved lymph node")}
            </div>
            
            <div class="subsection">
                <h3 class="large-subsection-header">3.4 M stage (Distant Metastasis)</h3>
                {render_question_item(m_stage_item, "M stage determination")}
                {render_question_item(distant_meta_item, "3.4.1 Site of distant metastasis")}
            </div>

        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">4</div>
            <h2 class="large-header">ATA Risk Stratification</h2>
        </div>
        <div class="section-content">
            <div class="ata-main">
                {render_question_item(ata_item, "ATA Risk Category (High → Intermediate → Low hierarchy)")}
            </div>
            <div class="ata-details">
                <h4>Risk Assessment Details:</h4>
                {render_question_item(ata_distant_item, "4.1 Distant metastasis")}
                {render_question_item(ata_margins_item, "4.2 Margins involvement")}
                {render_question_item(ata_histologic_item, "4.3 Histologic type and subtype")}
                {render_question_item(ata_vascular_item, "4.4 Vascular invasion and extensiveness")}
                {render_question_item(ata_ln_involved_item, "4.5 Number of lymph node involved")}
                {render_question_item(ata_ln_size_item, "4.6 Largest size of the involved lymph node")}
                {render_question_item(ata_capsular_item, "4.7 Capsular invasion")}
                {render_question_item(ata_gene_item, "4.8 Gene mutation")}
            </div>
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">5</div>
            <h2>Other information</h2>
        </div>
        <div class="section-content">
            {render_question_item(parathyroid_item, "5.1 Inadvertent parathyroidectomy")}
            {render_question_item(thyroiditis_item, "5.2 Lymphocytic thyroiditis")}
        </div>
    </div>'''
    
    # 格式化原始报告内容并添加智能高亮功能
    def create_searchable_content(content):
        """创建可搜索和高亮的内容"""
        # 按行分割内容
        lines = content.split('\n')
        formatted_lines = []
        
        for i, line in enumerate(lines):
            if line.strip():  # 非空行
                # 为每行添加唯一ID，便于搜索和高亮
                line_id = f"line-{i}"
                formatted_lines.append(f'<span id="{line_id}" class="content-line">{line}</span>')
            else:
                formatted_lines.append('<br>')
        
        return '<br>'.join(formatted_lines)
    
    formatted_content = create_searchable_content(original_content)
    
    html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expert Azure GPT-4 Analysis - {filename}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header-title {{ font-size: 18px; font-weight: 600; }}
        .expert-badge {{
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }}
        
        .main-container {{
            display: flex;
            height: calc(100vh - 60px);
        }}
        
        .panel {{
            width: 50%;
            background: white;
            display: flex;
            flex-direction: column;
        }}
        
        .left-panel {{ border-right: 2px solid #e0e6ed; }}
        
        .panel-content {{
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }}
        
                 .original-content {{
             font-family: 'Courier New', monospace;
             font-size: 13px;
             line-height: 1.5;
             color: #333;
         }}
         
         .content-line {{
             display: inline;
             transition: background-color 0.3s ease;
         }}
         
         .content-line.highlighted {{
             background-color: #ff9500 !important;
             color: white !important;
             padding: 2px 4px;
             border-radius: 3px;
             font-weight: bold;
             box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
         }}
         
         .quote-link {{
             cursor: pointer;
             color: #2c5aa0;
             text-decoration: underline;
             transition: all 0.2s ease;
             padding: 2px 4px;
             border-radius: 3px;
             margin: 0 2px;
         }}
         
         .quote-link:hover {{
             background-color: #e3f2fd;
             color: #1565c0;
             transform: translateY(-1px);
         }}
         
         .quote-link.active {{
             background-color: #ff9500;
             color: white;
             font-weight: bold;
         }}
         
         .quotes-container {{
             line-height: 1.6;
         }}
         
         .no-quotes {{
             color: #999;
             font-style: italic;
             font-size: 12px;
         }}
         
         .quote-link {{
             position: relative;
         }}
         
         .quote-link::before {{
             content: "📍";
             font-size: 10px;
             margin-right: 3px;
             opacity: 0.6;
         }}
         
         .system-info-quote {{
             background-color: #e3f2fd;
             color: #1565c0;
             padding: 2px 6px;
             border-radius: 4px;
             font-size: 12px;
             border-left: 3px solid #2196f3;
             margin: 0 2px;
             position: relative;
         }}
         
         .system-info-quote::before {{
             content: "📊";
             font-size: 10px;
             margin-right: 3px;
         }}
         
         .system-info-quote:hover {{
             background-color: #bbdefb;
         }}
        
        .analysis-section {{ margin-bottom: 30px; }}
        
        .section-header {{
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }}
        
        .section-number {{
            width: 28px; height: 28px;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .section-header h2 {{
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }}
        
        .section-header h2.large-header {{
            font-size: 22px;
            font-weight: 700;
        }}
        
        .patient-summary {{
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }}
        
        .patient-summary h2 {{
            color: #2c5aa0;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 12px;
            text-align: center;
        }}
        
        .summary-content {{
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }}
        
        .section-content {{ margin-left: 40px; }}
        .question-item {{ margin-bottom: 20px; }}
        .subsection {{ margin: 20px 0; padding-left: 20px; border-left: 3px solid #e9ecef; }}
        .subsection h3 {{ color: #495057; font-size: 16px; margin-bottom: 10px; margin-left: -40px; }}
        .subsection h3.large-subsection-header {{ font-size: 20px; font-weight: 700; margin-left: -40px; }}
        .final-stage {{ border-left: 3px solid #28a745; background: #f8fff9; padding: 15px; }}
        .ata-main {{ background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px; }}
        .ata-details {{ background: #f8f9fa; padding: 15px; border-radius: 8px; }}
        
        .question-item h4 {{
            color: #333;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }}
        
        .answer-item {{
            margin-bottom: 4px;
            display: flex;
            align-items: flex-start;
        }}
        
        .answer-item .label {{
            color: #666;
            font-size: 12px;
            min-width: 100px;
            margin-right: 8px;
        }}
        
        .answer-item .value {{
            color: #333;
            font-size: 12px;
            font-weight: 500;
            flex: 1;
        }}
        
        .panel-content::-webkit-scrollbar {{ width: 6px; }}
        .panel-content::-webkit-scrollbar-track {{ background: #f1f1f1; }}
        .panel-content::-webkit-scrollbar-thumb {{ background: #c1c1c1; border-radius: 3px; }}
        .panel-content::-webkit-scrollbar-thumb:hover {{ background: #a8a8a8; }}
        
        @media (max-width: 1024px) {{
            .main-container {{ flex-direction: column; height: auto; }}
            .panel {{ width: 100%; height: 50vh; }}
            body {{ overflow: auto; }}
        }}

        .pathology-title {
            color: #2c5aa0;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e6ed;
        }

        .section-content .subsection-item {
            font-size: 18px !important;
            margin-bottom: 15px;
        }

        .section-content .question-item h4 {
            font-size: 18px !important;
            color: #333;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .answer-item .label {
            color: #666;
            font-size: 16px !important;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .answer-item .value {
            color: #333;
            font-size: 16px !important;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-weight: 500;
        }

        .quotes-container {
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px !important;
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">Expert Pathology Analysis (AJCC 8th Edition TNM + ATA)</div>
        <div class="expert-badge">🩺 Azure GPT-4 Expert</div>
    </div>
    
    <div class="main-container">
        <div class="panel left-panel">
            <div class="panel-content">
                <h2 class="pathology-title">Pathology Report</h2>
                <div class="original-content">{formatted_content}</div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-content">
                {analysis_sections}
            </div>
                 </div>
     </div>
     
     <script>
         // 智能高亮和滚动功能
         let currentHighlighted = null;
         let currentActiveQuote = null;
         
         function highlightQuote(element) {{
             const quote = element.getAttribute('data-quote');
             if (!quote || quote.trim() === '') return;
             
             // 清除之前的高亮
             clearHighlights();
             
             // 设置当前活跃的quote
             if (currentActiveQuote) {{
                 currentActiveQuote.classList.remove('active');
             }}
             currentActiveQuote = element;
             element.classList.add('active');
             
             // 在左侧内容中搜索并高亮匹配的文本
             const originalContent = document.querySelector('.original-content');
             const contentLines = originalContent.querySelectorAll('.content-line');
             
             let bestMatch = null;
             let bestScore = 0;
             
             // 智能匹配算法
             for (let line of contentLines) {{
                 const lineText = line.textContent.toLowerCase();
                 const quoteText = quote.toLowerCase();
                 
                 // 直接匹配
                 if (lineText.includes(quoteText)) {{
                     bestMatch = line;
                     bestScore = 100;
                     break;
                 }}
                 
                 // 部分匹配 - 计算相似度
                 const words = quoteText.split(/\\s+/).filter(w => w.length > 2);
                 let matchedWords = 0;
                 
                 for (let word of words) {{
                     if (lineText.includes(word)) {{
                         matchedWords++;
                     }}
                 }}
                 
                 const score = (matchedWords / words.length) * 100;
                 if (score > bestScore && score > 30) {{
                     bestMatch = line;
                     bestScore = score;
                 }}
             }}
             
             // 如果找到匹配，高亮并滚动
             if (bestMatch) {{
                 bestMatch.classList.add('highlighted');
                 currentHighlighted = bestMatch;
                 
                 // 滚动到匹配位置
                 const leftPanel = document.querySelector('.left-panel .panel-content');
                 const lineTop = bestMatch.offsetTop;
                 const panelHeight = leftPanel.clientHeight;
                 
                 leftPanel.scrollTo({{
                     top: Math.max(0, lineTop - panelHeight / 2),
                     behavior: 'smooth'
                 }});
                 
                 // 添加闪烁效果
                 bestMatch.style.animation = 'highlight-flash 0.6s ease-in-out';
                 setTimeout(() => {{
                     if (bestMatch.style) {{
                         bestMatch.style.animation = '';
                     }}
                 }}, 600);
             }} else {{
                 // 如果没找到精确匹配，显示提示
                 showSearchHint(quote);
             }}
         }}
         
         function clearHighlights() {{
             if (currentHighlighted) {{
                 currentHighlighted.classList.remove('highlighted');
                 currentHighlighted = null;
             }}
         }}
         
         function showSearchHint(quote) {{
             // 创建临时提示
             const hint = document.createElement('div');
             hint.style.cssText = `
                 position: fixed;
                 top: 50%;
                 left: 50%;
                 transform: translate(-50%, -50%);
                 background: #ff9500;
                 color: white;
                 padding: 10px 20px;
                 border-radius: 5px;
                 z-index: 10000;
                 font-size: 14px;
                 box-shadow: 0 4px 12px rgba(0,0,0,0.3);
             `;
             hint.textContent = '未找到完全匹配的文本，请检查原文';
             document.body.appendChild(hint);
             
             setTimeout(() => {{
                 document.body.removeChild(hint);
             }}, 2000);
         }}
         
         // 添加CSS动画
         const style = document.createElement('style');
         style.textContent = `
             @keyframes highlight-flash {{
                 0% {{ box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.7); }}
                 50% {{ box-shadow: 0 0 0 10px rgba(255, 149, 0, 0.3); }}
                 100% {{ box-shadow: 0 0 0 0 rgba(255, 149, 0, 0); }}
             }}
         `;
         document.head.appendChild(style);
         
         // 双击清除所有高亮
         document.addEventListener('dblclick', function(e) {{
             if (e.target.closest('.original-content')) {{
                 clearHighlights();
                 if (currentActiveQuote) {{
                     currentActiveQuote.classList.remove('active');
                     currentActiveQuote = null;
                 }}
             }}
         }});
     </script>
 </body>
 </html>'''
    
    return html_template

def main():
    """主函数 - 专家版本"""
    print("🩺 Azure GPT-4.1 thyroid cancer pathology report analysis (AJCC 8 TNM + ATA)")
    print("=" * 60)
    
    # 项目内的相对路径
    input_dir = "./txts/txts"
    output_dir = "./html_reports_expert"
    
    print(f"📂 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    print(f"🔑 Azure API: {AZURE_OPENAI_API_KEY[:15]}...{AZURE_OPENAI_API_KEY[-10:]}")
    print(f"🌐 Endpoint URL: {ENDPOINT_URL}")
    print(f"🚀 Model: {DEPLOYMENT_NAME}")
    print(f"📋 AJCC 8 TNM + ATA risk")
    
    # 简化网络测试 - 直接跳过，让程序继续运行
    print("🔍 Skip the connection test, call API...")
    print("💡 Connection failed，use backup results")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载年龄数据
    age_dict = load_age_data()
    
    # 获取所有TXT文件
    txt_files = glob.glob(os.path.join(input_dir, "*.txt"))
    
    if not txt_files:
        print("❌ Failed to find TXT files")
        print(f"   Please confirm that {input_dir} has .txt files")
        return
    
    print(f"🔍 Found {len(txt_files)} TXT files")
    print("=" * 60)
    
    # 处理统计
    success_count = 0
    failed_count = 0
    
    # 处理所有文件
    for i, txt_file in enumerate(txt_files, 1):
        filename = os.path.basename(txt_file)
        base_name = os.path.splitext(filename)[0]
        
        print(f"[{i:2d}/{len(txt_files)}] {filename:<30} ", end="")
        
        try:
            # 读取文件内容
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用Azure GPT-4专家分析
            analysis_results = analyze_with_azure_gpt4_expert(content, filename, age_dict)
            
            # 检查分析是否成功
            analysis_success = any(r.get("answer", "") != "Analysis failed" for r in analysis_results)
            
            # 生成HTML报告
            html_content = generate_expert_html(content, analysis_results, base_name)
            
            # 保存HTML文件
            html_filename = f"{base_name}_expert_analysis.html"
            html_path = os.path.join(output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            if analysis_success:
                success_count += 1
                print("")
            else:
                failed_count += 1
                print("(use backup results)")
            
            # API请求限制控制 - 专家分析需要更多时间
            if i % 3 == 0:
                time.sleep(12)  # 每3个文件后稍作停顿
            else:
                time.sleep(8)  # 基本延迟
            
        except Exception as e:
            print(f"❌ 文件处理错误: {e}")
            failed_count += 1
    
    # creating index page
    print("\n📋 creating index page...")
    generate_expert_index_page(output_dir, success_count, failed_count, len(txt_files))
    
    # 处理完成报告
    print("\n" + "=" * 60)
    print("🎉 Azure GPT-4.1 thyroid cancer pathology report analysis completed！")
    print("=" * 60)
    print(f"✅ Successfully analyzed: {success_count}/{len(txt_files)}")
    print(f"⚠️ Failed to analyze: {failed_count}/{len(txt_files)}")
    print(f"📁 View results: {os.path.abspath(output_dir)}")
    print(f"🌐 Index page: {os.path.abspath(output_dir)}/index.html")
    print(f"🩺 Azure OpenAI GPT-4.1 AJCC TNM + ATA risk")
    
    # Cost estimation
    estimated_cost = success_count * 0.12  
    print(f"💰 Estimated cost: ${estimated_cost:.2f}")

def generate_expert_index_page(output_dir, success_count, failed_count, total_count):
    """生成专家级索引页面"""
    
    # Obtain all HTML files
    html_files = glob.glob(os.path.join(output_dir, "*_expert_analysis.html"))
    html_files.sort()
    
    index_html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure GPT-4.1 thyroid cancer pathology report analysis (8th edtion of AJCC TNM staging + 2015 ATA risk)</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }}
        .header {{
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        .expert-badge {{
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-top: 10px;
        }}
        .stats {{
            background: #f8f9fa;
            padding: 25px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
        }}
        .file-grid {{
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }}
        .file-item {{
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
        }}
        .file-item:hover {{
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #2c5aa0;
        }}
        .file-link {{
            display: block;
            color: #2c5aa0;
            text-decoration: none;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
        }}
        .file-link:hover {{
            color: #1e3d72;
        }}
        .file-id {{
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 15px;
        }}
        .expert-tag {{
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }}
        .features {{
            font-size: 11px;
            color: #6c757d;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }}
        .feature-tag {{
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🩺 Thyroid Cancer Pathology Report Analysis</h1>
            <p>Azure GPT-4.1 AJCC 8 TNM staging and ATA risk classification</p>
            <div class="expert-badge">🩺 Expert Medical Analysis</div>
        </div>
        <div class="stats">
            <h3 style="margin: 0; color: #2c5aa0; margin-bottom: 15px;">📊 Azure GPT-4.1 Pathology report analysis</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{len(html_files)}</div>
                    <div>Pathology reports</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">AJCC 8th</div>
                    <div>TNM staging</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">ATA</div>
                    <div>Risk stratification</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{success_count}</div>
                    <div>Successfully analyzed</div>
                </div>
            </div>
        </div>
        <div class="file-grid">'''
    
    for html_file in html_files:
        filename = os.path.basename(html_file)
        base_name = filename.replace('_expert_analysis.html', '')
        
        index_html += f'''
            <div class="file-item">
                <div class="expert-tag">Expert</div>
                <a href="{filename}" class="file-link" target="_blank">📄 {base_name}</a>
                <div class="file-id">pathology report | Azure GPT-4.1 AJCC 8 TNM + ATA risk</div>
                <div class="features">
                    <span class="feature-tag">AJCC 8th edition</span>
                    <span class="feature-tag">ATA risk stratification</span>
                    <span class="feature-tag">age-based</span>
                    <span class="feature-tag">analysis results</span>
                </div>
            </div>'''
    
    index_html += '''
        </div>
    </div>
</body>
</html>'''
    
    # 保存索引文件
    index_path = os.path.join(output_dir, 'index.html')
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(index_html)

if __name__ == "__main__":
    main()