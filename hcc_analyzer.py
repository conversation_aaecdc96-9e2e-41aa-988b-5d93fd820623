#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HCC (Hepatocellular Carcinoma) Medical Report Analyzer
Azure GPT-4 powered analysis with BCLC staging, Child-Pugh, MELD, and ALBI scoring
"""

import os
import glob
import json
import time
import requests
import pandas as pd
from datetime import datetime
from hcc_questions_mapping import HC<PERSON><PERSON><PERSON><PERSON>Mapping
from hcc_medical_rules import HCCMedicalRules
from html_generator import HTMLReportGenerator

# Azure OpenAI API配置
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# 构建Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

def load_age_data():
    """加载Excel文件中的年龄数据"""
    try:
        # 尝试加载现有的年龄数据文件
        excel_files = ["TCGA-THCA_GPT4.1_20250609 TW.xlsx", "HCC_patient_data.xlsx"]
        
        for excel_file in excel_files:
            if os.path.exists(excel_file):
                df = pd.read_excel(excel_file)
                age_dict = {}
                
                # 尝试不同的列名组合
                id_cols = ['case_submitter_id', 'patient_id', 'case_id']
                age_cols = ['age_at_index', 'age', 'patient_age']
                
                id_col = None
                age_col = None
                
                for col in id_cols:
                    if col in df.columns:
                        id_col = col
                        break
                        
                for col in age_cols:
                    if col in df.columns:
                        age_col = col
                        break
                
                if id_col and age_col:
                    for _, row in df.iterrows():
                        patient_id = row[id_col]
                        age = row[age_col]
                        if pd.notna(patient_id) and pd.notna(age):
                            age_dict[patient_id] = int(age)
                    
                    print(f"📊 加载了 {len(age_dict)} 个样本的年龄数据 from {excel_file}")
                    return age_dict
        
        print("⚠️ 未找到年龄数据文件")
        return {}
        
    except Exception as e:
        print(f"⚠️ 加载年龄数据失败: {e}")
        return {}

def get_age_from_excel(filename, age_dict):
    """从Excel数据中获取年龄"""
    # 从文件名提取ID (去掉.txt扩展名)
    patient_id = os.path.splitext(filename)[0]
    return age_dict.get(patient_id, None)

def analyze_with_azure_gpt4_hcc(content, filename=None, age_dict=None):
    """使用Azure GPT-4分析HCC医学报告"""
    
    # 截取内容，避免超过token限制
    if len(content) > 10000:
        content = content[:10000] + "...[content truncated]"
    
    # 从Excel获取年龄信息
    excel_age = None
    if filename and age_dict:
        excel_age = get_age_from_excel(filename, age_dict)
        if excel_age:
            print(f"📅{excel_age}岁", end=" ", flush=True)
    
    # 获取HCC问题映射和医学规则
    hcc_mapping = HCCQuestionMapping()
    hcc_rules = HCCMedicalRules()
    
    prompt = f"""You are a medical expert specializing in hepatocellular carcinoma (HCC) medical report analysis. You must analyze the report using BCLC staging, Child-Pugh classification, MELD score, and ALBI score systems.

MEDICAL REPORT:
{content}

## STRUCTURED QUESTIONS FOR HCC ANALYSIS:

{hcc_mapping.get_structured_questions()}

## HCC MEDICAL STAGING AND SCORING SYSTEMS:

{hcc_rules.get_bclc_staging_rules()}

{hcc_rules.get_child_pugh_rules()}

{hcc_rules.get_meld_rules()}

{hcc_rules.get_albi_rules()}

{hcc_rules.get_ecog_performance_status()}

## ANALYSIS INSTRUCTIONS:
1. Answer each structured question above precisely and completely
2. For age: Extract from report or use clinical data{f": {excel_age} years old" if excel_age else ""}
3. Calculate BCLC staging based on tumor characteristics, liver function, and performance status
4. Determine Child-Pugh classification using the 5-parameter scoring system
5. Calculate MELD score using the provided formula if lab values are available
6. Calculate ALBI score using the provided formula if albumin and bilirubin are available
7. **CRITICAL REQUIREMENT**: Supporting quotes MUST be EXACT, VERBATIM text from the original medical report
   - 🚫 NEVER create, summarize, paraphrase, or interpret any text
   - 🚫 NEVER include medical classification rules, criteria, or guidelines 
   - 🚫 NEVER include explanatory phrases like "according to", "based on", "meets criteria"
   - 🚫 NEVER include BCLC staging rules, Child-Pugh criteria, or MELD/ALBI formulas
   - ✅ ONLY copy-paste EXACT sentences/phrases that physically exist in the input report
   - ✅ Use quotation marks and copy the text character-for-character including punctuation
   - ✅ If no supporting text exists in the report, use an empty quotes array: "quotes": []
   
   **EXAMPLE**:
   ❌ Wrong: "quotes": ["Patient meets BCLC Stage A criteria due to single lesion"]
   ✅ Correct: "quotes": ["Single 3.2 cm lesion in segment VI of the liver."]
   
   **REMEMBER**: You are extracting information, NOT creating supporting evidence. Every quote must be traceable to the exact location in the original report.

Return JSON with ALL questions:
{{
  "results": [
    {{"question": "0.1 BCLC Staging", "answer": "your answer", "confidence": 8, "quotes": []}},
    {{"question": "0.2 Child-Pugh score", "answer": "your answer", "confidence": 7, "quotes": []}},
    {{"question": "0.3 MELD score", "answer": "your answer", "confidence": 9, "quotes": []}},
    {{"question": "0.4 ALBI score", "answer": "your answer", "confidence": 8, "quotes": []}},
    {{"question": "1.1 Age", "answer": "your answer", "confidence": 9, "quotes": ["quote1"]}},
    {{"question": "1.2 Gender", "answer": "your answer", "confidence": 9, "quotes": ["quote1"]}},
    {{"question": "2.1 Number of confirmed HCC", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "2.2 Largest size of HCC", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "3 Child-Pugh classification", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "4.1 ECOG performance status", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "4.2 Extent of physical activity restriction", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "4.3 Activity status", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "4.4 Deceased or expired", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "5.1 Grade of encephalopathy", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "5.2 Diagnosis of encephalopathy", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "5.3 Mental status", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "5.4 Level of consciousness", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "6.1 Grade of ascites", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "6.2 Diagnosis of ascites", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "6.3 Discharge for ascites", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "7.1 Total bilirubin", "answer": "your answer", "confidence": 9, "quotes": ["quote1"]}},
    {{"question": "7.2 Albumin", "answer": "your answer", "confidence": 9, "quotes": ["quote1"]}},
    {{"question": "7.3 Sodium", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "7.4 Prothrombin time", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "7.5 International normalized ratio", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "7.6 Creatinine", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "7.7 Alpha-fetoprotein level", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "7.8 Indocyanine green retention at 15 minutes", "answer": "your answer", "confidence": 5, "quotes": ["quote1"]}},
    {{"question": "8.1 Presence of macrovascular invasion", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "8.2 Presence of lymphovascular invasion", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "8.3 Extent of invasion", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "8.4 Presence of portal vein thrombosis", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "9.1 Presence of metastatic lymph node", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "9.2 Presence of extrahepatic spread", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "9.3 Presence of metastatic HCC", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "9.4 Site of distant metastasis", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "10.1 Presence of cirrhosis", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "10.2 Severe comorbidities", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "11 Prior treatment received", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "12 Liver transplant", "answer": "your answer", "confidence": 8, "quotes": ["quote1"]}},
    {{"question": "13.1 Histological tumour grade", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "13.2 Histological tumour type", "answer": "your answer", "confidence": 7, "quotes": ["quote1"]}},
    {{"question": "13.3 Margin status", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}},
    {{"question": "13.4 Presence of satellitosis", "answer": "your answer", "confidence": 6, "quotes": ["quote1"]}}
  ]
}}

IMPORTANT: Return ONLY the JSON response, no additional text or explanations."""
    
    headers = {
        'Content-Type': 'application/json',
        'api-key': AZURE_OPENAI_API_KEY
    }
    
    data = {
        "messages": [
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "max_tokens": 3000,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data, timeout=200)
        response.raise_for_status()
        
        result = response.json()
        content = result['choices'][0]['message']['content'].strip()
        
        # 尝试解析JSON
        try:
            json_data = json.loads(content)
            return json_data
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_data = json.loads(json_match.group())
                return json_data
            else:
                print(f"❌ 无法解析JSON响应")
                return None
                
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def validate_quotes(analysis_result, original_content):
    """验证引用的有效性"""
    if not analysis_result or 'results' not in analysis_result:
        return {'total': 0, 'valid': 0, 'invalid': 0, 'validity_rate': 0}

    total_quotes = 0
    valid_quotes = 0

    for result in analysis_result['results']:
        if 'quotes' in result and result['quotes']:
            for quote in result['quotes']:
                total_quotes += 1
                # 检查引用是否在原文中存在
                if quote.strip() and quote.strip() in original_content:
                    valid_quotes += 1

    invalid_quotes = total_quotes - valid_quotes
    validity_rate = round((valid_quotes / total_quotes * 100) if total_quotes > 0 else 0, 1)

    return {
        'total': total_quotes,
        'valid': valid_quotes,
        'invalid': invalid_quotes,
        'validity_rate': validity_rate
    }

def process_single_file(file_path, age_dict=None):
    """处理单个HCC医学报告文件"""
    try:
        filename = os.path.basename(file_path)
        print(f"\n🔍 分析文件: {filename} ", end="", flush=True)

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if not content.strip():
            print("❌ 文件为空")
            return None

        # 使用Azure GPT-4分析
        analysis_result = analyze_with_azure_gpt4_hcc(content, filename, age_dict)

        if analysis_result:
            # 验证引用
            validation_stats = validate_quotes(analysis_result, content)
            print(f"✅ 完成 (引用有效率: {validation_stats['validity_rate']}%)")

            # 获取年龄信息
            age_info = None
            if age_dict:
                age_info = get_age_from_excel(filename, age_dict)

            # 生成HTML报告
            html_generator = HTMLReportGenerator()
            html_content = html_generator.create_html_report(
                filename,
                content,
                analysis_result,
                age_info,
                validation_stats
            )

            # 保存HTML文件
            output_filename = f"{os.path.splitext(filename)[0]}_HCC_analysis.html"
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"📄 HTML报告已保存: {output_filename}")
            return analysis_result
        else:
            print("❌ 分析失败")
            return None

    except Exception as e:
        print(f"❌ 处理文件失败: {e}")
        return None

def main():
    """主函数"""
    print("🏥 HCC医学报告分析系统启动")
    print("=" * 50)

    # 加载年龄数据
    age_dict = load_age_data()

    # 查找所有txt文件
    txt_files = glob.glob("*.txt")

    if not txt_files:
        print("❌ 未找到任何.txt文件")
        return

    print(f"📁 找到 {len(txt_files)} 个文件待处理")

    successful_analyses = 0
    failed_analyses = 0

    for file_path in txt_files:
        result = process_single_file(file_path, age_dict)
        if result:
            successful_analyses += 1
        else:
            failed_analyses += 1

        # 添加延迟避免API限制
        time.sleep(1)

    print("\n" + "=" * 50)
    print(f"📊 分析完成统计:")
    print(f"   ✅ 成功: {successful_analyses}")
    print(f"   ❌ 失败: {failed_analyses}")
    print(f"   📈 成功率: {successful_analyses/(successful_analyses+failed_analyses)*100:.1f}%")

if __name__ == "__main__":
    main()
