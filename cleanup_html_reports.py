import os

def cleanup_html_reports():
    """删除html_reports_20250715中不需要的case文件"""
    
    # 获取extracted_cases_20250715中的case列表
    extracted_dir = "extracted_cases_20250715"
    html_dir = "html_reports_20250715"
    
    if not os.path.exists(extracted_dir):
        print(f"❌ 目录不存在: {extracted_dir}")
        return
    
    if not os.path.exists(html_dir):
        print(f"❌ 目录不存在: {html_dir}")
        return
    
    # 获取extracted_cases中的case ID列表
    extracted_cases = set()
    for filename in os.listdir(extracted_dir):
        if filename.endswith('.txt'):
            case_id = filename.replace('.txt', '')
            extracted_cases.add(case_id)
    
    print(f"📁 extracted_cases_20250715 中有 {len(extracted_cases)} 个案例")
    
    # 获取html_reports中的文件列表
    html_files = []
    for filename in os.listdir(html_dir):
        if filename.endswith('.html') and filename.startswith('hcc_analysis_'):
            html_files.append(filename)
    
    print(f"📁 html_reports_20250715 中有 {len(html_files)} 个HTML文件")
    
    # 找出需要删除的文件
    files_to_delete = []
    files_to_keep = []
    
    for html_file in html_files:
        # 从HTML文件名提取case ID
        case_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
        
        if case_id in extracted_cases:
            files_to_keep.append(html_file)
        else:
            files_to_delete.append(html_file)
    
    print(f"\n📊 分析结果:")
    print(f"✅ 需要保留的文件: {len(files_to_keep)} 个")
    print(f"🗑️  需要删除的文件: {len(files_to_delete)} 个")
    
    if files_to_delete:
        print(f"\n🗑️  将要删除的文件:")
        for file in sorted(files_to_delete):
            print(f"  - {file}")
        
        # 确认删除
        confirm = input(f"\n确认删除这 {len(files_to_delete)} 个文件吗? (y/N): ")
        
        if confirm.lower() == 'y':
            deleted_count = 0
            for file in files_to_delete:
                file_path = os.path.join(html_dir, file)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    print(f"✅ 已删除: {file}")
                except Exception as e:
                    print(f"❌ 删除失败 {file}: {e}")
            
            print(f"\n🎉 清理完成! 成功删除 {deleted_count} 个文件")
            print(f"📁 html_reports_20250715 现在有 {len(files_to_keep)} 个文件")
        else:
            print("❌ 取消删除操作")
    else:
        print("\n✅ 没有需要删除的文件，所有HTML文件都对应extracted_cases中的案例")
    
    # 显示保留的文件列表
    if files_to_keep:
        print(f"\n📋 保留的文件列表:")
        for file in sorted(files_to_keep):
            case_id = file.replace('hcc_analysis_', '').replace('.html', '')
            print(f"  ✅ {file} -> {case_id}")

if __name__ == "__main__":
    cleanup_html_reports()