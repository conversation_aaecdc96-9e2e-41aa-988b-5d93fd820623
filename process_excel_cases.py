#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理excel_based_analysis_20cases中的20个案例
使用更新后的hcc_analyzer_simple.py，添加1-13大标题分节
"""

import os
import sys
from hcc_analyzer_simple import analyze_with_gpt4, generate_simple_html, clean_medical_text

def process_excel_cases():
    """处理excel_based_analysis_20cases中的所有案例"""
    
    # 获取需要处理的案例列表
    excel_cases = []
    excel_dir = 'excel_based_analysis_20cases'
    
    if not os.path.exists(excel_dir):
        print(f"❌ 文件夹不存在: {excel_dir}")
        return
        
    for f in os.listdir(excel_dir):
        if f.endswith('.html') and f.startswith('hcc_analysis_'):
            case_id = f.replace('hcc_analysis_', '').replace('.html', '')
            excel_cases.append(case_id)
    
    excel_cases = sorted(excel_cases)
    print(f"📊 找到 {len(excel_cases)} 个需要处理的案例")
    
    # 检查所有txt文件是否存在
    missing_files = []
    for case_id in excel_cases:
        txt_file = f"txts/{case_id}.txt"
        if not os.path.exists(txt_file):
            missing_files.append(txt_file)
    
    if missing_files:
        print(f"❌ 缺失 {len(missing_files)} 个txt文件:")
        for f in missing_files:
            print(f"  {f}")
        return
    
    print("✅ 所有txt文件都存在，开始处理...")
    
    # 处理每个案例
    success_count = 0
    for i, case_id in enumerate(excel_cases, 1):
        print(f"\n🔍 处理案例 {i}/{len(excel_cases)}: {case_id}")
        
        txt_file = f"txts/{case_id}.txt"
        output_file = f"{excel_dir}/hcc_analysis_{case_id}.html"
        
        try:
            # 读取txt文件
            with open(txt_file, 'r', encoding='utf-8') as f:
                text_content = f.read()
            
            print(f"📄 文件大小: {len(text_content):,} 字符")
            
            # 分析文本
            print("🤖 分析中...", end="", flush=True)
            analysis_response = analyze_with_gpt4(text_content, case_id)
            print(" 完成")
            
            # 生成HTML报告
            print("📝 生成HTML报告...", end="", flush=True)
            html_content = generate_simple_html(text_content, analysis_response, case_id)
            print(" 完成")
            
            # 保存HTML文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ 成功保存: {output_file}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
            continue
    
    print(f"\n🎉 处理完成！成功: {success_count}/{len(excel_cases)}")

if __name__ == "__main__":
    process_excel_cases() 