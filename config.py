#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - Azure OpenAI API 和文件路径配置
"""

# Azure OpenAI API 配置
AZURE_CONFIG = {
    'api_key': 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte',
    'endpoint': 'https://nerllm.openai.azure.com',
    'deployment': 'gpt-4.1',
    'api_version': '2025-01-01-preview'
}

# 文件路径配置
PATHS = {
    'excel_file': "TCGA-THCA_GPT4.1_20250609 TW.xlsx",
    'input_dir': "./txts/txts/",
    'output_dir': "./html_reports_expert/"
}

# 分析参数配置
ANALYSIS_CONFIG = {
    'max_content_length': 10000,
    'request_timeout': 200,
    'temperature': 0.1,
    'max_tokens': 3000
} 