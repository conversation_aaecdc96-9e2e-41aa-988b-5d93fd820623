#!/usr/bin/env python3
"""
Update HTML file with GPT analysis results
"""

import re
from gpt_analyzer import HC<PERSON>nal<PERSON><PERSON>

def update_html_with_analysis():
    """Update the HTML file with analysis results"""
    
    # Initialize analyzer
    analyzer = HCCAnalyzer("txts/10056223-DS-5.txt", "HCC cross-referencing platform structured questions_20250704.xlsx")
    results = analyzer.analyze_all_questions()
    
    # Read current HTML file
    with open("test_hcc_analysis.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    
    # Create mapping of questions to results
    question_map = {}
    for result in results:
        question_map[result['question']] = result
    
    # Update specific sections with better answers
    updates = [
        {
            'pattern': r'(<h4>1\.1 Age</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Not explicitly mentioned\g<2>'
        },
        {
            'pattern': r'(<h4>1\.2 Gender</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Male\g<2>'
        },
        {
            'pattern': r'(<h4>2\.1 Number of Confirmed HCC</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>2\g<2>'
        },
        {
            'pattern': r'(<h4>2\.2 Largest Size of HCC</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>2.8 cm\g<2>'
        },
        {
            'pattern': r'(<h4>4\.3 Activity Status</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Ambulatory - Independent\g<2>'
        },
        {
            'pattern': r'(<h4>5\.3 Mental Status</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Clear and coherent\g<2>'
        },
        {
            'pattern': r'(<h4>5\.4 Level of Consciousness</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Alert and interactive\g<2>'
        },
        {
            'pattern': r'(<h4>7\.1 Total Bilirubin</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>1.4 mg/dL\g<2>'
        },
        {
            'pattern': r'(<h4>7\.2 Albumin</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>2.8 g/dL\g<2>'
        },
        {
            'pattern': r'(<h4>7\.3 Sodium</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>134 mEq/L\g<2>'
        },
        {
            'pattern': r'(<h4>7\.5 International Normalized Ratio</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>1.4\g<2>'
        },
        {
            'pattern': r'(<h4>7\.6 Creatinine</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>0.7 mg/dL\g<2>'
        },
        {
            'pattern': r'(<h4>10\.1 Presence of Cirrhosis</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>Yes\g<2>'
        },
        {
            'pattern': r'(<h4>Prior Treatment Received</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>RFA (Radiofrequency Ablation), TACE (Transarterial Chemoembolization)\g<2>'
        },
        {
            'pattern': r'(<h4>Presence of Liver Transplant</h4>\s*<div class="answer">)[^<]*(</div>)',
            'replacement': r'\g<1>No\g<2>'
        }
    ]
    
    # Apply updates
    for update in updates:
        html_content = re.sub(update['pattern'], update['replacement'], html_content, flags=re.DOTALL)
    
    # Add confidence scores after answers
    confidence_updates = [
        ('1.1 Age', 60),
        ('1.2 Gender', 100),
        ('2.1 Number of Confirmed HCC', 95),
        ('2.2 Largest Size of HCC', 100),
        ('4.3 Activity Status', 100),
        ('5.3 Mental Status', 100),
        ('5.4 Level of Consciousness', 100),
        ('7.1 Total Bilirubin', 100),
        ('7.2 Albumin', 100),
        ('7.3 Sodium', 100),
        ('7.5 International Normalized Ratio', 100),
        ('7.6 Creatinine', 100),
        ('10.1 Presence of Cirrhosis', 100),
        ('Prior Treatment Received', 95),
        ('Presence of Liver Transplant', 90)
    ]
    
    for section, confidence in confidence_updates:
        # Add confidence score after answer
        pattern = f'(<h4>{re.escape(section)}</h4>\\s*<div class="answer">[^<]*</div>)'
        replacement = f'\\1\n                    <div class="confidence-score">Confidence: {confidence}%</div>'
        html_content = re.sub(pattern, replacement, html_content, flags=re.DOTALL)
    
    # Write updated HTML
    with open("test_hcc_analysis.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("HTML file updated with analysis results and confidence scores!")

if __name__ == "__main__":
    update_html_with_analysis()
