#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析前四个问题的准确性
"""

import os
import re
import pandas as pd
from bs4 import BeautifulSoup

def extract_answers_from_html(html_file):
    """从HTML文件中提取前四个问题的答案"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 提取前四个问题的答案
        answers = {}
        
        # 找到所有的score-section
        score_sections = soup.find_all('div', class_='score-section')
        
        for section in score_sections:
            h4 = section.find('h4')
            if h4:
                title = h4.text.strip()
                # 查找包含"Answer:"的div，使用正则表达式提取答案
                section_text = section.get_text()
                
                # 使用正则表达式提取Answer:后面的内容
                match = re.search(r'Answer:\s*([^\n]+)', section_text)
                if match:
                    answer = match.group(1).strip()
                    answers[title] = answer
        
        return {
            'BCLC_staging': answers.get('BCLC Staging', 'Not found'),
            'Child_Pugh_score': answers.get('Child-Pugh score', 'Not found'),
            'MELD_score': answers.get('MELD score', 'Not found'),
            'ALBI_score': answers.get('ALBI score', 'Not found')
        }
    except Exception as e:
        print(f"Error processing {html_file}: {e}")
        return {
            'BCLC_staging': 'Error',
            'Child_Pugh_score': 'Error', 
            'MELD_score': 'Error',
            'ALBI_score': 'Error'
        }

def parse_ground_truth(ground_truth_data):
    """解析标准答案数据"""
    # 解析标准答案数据
    lines = ground_truth_data.strip().split('\n')
    ground_truth = {}
    
    for line in lines:
        # 使用制表符分割，但是考虑到数据中可能有多个连续的制表符
        parts = [part.strip() for part in line.split('\t') if part.strip()]
        if len(parts) >= 6:
            case_id = parts[1]
            question_num = parts[2]
            question_name = parts[4]
            answer = parts[5]
            
            if case_id not in ground_truth:
                ground_truth[case_id] = {}
            
            if question_num == '1' and 'BCLC staging' in question_name:
                ground_truth[case_id]['BCLC_staging'] = answer
            elif question_num == '2' and 'Child-Pugh score' in question_name:
                ground_truth[case_id]['Child_Pugh_score'] = answer
            elif question_num == '3' and 'MELD score' in question_name:
                ground_truth[case_id]['MELD_score'] = answer
            elif question_num == '4' and 'ALBI score' in question_name:
                ground_truth[case_id]['ALBI_score'] = answer
    
    return ground_truth

def normalize_answer(answer):
    """标准化答案格式"""
    if not answer or answer == 'Not found' or answer == 'Error':
        return answer
    
    answer = str(answer).strip()
    
    # 处理Child-Pugh分数
    if 'Class' in answer or 'class' in answer:
        if 'A' in answer:
            return 'A'
        elif 'B' in answer:
            return 'B'
        elif 'C' in answer:
            return 'C'
    
    # 处理BCLC分期
    if answer in ['0', 'A', 'B', 'C', 'D']:
        return answer
    
    # 处理数值
    if answer.replace('-', '').replace('.', '').isdigit():
        return answer
    
    # 处理特殊情况
    if answer == '-':
        return '-'
    
    return answer

def compare_answers(predicted, ground_truth):
    """比较预测答案和标准答案"""
    predicted_norm = normalize_answer(predicted)
    ground_truth_norm = normalize_answer(ground_truth)
    
    # 精确匹配
    if predicted_norm == ground_truth_norm:
        return True
    
    # 处理数值答案的近似匹配
    try:
        pred_float = float(predicted_norm)
        truth_float = float(ground_truth_norm)
        # 允许小的数值差异
        if abs(pred_float - truth_float) <= 0.1:
            return True
    except:
        pass
    
    # 处理Child-Pugh特殊情况
    if 'B/C' in ground_truth_norm:
        if predicted_norm in ['B', 'C']:
            return True
    
    return False

def main():
    # 标准答案数据
    ground_truth_data = """1	10056223-DS-5	1	0.1	BCLC staging					A														
1	10056223-DS-5	2	0.2	Child-Pugh score					B														
1	10056223-DS-5	3	0.3	MELD score					13														
1	10056223-DS-5	4	0.4	ALBI score					-1.37														
2	10151324-DS-16	1	0.1	BCLC staging					A														
2	10151324-DS-16	2	0.2	Child-Pugh score					A														
2	10151324-DS-16	3	0.3	MELD score					-														
2	10151324-DS-16	4	0.4	ALBI score					-														
3	10151324-DS-18	1	0.1	BCLC staging					A														
3	10151324-DS-18	2	0.2	Child-Pugh score					A														
3	10151324-DS-18	3	0.3	MELD score					9														
3	10151324-DS-18	4	0.4	ALBI score					-2.79														
4	10225793-DS-33	1	0.1	BCLC staging					0														
4	10225793-DS-33	2	0.2	Child-Pugh score					B														
4	10225793-DS-33	3	0.3	MELD score					15														
4	10225793-DS-33	4	0.4	ALBI score					-														
5	10225793-DS-34	1	0.1	BCLC staging					0														
5	10225793-DS-34	2	0.2	Child-Pugh score					C														
5	10225793-DS-34	3	0.3	MELD score					14														
5	10225793-DS-34	4	0.4	ALBI score					-1.66														
6	10225793-DS-36	1	0.1	BCLC staging					0														
6	10225793-DS-36	2	0.2	Child-Pugh score					C														
6	10225793-DS-36	3	0.3	MELD score					15														
6	10225793-DS-36	4	0.4	ALBI score					-1.22														
7	10262565-DS-19	1	0.1	BCLC staging					D														
7	10262565-DS-19	2	0.2	Child-Pugh score					B														
7	10262565-DS-19	3	0.3	MELD score					-														
7	10262565-DS-19	4	0.4	ALBI score					-1.48														
8	10262565-DS-20	1	0.1	BCLC staging					D														
8	10262565-DS-20	2	0.2	Child-Pugh score					B/C														
8	10262565-DS-20	3	0.3	MELD score					-														
8	10262565-DS-20	4	0.4	ALBI score					-														
9	10388675-DS-19	1	0.1	BCLC staging					D														
9	10388675-DS-19	2	0.2	Child-Pugh score					B														
9	10388675-DS-19	3	0.3	MELD score					-														
9	10388675-DS-19	4	0.4	ALBI score					-														
10	10388675-DS-20	1	0.1	BCLC staging					D														
10	10388675-DS-20	2	0.2	Child-Pugh score					B														
10	10388675-DS-20	3	0.3	MELD score					-														
10	10388675-DS-20	4	0.4	ALBI score					-1.75														
11	10666715-DS-8	1	0.1	BCLC staging					B														
11	10666715-DS-8	2	0.2	Child-Pugh score					A														
11	10666715-DS-8	3	0.3	MELD score					-														
11	10666715-DS-8	4	0.4	ALBI score					-1.83														
12	10747475-DS-4	1	0.1	BCLC staging					A														
12	10747475-DS-4	2	0.2	Child-Pugh score					B														
12	10747475-DS-4	3	0.3	MELD score					13														
12	10747475-DS-4	4	0.4	ALBI score					-1.43														
13	10760122-DS-21	1	0.1	BCLC staging					D														
13	10760122-DS-21	2	0.2	Child-Pugh score					B														
13	10760122-DS-21	3	0.3	MELD score					-														
13	10760122-DS-21	4	0.4	ALBI score					-														
14	10880579-DS-11	1	0.1	BCLC staging					D														
14	10880579-DS-11	2	0.2	Child-Pugh score					B														
14	10880579-DS-11	3	0.3	MELD score					19														
14	10880579-DS-11	4	0.4	ALBI score					-1.02														
15	10902714-DS-17	1	0.1	BCLC staging					D														
15	10902714-DS-17	2	0.2	Child-Pugh score					B														
15	10902714-DS-17	3	0.3	MELD score					-														
15	10902714-DS-17	4	0.4	ALBI score					-2.67														
16	10923555-DS-9	1	0.1	BCLC staging					A														
16	10923555-DS-9	2	0.2	Child-Pugh score					B														
16	10923555-DS-9	3	0.3	MELD score					-														
16	10923555-DS-9	4	0.4	ALBI score					-1.3														
17	10960817-DS-14	1	0.1	BCLC staging					D														
17	10960817-DS-14	2	0.2	Child-Pugh score					B														
17	10960817-DS-14	3	0.3	MELD score					22														
17	10960817-DS-14	4	0.4	ALBI score					-2.35														
18	11102747-DS-2	1	0.1	BCLC staging					B														
18	11102747-DS-2	2	0.2	Child-Pugh score					A														
18	11102747-DS-2	3	0.3	MELD score					12														
18	11102747-DS-2	4	0.4	ALBI score					-1.91														
19	11198012-DS-6	1	0.1	BCLC staging					C														
19	11198012-DS-6	2	0.2	Child-Pugh score					A														
19	11198012-DS-6	3	0.3	MELD score					11														
19	11198012-DS-6	4	0.4	ALBI score					-2.32														
20	11198012-DS-7	1	0.1	BCLC staging					C														
20	11198012-DS-7	2	0.2	Child-Pugh score					B														
20	11198012-DS-7	3	0.3	MELD score					-														
20	11198012-DS-7	4	0.4	ALBI score					-1.75"""
    
    # 解析标准答案
    ground_truth = parse_ground_truth(ground_truth_data)
    
    # HTML文件目录
    html_dir = "html_reports_split_cases_20250104"
    
    # 提取所有答案
    results = []
    
    for case_id in ground_truth.keys():
        html_file = os.path.join(html_dir, f"hcc_analysis_{case_id}.html")
        
        if os.path.exists(html_file):
            predicted_answers = extract_answers_from_html(html_file)
            ground_truth_answers = ground_truth[case_id]
            
            # 比较每个问题
            for question in ['BCLC_staging', 'Child_Pugh_score', 'MELD_score', 'ALBI_score']:
                predicted = predicted_answers.get(question, 'Not found')
                truth = ground_truth_answers.get(question, 'Not found')
                
                match = compare_answers(predicted, truth)
                
                results.append({
                    'case_id': case_id,
                    'question': question,
                    'predicted': predicted,
                    'ground_truth': truth,
                    'match': match
                })
        else:
            print(f"HTML文件不存在: {html_file}")
    
    # 计算准确率
    total_questions = len(results)
    correct_answers = sum(1 for r in results if r['match'])
    accuracy = correct_answers / total_questions * 100
    
    print(f"\n=== 前四个问题准确性分析 ===")
    print(f"总问题数: {total_questions}")
    print(f"正确答案数: {correct_answers}")
    print(f"总体准确率: {accuracy:.1f}%")
    
    # 按问题类型分析
    print(f"\n=== 各问题准确率 ===")
    for question in ['BCLC_staging', 'Child_Pugh_score', 'MELD_score', 'ALBI_score']:
        question_results = [r for r in results if r['question'] == question]
        question_correct = sum(1 for r in question_results if r['match'])
        question_total = len(question_results)
        question_accuracy = question_correct / question_total * 100
        
        print(f"{question}: {question_correct}/{question_total} ({question_accuracy:.1f}%)")
    
    # 显示错误案例
    print(f"\n=== 错误案例详情 ===")
    for result in results:
        if not result['match']:
            print(f"Case {result['case_id']}: {result['question']}")
            print(f"  预测: {result['predicted']}")
            print(f"  标准: {result['ground_truth']}")
            print()
    
    # 保存详细结果
    df = pd.DataFrame(results)
    df.to_csv('accuracy_analysis_results.csv', index=False, encoding='utf-8')
    print(f"详细结果已保存到 accuracy_analysis_results.csv")

if __name__ == "__main__":
    main() 