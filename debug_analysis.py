#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版本：查看标准答案解析过程
"""

def parse_ground_truth(ground_truth_data):
    """解析标准答案数据"""
    # 解析标准答案数据
    lines = ground_truth_data.strip().split('\n')
    ground_truth = {}
    
    for i, line in enumerate(lines[:5]):  # 只处理前5行进行调试
        print(f"Line {i+1}: {repr(line)}")
        # 使用制表符分割，但是考虑到数据中可能有多个连续的制表符
        parts = [part.strip() for part in line.split('\t') if part.strip()]
        print(f"Parts: {parts}")
        print(f"Parts length: {len(parts)}")
        
        if len(parts) >= 6:
            case_id = parts[1]
            question_num = parts[2]
            question_name = parts[4]
            answer = parts[5] if len(parts) > 5 else ""
            
            print(f"Case ID: {case_id}")
            print(f"Question num: {question_num}")
            print(f"Question name: {question_name}")
            print(f"Answer: {answer}")
            
            if case_id not in ground_truth:
                ground_truth[case_id] = {}
            
            if question_num == '1' and 'BCLC staging' in question_name:
                ground_truth[case_id]['BCLC_staging'] = answer
            elif question_num == '2' and 'Child-Pugh score' in question_name:
                ground_truth[case_id]['Child_Pugh_score'] = answer
            elif question_num == '3' and 'MELD score' in question_name:
                ground_truth[case_id]['MELD_score'] = answer
            elif question_num == '4' and 'ALBI score' in question_name:
                ground_truth[case_id]['ALBI_score'] = answer
        print("-" * 50)
    
    return ground_truth

def main():
    # 标准答案数据
    ground_truth_data = """1	10056223-DS-5	1	0.1	BCLC staging					A														
1	10056223-DS-5	2	0.2	Child-Pugh score					B														
1	10056223-DS-5	3	0.3	MELD score					13														
1	10056223-DS-5	4	0.4	ALBI score					-1.37														
2	10151324-DS-16	1	0.1	BCLC staging					A"""
    
    ground_truth = parse_ground_truth(ground_truth_data)
    print("Ground truth parsed:")
    print(ground_truth)

if __name__ == "__main__":
    main() 