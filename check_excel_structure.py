#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MIMIC_HCC_GroundTruth.xlsx的结构
"""

import pandas as pd

# Excel文件路径
EXCEL_PATH = r"C:\Users\<USER>\Desktop\case_new\MIMIC_HCC_GroundTruth.xlsx"

def main():
    """主函数"""
    try:
        # 读取Excel文件
        print(f"📊 读取Excel文件: {EXCEL_PATH}")
        df = pd.read_excel(EXCEL_PATH)
        
        # 打印列名
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"{i}: {col}")
        
        # 打印前几行数据，查看文件ID列
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否有包含文件ID的列
        possible_id_columns = []
        for col in df.columns:
            if any(str(val).endswith('-DS-5') or str(val).endswith('-DS-6') for val in df[col].dropna().values):
                possible_id_columns.append(col)
        
        print("\n可能包含文件ID的列:")
        for col in possible_id_columns:
            print(f"- {col}")
            # 打印该列的前几个非空值
            non_null_values = df[col].dropna().values[:5]
            print(f"  示例值: {', '.join(str(val) for val in non_null_values)}")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
