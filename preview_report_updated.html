<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expert Azure GPT-4 Analysis - Sample Case (Updated)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title { font-size: 18px; font-weight: 600; }
        .expert-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }
        
        .panel {
            width: 50%;
            background: white;
            display: flex;
            flex-direction: column;
        }
        
        .left-panel { border-right: 2px solid #e0e6ed; }
        
        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .pathology-title {
            color: #2c5aa0;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e6ed;
        }

        .original-content {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
            white-space: pre-wrap;
        }

        .section-content .subsection-item {
            font-size: 18px !important;
            margin-bottom: 15px;
        }

        .section-content .question-item h4 {
            font-size: 18px !important;
            color: #333;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .classification-summary {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.1);
        }

        .summary-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            line-height: 1.6;
            text-transform: capitalize;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .section-number {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            margin-right: 12px;
        }

        .answer-item {
            margin-bottom: 15px;
        }

        .answer-item .label {
            color: #666;
            font-size: 16px !important;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .answer-item .value {
            color: #333;
            font-size: 16px !important;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-weight: 500;
        }

        .quotes-container {
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px !important;
            color: #666;
            line-height: 1.6;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c5aa0;
        }

        .content-line {
            display: inline;
            transition: background-color 0.3s ease;
        }
        
        .content-line.highlighted {
            background-color: #ff9500 !important;
            color: white !important;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
        }
        
        .quote-link {
            cursor: pointer;
            color: #2c5aa0;
            text-decoration: underline;
            transition: all 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 0 2px;
        }
        
        .quote-link:hover {
            background-color: #e3f2fd;
            color: #1565c0;
            transform: translateY(-1px);
        }
        
        .quote-link.active {
            background-color: #ff9500;
            color: white;
            font-weight: bold;
        }
        
        @keyframes highlight-flash {
            0% { box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.7); }
            50% { box-shadow: 0 0 0 10px rgba(255, 149, 0, 0.3); }
            100% { box-shadow: 0 0 0 0 rgba(255, 149, 0, 0); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">Expert Pathology Analysis (AJCC 8th Edition TNM + ATA)</div>
        <div class="expert-badge">🩺 Azure GPT-4 Expert</div>
    </div>
    
    <div class="main-container">
        <div class="panel left-panel">
            <div class="panel-content">
                <h2 class="pathology-title">Pathology Report</h2>
                <div class="original-content">SURGICAL PATHOLOGY REPORT

CLINICAL HISTORY:
45-year-old female with thyroid nodule.

SPECIMEN:
Total thyroidectomy specimen

GROSS DESCRIPTION:
The specimen consists of a total thyroidectomy measuring 5.0 x 3.5 x 2.5 cm. Sectioning reveals a well-circumscribed, tan-white nodule in the right lobe measuring 2.8 cm in greatest dimension. The nodule appears encapsulated. The remaining thyroid tissue appears unremarkable.

MICROSCOPIC DESCRIPTION:
Sections show a well-circumscribed papillary thyroid carcinoma with classical features. The tumor measures 2.8 cm in greatest dimension. There is no evidence of extrathyroidal extension. Lymphovascular invasion is identified. The tumor is completely encapsulated.

Regional lymph nodes examination reveals 3 of 8 lymph nodes positive for metastatic papillary thyroid carcinoma. The largest metastatic focus measures 0.5 cm.

IMMUNOHISTOCHEMISTRY:
BRAF V600E: Positive

DIAGNOSIS:
Total thyroidectomy:
- Classical papillary thyroid carcinoma
- Tumor size: 2.8 cm
- No extrathyroidal extension
- Lymphovascular invasion present
- Surgical margins negative
- Lymph nodes: 3/8 positive
- BRAF V600E mutation positive

STAGING:
- pT2N1aM0
- AJCC 8th Edition Stage I</div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-content">
                <div class="classification-summary">
                    <h2>📋 Thyroid Cancer Classification Summary</h2>
                    <div class="summary-content">Classical papillary thyroid carcinoma, pT2N1aM0 (AJCC 8 Stage I), ATA (2015) Intermediate Risk</div>
                </div>

                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-number">1</div>
                        <h2 class="section-title">Type of surgery</h2>
                    </div>
                    <div class="section-content">
                        <div class="question-item">
                            <h4>1.1 Surgical Procedure</h4>
                            <div class="answer-item">
                                <div class="label">Answer</div>
                                <div class="value">Total thyroidectomy</div>
                                <div class="quotes-container">
                                    <span class="quote-link" data-quote="Total thyroidectomy specimen" onclick="highlightQuote(this)">"Total thyroidectomy specimen"</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-number">2</div>
                        <h2 class="section-title">Tumor Characteristics</h2>
                    </div>
                    <div class="section-content">
                        <div class="question-item">
                            <h4>2.1 Tumor Size</h4>
                            <div class="answer-item">
                                <div class="label">Answer</div>
                                <div class="value">2.8 cm</div>
                                <div class="quotes-container">
                                    <span class="quote-link" data-quote="The tumor measures 2.8 cm in greatest dimension" onclick="highlightQuote(this)">"The tumor measures 2.8 cm in greatest dimension"</span>
                                </div>
                            </div>
                        </div>
                        <div class="question-item">
                            <h4>2.2 Extrathyroidal Extension</h4>
                            <div class="answer-item">
                                <div class="label">Answer</div>
                                <div class="value">Not present</div>
                                <div class="quotes-container">
                                    <span class="quote-link" data-quote="There is no evidence of extrathyroidal extension" onclick="highlightQuote(this)">"There is no evidence of extrathyroidal extension"</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-number">3</div>
                        <h2 class="section-title">Lymph Node Status</h2>
                    </div>
                    <div class="section-content">
                        <div class="question-item">
                            <h4>3.1 Lymph Node Involvement</h4>
                            <div class="answer-item">
                                <div class="label">Answer</div>
                                <div class="value">3 of 8 lymph nodes positive</div>
                                <div class="quotes-container">
                                    <span class="quote-link" data-quote="Regional lymph nodes examination reveals 3 of 8 lymph nodes positive for metastatic papillary thyroid carcinoma" onclick="highlightQuote(this)">"Regional lymph nodes examination reveals 3 of 8 lymph nodes positive for metastatic papillary thyroid carcinoma"</span>
                                </div>
                            </div>
                        </div>
                        <div class="question-item">
                            <h4>3.2 Largest Metastatic Focus</h4>
                            <div class="answer-item">
                                <div class="label">Answer</div>
                                <div class="value">0.5 cm</div>
                                <div class="quotes-container">
                                    <span class="quote-link" data-quote="The largest metastatic focus measures 0.5 cm" onclick="highlightQuote(this)">"The largest metastatic focus measures 0.5 cm"</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更多分析部分... -->
            </div>
        </div>
    </div>

    <script>
        // 智能高亮和滚动功能
        let currentHighlighted = null;
        let currentActiveQuote = null;
        
        function highlightQuote(element) {
            const quote = element.getAttribute('data-quote');
            if (!quote || quote.trim() === '') return;
            
            // 清除之前的高亮
            clearHighlights();
            
            // 设置当前活跃的quote
            if (currentActiveQuote) {
                currentActiveQuote.classList.remove('active');
            }
            currentActiveQuote = element;
            element.classList.add('active');
            
            // 在左侧内容中搜索并高亮匹配的文本
            const originalContent = document.querySelector('.original-content');
            const contentLines = originalContent.querySelectorAll('.content-line');
            
            let bestMatch = null;
            let bestScore = 0;
            
            // 智能匹配算法
            for (let line of contentLines) {
                const lineText = line.textContent.toLowerCase();
                const quoteText = quote.toLowerCase();
                
                // 直接匹配
                if (lineText.includes(quoteText)) {
                    bestMatch = line;
                    bestScore = 100;
                    break;
                }
                
                // 部分匹配 - 计算相似度
                const words = quoteText.split(/\s+/).filter(w => w.length > 2);
                let matchedWords = 0;
                
                for (let word of words) {
                    if (lineText.includes(word)) {
                        matchedWords++;
                    }
                }
                
                const score = (matchedWords / words.length) * 100;
                if (score > bestScore && score > 30) {
                    bestMatch = line;
                    bestScore = score;
                }
            }
            
            // 如果找到匹配，高亮并滚动
            if (bestMatch) {
                bestMatch.classList.add('highlighted');
                currentHighlighted = bestMatch;
                
                // 滚动到匹配位置
                const leftPanel = document.querySelector('.left-panel .panel-content');
                const lineTop = bestMatch.offsetTop;
                const panelHeight = leftPanel.clientHeight;
                
                leftPanel.scrollTo({
                    top: Math.max(0, lineTop - panelHeight / 2),
                    behavior: 'smooth'
                });
                
                // 添加闪烁效果
                bestMatch.style.animation = 'highlight-flash 0.6s ease-in-out';
                setTimeout(() => {
                    if (bestMatch.style) {
                        bestMatch.style.animation = '';
                    }
                }, 600);
            } else {
                // 如果没找到精确匹配，显示提示
                showSearchHint(quote);
            }
        }
        
        function clearHighlights() {
            if (currentHighlighted) {
                currentHighlighted.classList.remove('highlighted');
                currentHighlighted = null;
            }
        }
        
        function showSearchHint(quote) {
            // 创建临时提示
            const hint = document.createElement('div');
            hint.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #ff9500;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            hint.textContent = '未找到完全匹配的文本，请检查原文';
            document.body.appendChild(hint);
            
            setTimeout(() => {
                document.body.removeChild(hint);
            }, 2000);
        }
        
        // 双击清除所有高亮
        document.addEventListener('dblclick', function(e) {
            if (e.target.closest('.original-content')) {
                clearHighlights();
                if (currentActiveQuote) {
                    currentActiveQuote.classList.remove('active');
                    currentActiveQuote = null;
                }
            }
        });

        // 初始化时为原始内容添加行包装
        function wrapContentLines() {
            const originalContent = document.querySelector('.original-content');
            const text = originalContent.textContent;
            const lines = text.split('\n');
            originalContent.innerHTML = lines.map((line, index) => 
                `<span id="line-${index}" class="content-line">${line}</span>`
            ).join('\n');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            wrapContentLines();
        });
    </script>
</body>
</html> 