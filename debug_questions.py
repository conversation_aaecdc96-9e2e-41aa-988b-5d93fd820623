#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from hcc_analyzer_simple import load_hcc_questions, analyze_with_gpt4

def debug_questions():
    """调试问题重复的问题"""
    
    # 加载问题
    questions = load_hcc_questions()
    print(f"📊 加载了 {len(questions)} 个问题")
    
    # 读取文件
    with open("txts/10056223-DS-5.txt", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分析
    results = analyze_with_gpt4(content, "10056223-DS-5.txt")
    
    print(f"\n📊 GPT返回了 {len(results)} 个结果")
    
    # 查找包含age的问题
    age_questions = []
    for i, result in enumerate(results):
        question = result.get("question", "").lower()
        if "age" in question:
            age_questions.append((i, result["question"], result.get("answer", "")[:50]))
    
    print(f"\n🔍 找到 {len(age_questions)} 个包含'age'的问题:")
    for i, (idx, question, answer) in enumerate(age_questions):
        print(f"  {i+1}. [{idx}] {question} -> {answer}...")
    
    # 查找包含gender的问题
    gender_questions = []
    for i, result in enumerate(results):
        question = result.get("question", "").lower()
        if "gender" in question:
            gender_questions.append((i, result["question"], result.get("answer", "")[:50]))
    
    print(f"\n🔍 找到 {len(gender_questions)} 个包含'gender'的问题:")
    for i, (idx, question, answer) in enumerate(gender_questions):
        print(f"  {i+1}. [{idx}] {question} -> {answer}...")

if __name__ == "__main__":
    debug_questions()
