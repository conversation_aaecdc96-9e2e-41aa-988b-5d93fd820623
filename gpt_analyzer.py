#!/usr/bin/env python3
"""
GPT-based Medical Text Analyzer for HCC Cases
Analyzes medical text and answers structured questions with confidence scores
"""

import json
import re
import pandas as pd
from typing import Dict, List, Tuple, Optional

class HCCAnalyzer:
    def __init__(self, text_file_path: str, excel_file_path: str):
        """
        Initialize the HCC analyzer with medical text and questions
        
        Args:
            text_file_path: Path to the medical text file
            excel_file_path: Path to the Excel file with structured questions
        """
        self.text_file_path = text_file_path
        self.excel_file_path = excel_file_path
        self.medical_text = self._load_medical_text()
        self.questions = self._load_questions()
        
    def _load_medical_text(self) -> str:
        """Load the medical text from file"""
        try:
            with open(self.text_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error loading medical text: {e}")
            return ""
    
    def _load_questions(self) -> List[Dict]:
        """Load questions from Excel file"""
        try:
            df = pd.read_excel(self.excel_file_path)
            questions = []
            
            for index, row in df.iterrows():
                if pd.notna(row.iloc[2]):  # Column C (Questions)
                    question_data = {
                        'id': index + 1,
                        'structure': row.iloc[1] if pd.notna(row.iloc[1]) else "",  # Column B
                        'question': row.iloc[2],  # Column C
                        'remarks': row.iloc[3] if pd.notna(row.iloc[3]) else ""  # Column D
                    }
                    questions.append(question_data)
            
            return questions
        except Exception as e:
            print(f"Error loading questions: {e}")
            return []
    
    def find_supporting_quotes(self, answer: str, question: str) -> List[str]:
        """
        Find supporting quotes from the original text for a given answer
        
        Args:
            answer: The answer provided
            question: The original question
            
        Returns:
            List of supporting quotes from the original text
        """
        quotes = []
        text_lines = self.medical_text.split('\n')
        
        # Keywords to search for based on the answer
        keywords = self._extract_keywords(answer, question)
        
        for line in text_lines:
            line = line.strip()
            if not line:
                continue
                
            # Check if line contains relevant keywords
            for keyword in keywords:
                if keyword.lower() in line.lower():
                    quotes.append(line)
                    break
        
        return quotes[:3]  # Return top 3 most relevant quotes
    
    def _extract_keywords(self, answer: str, question: str) -> List[str]:
        """Extract relevant keywords from answer and question"""
        keywords = []
        
        # Extract specific medical terms and values
        medical_terms = [
            'male', 'female', 'hcc', 'lesion', 'cm', 'cirrhosis', 
            'bilirubin', 'albumin', 'inr', 'creatinine', 'sodium',
            'tace', 'rfa', 'ablation', 'chemoembolization'
        ]
        
        answer_words = answer.lower().split()
        for word in answer_words:
            if word in medical_terms or len(word) > 3:
                keywords.append(word)
        
        # Add specific patterns
        if 'sex:' in self.medical_text.lower():
            keywords.append('sex:')
        if 'gender' in question.lower():
            keywords.extend(['sex:', 'male', 'female'])
        if 'size' in question.lower():
            keywords.extend(['cm', 'measures', 'lesion'])
        if 'number' in question.lower():
            keywords.extend(['lesions', 'two', 'single', 'multiple'])
            
        return list(set(keywords))
    
    def calculate_confidence(self, answer: str, question: str, supporting_quotes: List[str]) -> int:
        """
        Calculate confidence score for an answer based on supporting evidence
        
        Args:
            answer: The provided answer
            question: The original question
            supporting_quotes: List of supporting quotes found
            
        Returns:
            Confidence score (0-100)
        """
        confidence = 50  # Base confidence
        
        # Increase confidence based on supporting quotes
        if supporting_quotes:
            confidence += min(len(supporting_quotes) * 15, 30)
        
        # Increase confidence for specific, measurable answers
        if re.search(r'\d+\.?\d*\s*(cm|mg/dL|mEq/L)', answer):
            confidence += 20
        
        # Increase confidence for clear categorical answers
        categorical_answers = ['male', 'female', 'yes', 'no', 'present', 'absent']
        if any(cat in answer.lower() for cat in categorical_answers):
            confidence += 15
        
        # Decrease confidence for uncertain language
        uncertain_terms = ['not mentioned', 'not explicitly', 'unclear', 'possibly']
        if any(term in answer.lower() for term in uncertain_terms):
            confidence -= 20
        
        return max(10, min(100, confidence))
    
    def analyze_question(self, question_data: Dict) -> Dict:
        """
        Analyze a single question and provide answer with confidence
        
        Args:
            question_data: Dictionary containing question information
            
        Returns:
            Dictionary with answer, confidence, and supporting quotes
        """
        question = question_data['question']
        
        # Simple rule-based analysis for demonstration
        # In a real implementation, this would use GPT API
        answer = self._generate_answer(question)
        supporting_quotes = self.find_supporting_quotes(answer, question)
        confidence = self.calculate_confidence(answer, question, supporting_quotes)
        
        return {
            'question_id': question_data['id'],
            'question': question,
            'answer': answer,
            'confidence': confidence,
            'supporting_quotes': supporting_quotes
        }
    
    def _generate_answer(self, question: str) -> str:
        """
        Generate answer based on medical text analysis
        This is a simplified version - in practice would use GPT API
        """
        question_lower = question.lower()

        # Gender detection
        if 'gender' in question_lower or 'sex' in question_lower:
            if 'sex: m' in self.medical_text.lower():
                return "Male"
            elif 'sex: f' in self.medical_text.lower():
                return "Female"
            else:
                return "Not specified"

        # Age detection
        if 'age' in question_lower:
            age_match = re.search(r'(\d+)\s*years?\s*old', self.medical_text, re.IGNORECASE)
            if age_match:
                return f"{age_match.group(1)} years"
            else:
                return "Not explicitly mentioned"

        # HCC number detection
        if 'number' in question_lower and 'hcc' in question_lower:
            if 'two arterially-enhancing lesions' in self.medical_text.lower():
                return "2"
            elif 'single' in self.medical_text.lower():
                return "1"
            else:
                return "Not clearly specified"

        # Size detection
        if 'size' in question_lower and ('hcc' in question_lower or 'lesion' in question_lower):
            size_match = re.search(r'(\d+\.?\d*)\s*cm', self.medical_text)
            if size_match:
                return f"{size_match.group(1)} cm"
            else:
                return "Not specified"

        # Lab values - improved detection
        if 'bilirubin' in question_lower:
            bilirubin_match = re.search(r'total bilirubin:?\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if bilirubin_match:
                return f"{bilirubin_match.group(1)} mg/dL"
            else:
                return "Not reported"

        if 'albumin' in question_lower:
            albumin_match = re.search(r'albumin:?\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if albumin_match:
                return f"{albumin_match.group(1)} g/dL"
            else:
                return "Not reported"

        if 'sodium' in question_lower:
            sodium_match = re.search(r'sodium:?\s*(\d+)', self.medical_text, re.IGNORECASE)
            if sodium_match:
                return f"{sodium_match.group(1)} mEq/L"
            else:
                return "Not reported"

        if 'creatinine' in question_lower:
            creatinine_match = re.search(r'creatinine:?\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if creatinine_match:
                return f"{creatinine_match.group(1)} mg/dL"
            else:
                return "Not reported"

        if 'inr' in question_lower or 'international normalized ratio' in question_lower:
            inr_match = re.search(r'inr:?\s*(\d+\.?\d*)', self.medical_text, re.IGNORECASE)
            if inr_match:
                return f"{inr_match.group(1)}"
            else:
                return "Not reported"

        # Cirrhosis detection
        if 'cirrhosis' in question_lower:
            if 'cirrhosis' in self.medical_text.lower():
                return "Yes"
            else:
                return "No evidence mentioned"

        # Activity status
        if 'activity status' in question_lower:
            if 'ambulatory - independent' in self.medical_text.lower():
                return "Ambulatory - Independent"
            else:
                return "Not specified"

        # Mental status
        if 'mental status' in question_lower:
            if 'clear and coherent' in self.medical_text.lower():
                return "Clear and coherent"
            else:
                return "Not specified"

        # Level of consciousness
        if 'consciousness' in question_lower:
            if 'alert and interactive' in self.medical_text.lower():
                return "Alert and interactive"
            else:
                return "Not specified"

        # Ascites detection
        if 'ascites' in question_lower:
            if 'ascites' in self.medical_text.lower():
                return "Present"
            else:
                return "Not mentioned"

        # Prior treatment
        if 'prior treatment' in question_lower or 'treatment received' in question_lower:
            treatments = []
            if 'rfa' in self.medical_text.lower():
                treatments.append("RFA (Radiofrequency Ablation)")
            if 'tace' in self.medical_text.lower():
                treatments.append("TACE (Transarterial Chemoembolization)")
            if treatments:
                return ", ".join(treatments)
            else:
                return "Not specified"

        # Liver transplant
        if 'liver transplant' in question_lower:
            if 'transplant' in self.medical_text.lower():
                return "Yes"
            else:
                return "No"

        # Deceased/expired
        if 'deceased' in question_lower or 'expired' in question_lower:
            if 'discharge' in self.medical_text.lower():
                return "No"
            else:
                return "Not specified"

        # Default response
        return "Information not available in the provided text"
    
    def analyze_all_questions(self) -> List[Dict]:
        """Analyze all questions and return results"""
        results = []
        
        for question_data in self.questions:
            # Skip first 4 questions as requested
            if question_data['id'] <= 4:
                continue
                
            result = self.analyze_question(question_data)
            results.append(result)
        
        return results
    
    def generate_html_output(self, results: List[Dict]) -> str:
        """Generate HTML output for the analysis results"""
        html_sections = []
        
        for result in results:
            confidence_color = self._get_confidence_color(result['confidence'])
            
            quotes_html = ""
            if result['supporting_quotes']:
                quotes_html = f"""
                    <div class="supporting-quotes">
                        <div class="supporting-quotes-label">Supporting quotes</div>
                        {"".join([f'''
                        <div class="quote" onclick="highlightText('{quote}')">
                            <span class="quote-icon">🔗</span>
                            <div class="quote-text">{quote}</div>
                        </div>''' for quote in result['supporting_quotes']])}
                    </div>"""
            
            section_html = f"""
                <div class="subsection">
                    <h4>{result['question']}</h4>
                    <div class="answer">{result['answer']}</div>
                    <div class="confidence-score" style="background: {confidence_color}">
                        Confidence: {result['confidence']}%
                    </div>
                    {quotes_html}
                </div>"""
            
            html_sections.append(section_html)
        
        return "\n".join(html_sections)
    
    def _get_confidence_color(self, confidence: int) -> str:
        """Get color based on confidence level"""
        if confidence >= 80:
            return "linear-gradient(135deg, #d4edda, #c3e6cb)"
        elif confidence >= 60:
            return "linear-gradient(135deg, #fff3cd, #ffeaa7)"
        else:
            return "linear-gradient(135deg, #f8d7da, #f5c6cb)"

if __name__ == "__main__":
    # Example usage
    analyzer = HCCAnalyzer("txts/10056223-DS-5.txt", "HCC cross-referencing platform structured questions_20250704.xlsx")
    results = analyzer.analyze_all_questions()
    
    print("Analysis Results:")
    for result in results:
        print(f"Q: {result['question']}")
        print(f"A: {result['answer']} (Confidence: {result['confidence']}%)")
        print(f"Supporting quotes: {len(result['supporting_quotes'])}")
        print("-" * 50)
