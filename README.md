# 🩺 TCGA甲状腺癌病理报告专家级分析系统

基于Azure GPT-4的专业甲状腺癌病理报告分析系统，集成第8版TNM分期系统和ATA风险分层规则。

## 🌟 核心特性

### 🔬 专业医学规则
- **第8版分化型甲状腺癌TNM分期系统**
  - 精确的T、N、M分期定义
  - 年龄相关的分期计算逻辑（<55岁 vs ≥55岁）
  - 完整的分期组合规则

- **ATA风险分层系统**
  - 层次化评估：High → Intermediate → Low
  - 8个详细风险因素分析
  - 专业的风险评估逻辑

### 📊 智能数据集成
- **Excel年龄数据自动匹配**
  - 自动加载`TCGA-THCA_GPT4.1_20250609 TW.xlsx`中的339个样本年龄数据
  - 通过TCGA ID自动匹配患者年龄
  - 解决病理报告中缺少年龄信息的问题

### 🤖 Azure GPT-4专家分析
- **25个专业问题完整分析**
  - 手术类型（2个问题）
  - 肿瘤部位（1个问题）
  - AJCC分期（11个问题）
  - ATA风险分层（9个问题）
  - 其他信息（2个问题）

### 🎨 专业报告界面
- **双面板设计**：左侧原始报告，右侧专家分析
- **医学分类组织**：按专业结构展示分析结果
- **专家级视觉设计**：专业的医学报告界面
- **响应式布局**：支持不同屏幕尺寸

## 📁 项目结构

```
📦 TCGA甲状腺癌专家分析系统
├── 🩺 azure_gpt4_complete_expert.py    # 核心专家级分析器
├── 📊 TCGA-THCA_GPT4.1_20250609 TW.xlsx # 患者年龄数据
├── 📋 Question list 20250616.xlsx       # 25个分析问题定义
├── 📄 requirements.txt                  # Python依赖包
├── 📖 README.md                        # 项目说明文档
├── 📁 txts/                           # 输入目录
│   └── 📁 txts/                       # 50个TCGA病理报告文件
└── 📁 html_reports_expert/            # 专家级分析结果输出
    ├── 🌐 index.html                  # 结果索引页面
    └── 📄 *.html                      # 各个样本的专家分析报告
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install pandas openpyxl requests

# 或使用requirements.txt
pip install -r requirements.txt
```

### 2. 配置Azure OpenAI

在`azure_gpt4_complete_expert.py`中配置您的Azure OpenAI参数：
- API密钥
- 端点URL
- 部署名称
- API版本

### 3. 运行分析

```bash
python azure_gpt4_complete_expert.py
```

### 4. 查看结果

- 打开 `html_reports_expert/index.html` 查看所有分析结果
- 每个TCGA样本都有独立的专家级分析报告

## 📈 分析结果

### 成功率统计
- ✅ **98%成功率**：49/50文件成功分析
- 📊 **339个样本年龄数据**：完整覆盖所有TCGA样本
- 🎯 **25个问题完整分析**：专业医学问题全覆盖

### 分析内容
1. **手术类型分析**
   - 甲状腺切除术范围
   - 淋巴结清扫范围

2. **肿瘤特征分析**
   - 肿瘤部位定位
   - 肿瘤大小测量
   - 甲状腺外侵犯评估

3. **TNM分期分析**
   - T分期（原发肿瘤）
   - N分期（区域淋巴结）
   - M分期（远处转移）
   - 最终分期计算

4. **ATA风险分层**
   - 高风险因素识别
   - 中等风险因素评估
   - 低风险因素确认
   - 最终风险分类

5. **其他医学信息**
   - 甲状旁腺切除情况
   - 淋巴细胞性甲状腺炎

## 🔧 技术特点

### 专业医学规则
- 严格遵循第8版TNM分期标准
- 精确的ATA风险分层逻辑
- 年龄驱动的分期计算

### 智能数据处理
- 自动Excel数据匹配
- 容错处理和备用方案
- 智能内容截取和优化

### 高质量输出
- 专业的HTML报告生成
- 结构化的医学信息展示
- 友好的用户界面设计

## 💡 使用说明

### 输入要求
- **病理报告文件**：放置在`txts/txts/`目录下
- **文件格式**：UTF-8编码的.txt文件
- **命名规则**：TCGA-XX-XXXX.txt格式

### 输出结果
- **HTML报告**：双面板专家级分析报告
- **索引页面**：所有结果的汇总展示
- **专业标识**：Expert级别的视觉标识

## 📞 技术支持

本系统专为甲状腺癌病理报告分析设计，集成了最新的医学分期标准和风险评估规则，为临床研究和医学分析提供专业的AI辅助工具。

---

**🩺 专家级医学AI分析系统 | Azure GPT-4驱动 | TNM第8版 + ATA风险分层** 