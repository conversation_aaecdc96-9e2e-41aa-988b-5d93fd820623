#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure GPT-4 HCC医疗文本分析器
简化版本 - 左侧：原始医疗报告 | 右侧：GPT-4分析结果
"""

import os
import glob
import json
import time
import requests
import pandas as pd
from datetime import datetime

# Azure OpenAI API配置
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# 构建Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# HCC Excel文件路径
EXCEL_FILE_PATH = "HCC cross-referencing platform structured questions_20250704.xlsx"

def load_hcc_questions():
    """加载HCC结构化问题"""
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        questions = []

        # 假设Excel文件有Structure, Question, Remarks列
            for _, row in df.iterrows():
            if pd.notna(row.iloc[2]):  # 假设问题在第3列
                question = str(row.iloc[2]).strip()
                remarks = str(row.iloc[3]).strip() if len(row) > 3 and pd.notna(row.iloc[3]) else ""
                if question and question != 'nan':
                    questions.append({
                        'question': question,
                        'remarks': remarks
                    })

        print(f"📊 加载了 {len(questions)} 个HCC问题")
        return questions
    except Exception as e:
        print(f"⚠️ 加载HCC问题失败: {e}")
        return []

def analyze_with_azure_gpt4_hcc(content, filename=None, questions=None):
    """使用Azure GPT-4分析HCC医疗文本"""
    
    # 截取内容，避免超过token限制
    if len(content) > 15000:
        content = content[:15000] + "...[content truncated]"

    # 构建问题列表
    if not questions:
        questions = load_hcc_questions()

    questions_text = ""
    for i, q in enumerate(questions, 1):
        questions_text += f"{i}. {q['question']}\n"
        if q['remarks']:
            questions_text += f"   Remarks: {q['remarks']}\n"
        questions_text += "\n"

    prompt = f"""You are a medical expert specializing in hepatocellular carcinoma (HCC) and liver cancer analysis. Analyze the following medical text and answer all questions precisely.

MEDICAL TEXT:
{content}

## QUESTIONS TO ANALYZE:
{questions_text}

## ANALYSIS INSTRUCTIONS:
1. Read the medical text carefully
2. Answer each question based on the information available in the text
3. Provide confidence scores from 1-10 (1=very uncertain, 10=very certain)
4. Extract supporting quotes EXACTLY from the original text - do not paraphrase or interpret
5. If information is not available, state "Not mentioned" or "Not available"

**CRITICAL REQUIREMENT**: Supporting quotes MUST be EXACT, VERBATIM text from the original medical text:
- 🚫 NEVER create, summarize, paraphrase, or interpret any text
- 🚫 NEVER include explanatory phrases like "according to", "based on", "meets criteria"
- ✅ ONLY copy-paste EXACT sentences/phrases that physically exist in the input text
- ✅ Use quotation marks and copy the text character-for-character including punctuation
- ✅ If no supporting text exists, use an empty quotes array: "quotes": []

**EXAMPLE**:
❌ Wrong: "quotes": ["Patient has liver disease based on analysis"]
✅ Correct: "quotes": ["Total Bilirubin: 1.4 mg/dL"]

Return JSON format with ALL questions answered:
{{
  "results": [
    {{"question": "Question 1 text", "answer": "your answer", "confidence": 7, "quotes": ["exact quote from text"]}},
    {{"question": "Question 2 text", "answer": "your answer", "confidence": 8, "quotes": ["exact quote from text"]}},
    ... (continue for all questions)
  ]
}}"""

    headers = {
        "api-key": AZURE_OPENAI_API_KEY,
        "Content-Type": "application/json"
    }
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a medical expert specializing in thyroid cancer pathology with expertise in TNM staging and ATA risk stratification."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 3000,
        "temperature": 0.1
    }
    
    # 重试机制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄{attempt+1}", end="", flush=True)
                time.sleep(15 * attempt)  # 递增延迟
            else:
                print("🤖", end="", flush=True)
            
            response = requests.post(API_URL, headers=headers, json=data, timeout=600)
        
            if response.status_code == 401:
                print("❌密钥无效", end=" ")
                return create_fallback_results("Invalid Azure API key")
            elif response.status_code == 429:
                print("❌API限制", end=" ")
                if attempt < max_retries - 1:
                    continue  # 重试
                return create_fallback_results("API rate limit")
            elif response.status_code == 404:
                print("❌部署未找到", end=" ")
                return create_fallback_results("Deployment not found")
            elif response.status_code != 200:
                print(f"❌HTTP{response.status_code}", end=" ")
                if attempt < max_retries - 1:
                    continue  # 重试
                return create_fallback_results(f"HTTP {response.status_code}: {response.text[:100]}")
                
            result = response.json()
            
            if 'error' in result:
                print("❌API错误", end=" ")
                if attempt < max_retries - 1:
                    continue  # 重试
                return create_fallback_results(result['error'].get('message', 'Unknown error'))
                
            if 'choices' not in result or len(result['choices']) == 0:
                print("❌无响应", end=" ")
                if attempt < max_retries - 1:
                    continue  # 重试
                return create_fallback_results("No response from Azure OpenAI")
                
            gpt_response = result['choices'][0]['message']['content'].strip()
            
            # 解析JSON响应 - 改进版本
            try:
                # 尝试多种JSON提取方法
                parsed = parse_gpt_response_robust(gpt_response, filename)
                if parsed:
                    print("✅", end=" ", flush=True)
                    return parsed
                else:
                    print("⚠️JSON错误", end=" ")
                    if attempt < max_retries - 1:
                        continue  # 重试
                    # 保存错误响应用于调试
                    save_debug_response(gpt_response, filename, attempt)
                    return create_fallback_results("JSON parsing error")
                    
            except Exception as e:
                print(f"⚠️解析错误:{str(e)[:10]}", end=" ")
                if attempt < max_retries - 1:
                    continue  # 重试
                save_debug_response(gpt_response, filename, attempt)
                return create_fallback_results(f"Parsing error: {str(e)}")
                
        except requests.exceptions.Timeout:
            print("❌超时", end=" ")
            if attempt < max_retries - 1:
                continue  # 重试
            return create_fallback_results("Request timeout")
        except requests.exceptions.ConnectionError:
            print("❌连接错误", end=" ")
            if attempt < max_retries - 1:
                continue  # 重试
            return create_fallback_results("Connection error")
        except Exception as e:
            print(f"❌{str(e)[:20]}", end=" ")
            if attempt < max_retries - 1:
                continue  # 重试
            return create_fallback_results(str(e))
    
    # 如果所有重试都失败了
    return create_fallback_results("All retries failed")

def parse_gpt_response_robust(gpt_response, filename=None):
    """健壮的GPT响应JSON解析函数"""
    import re
    
    # 方法1: 标准JSON提取
    try:
        start = gpt_response.find('{')
        end = gpt_response.rfind('}') + 1
        
        if start != -1 and end != -1:
            json_content = gpt_response[start:end]
            parsed = json.loads(json_content)
            return parsed.get('results', [])
    except:
        pass
    
    # 方法2: 尝试修复常见的JSON错误
    try:
        # 找到可能的JSON内容
        json_match = re.search(r'\{.*"results".*\}', gpt_response, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            
            # 修复常见错误
            # 1. 移除尾随逗号
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            
            # 2. 修复单引号为双引号
            json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)
            
            # 3. 修复非转义的引号
            json_str = re.sub(r'(?<!\\)"(?![^"]*":)', '\\"', json_str)
            
            parsed = json.loads(json_str)
            return parsed.get('results', [])
    except:
        pass
    
    # 方法3: 尝试逐行解析，查找results数组
    try:
        lines = gpt_response.split('\n')
        in_results = False
        results_content = []
        
        for line in lines:
            if '"results"' in line and '[' in line:
                in_results = True
                # 提取[后的内容
                start_idx = line.find('[')
                if start_idx != -1:
                    results_content.append(line[start_idx:])
                continue
            
            if in_results:
                results_content.append(line)
                if ']' in line:
                    break
        
        if results_content:
            results_str = '\n'.join(results_content)
            # 尝试构造完整JSON
            full_json = '{"results": ' + results_str + '}'
            parsed = json.loads(full_json)
            return parsed.get('results', [])
    except:
        pass
    
    # 方法4: 部分解析 - 尝试提取关键答案
    try:
        # 如果JSON完全损坏，尝试用正则表达式提取关键信息
        # 使用动态问题列表
        questions_list = questions if questions else load_hcc_questions()
        question_texts = [q['question'] if isinstance(q, dict) else str(q) for q in questions_list]
        
        partial_results = []
        for q in question_texts:
            # 尝试找到这个问题的答案
            pattern = rf'"question":\s*"{re.escape(q)}".*?"answer":\s*"([^"]*)"'
            match = re.search(pattern, gpt_response, re.IGNORECASE | re.DOTALL)
            if match:
                answer = match.group(1)
                partial_results.append({
                    "question": q,
                    "answer": answer,
                    "confidence": 5,  # 降低置信度
                    "quotes": ["Partial extraction from malformed JSON"]
                })
            else:
                partial_results.append({
                    "question": q,
                    "answer": "Unable to extract",
                    "confidence": 1,
                    "quotes": ["JSON parsing failed"]
                })

        if partial_results:
            return partial_results
    except:
        pass
    
    return None

def save_debug_response(gpt_response, filename, attempt):
    """保存错误的GPT响应用于调试"""
    try:
        debug_dir = "debug_responses"
        os.makedirs(debug_dir, exist_ok=True)
        
        base_name = os.path.splitext(filename)[0] if filename else "unknown"
        debug_file = os.path.join(debug_dir, f"{base_name}_attempt_{attempt}_debug.txt")
        
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"Filename: {filename}\n")
            f.write(f"Attempt: {attempt}\n")
            f.write(f"Timestamp: {datetime.now()}\n")
            f.write("="*50 + "\n")
            f.write(gpt_response)
        
        print(f"🐛调试文件:{debug_file}", end=" ")
    except Exception as e:
        print(f"❌调试保存失败:{str(e)[:20]}", end=" ")

def create_fallback_results(error, questions=None):
    """创建失败时的默认结果 - HCC版本"""
    if not questions:
        questions = load_hcc_questions()

    question_texts = [q['question'] if isinstance(q, dict) else str(q) for q in questions]

    return [{"question": q, "answer": "Analysis failed", "confidence": 1, "quotes": [f"Error: {error}"]} for q in question_texts]

def generate_hcc_html(original_content, results, filename):
    """生成HCC双面板HTML报告"""

    def get_single_result(question_text):
        found = next((r for r in results if question_text.lower() in r["question"].lower()), None)
        return found if found else {"question": question_text, "answer": "Not found", "confidence": 1, "quotes": []}
    
    def verify_quote_in_content(quote, content):
        """简化的引用验证 - 基于强化的GPT提示词约束"""
        if not quote or not quote.strip():
            return False
        
        quote_clean = quote.strip()
        
        # 只过滤明显的系统生成信息（外部数据源）
        system_indicators = [
            "if age not found in report, use this age from clinical data",
            "age from clinical data:",
            "clinical data:",
            "excel data:",
            "patient age from external source"
        ]
        
        for indicator in system_indicators:
            if indicator.lower() in quote_clean.lower():
                return False  # 这是外部数据源，不是原文内容
        
        # 过滤明显的AI解释性语言
        ai_explanation_phrases = [
            "according to", "based on", "meets criteria", "fulfills criteria", 
            "the patient meets", "this meets", "rationale:", "therefore"
        ]
        
        for phrase in ai_explanation_phrases:
            if phrase.lower() in quote_clean.lower():
                return False
        
        # 移除引号和清理文本
        quote_clean = quote_clean.strip('"\'""''')
        
        
        # 简化的匹配验证
        import re
        quote_lower = quote_clean.lower()
        content_lower = content.lower()
        
        # 1. 直接匹配
        if quote_lower in content_lower:
            return True
        
        # 2. 去除标点符号后匹配
        quote_no_punct = re.sub(r'[^\w\s]', ' ', quote_lower)
        content_no_punct = re.sub(r'[^\w\s]', ' ', content_lower)
        quote_normalized = ' '.join(quote_no_punct.split())
        content_normalized = ' '.join(content_no_punct.split())
        
        if quote_normalized in content_normalized:
            return True
        
        # 3. 宽松的部分匹配（85%匹配率，适应轻微差异）
        quote_words = quote_normalized.split()
        if len(quote_words) >= 3:  # 至少3个单词
        content_words = content_normalized.split()
            for i in range(len(content_words) - len(quote_words) + 1):
                content_segment = content_words[i:i+len(quote_words)]
                matches = sum(1 for q, c in zip(quote_words, content_segment) if q == c)
                if matches >= len(quote_words) * 0.85:  # 85%匹配率
                    return True
        
        return False

    def render_question_item(item, title=None):
        if not title:
            title = item["question"]
        
        # 验证并过滤quotes，只保留原文中真实存在的
        valid_quote_links = []
        system_info_quotes = []
        
        for i, quote in enumerate(item["quotes"]):
            if quote and quote.strip():
                # 检查是否是系统添加的年龄信息
                is_system_info = any(indicator.lower() in quote.lower() for indicator in [
                    "if age not found in report, use this age from clinical data",
                    "age from clinical data:",
                    "clinical data:",
                    "excel data:"
                ])
                
                if is_system_info:
                    # 这是系统信息，单独处理
                    system_info_quotes.append(quote)
                elif verify_quote_in_content(quote, original_content):
                    # 这是原文中的真实内容
                    quote_id = f"quote-{abs(hash(quote + title))}_{i}"
                    escaped_quote = quote.replace('"', '&quot;').replace("'", "&#39;")
                    valid_quote_links.append(f'<span class="quote-link" data-quote="{escaped_quote}" onclick="highlightQuote(this)" title="点击定位到原文">{quote}</span>')
        
        # 构建quotes HTML
        quotes_parts = []
        
        if valid_quote_links:
            quotes_parts.append('; '.join(valid_quote_links))
        
        if system_info_quotes:
            # 为系统信息添加特殊样式
            system_quotes_html = []
            for sq in system_info_quotes:
                system_quotes_html.append(f'<span class="system-info-quote" title="信息来源：Excel数据文件">{sq}</span>')
            quotes_parts.append('; '.join(system_quotes_html))
        
        if quotes_parts:
            quotes_html = '; '.join(quotes_parts)
        else:
            quotes_html = '<span class="no-quotes">No valid supporting quotes found in original text</span>'
        
        return f'''
            <div class="question-item">
                <h4>{title}</h4>
                <div class="answer-group">
                    <div class="answer-item">
                        <span class="label">Answer</span>
                        <span class="value">{item["answer"]}</span>
                    </div>
                    <div class="answer-item">
                        <span class="label">Supporting quotes</span>
                        <span class="value quotes-container">{quotes_html}</span>
                    </div>
                </div>
            </div>'''
    
    # 生成患者摘要部分
    def generate_patient_summary():
        """生成患者关键信息摘要"""
        # 提取关键信息
        histologic_type = ata_histologic_item.get("answer", "Unknown histologic type") if ata_histologic_item else "Unknown histologic type"
        t_stage = t_stage_item.get("answer", "Tx") if t_stage_item else "Tx"
        n_stage = n_stage_item.get("answer", "Nx") if n_stage_item else "Nx"  
        m_stage = m_stage_item.get("answer", "Mx") if m_stage_item else "Mx"
        ajcc_stage = ajcc_stage_item.get("answer", "Unknown stage") if ajcc_stage_item else "Unknown stage"
        ata_risk = ata_item.get("answer", "Unknown risk") if ata_item else "Unknown risk"
        
        # 构建TNM字符串
        tnm_string = f"{t_stage}{n_stage}{m_stage}"
        
        # 构建摘要字符串
        summary_text = f"{histologic_type}, {tnm_string} (AJCC 8 {ajcc_stage}), ATA (2015) {ata_risk}"
        
        return f'''
        <div class="patient-summary">
            <h2>📋 Patient Summary</h2>
            <div class="summary-content">{summary_text}</div>
        </div>
        '''
    
    # 生成右侧分析内容
    analysis_sections = generate_patient_summary() + '''
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">1</div>
            <h2 class="section-title-large">Type of surgery</h2>
        </div>
        <div class="section-content">'''
    
    for item in surgery_items:
        analysis_sections += render_question_item(item)
    
    analysis_sections += f'''
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">2</div>
            <h2>Site of tumor</h2>
        </div>
        <div class="section-content">
            {render_question_item(site_item, "Site of tumor")}
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">3</div>
            <h2 class="section-title-large">AJCC 8th Edition TNM Stage</h2>
        </div>
        <div class="section-content">
            {render_question_item(ajcc_stage_item, "Overall AJCC Stage")}
            
            {render_question_item(age_item, "3.1 Age of the patient")}
            
            <div class="subsection">
                <h3 class="subsection-title-large">3.2 T stage (Primary Tumor)</h3>
                {render_question_item(t_stage_item, "T stage determination")}
                {render_question_item(tumor_size_item, "3.2.1 Tumour size")}
                {render_question_item(extension_item, "3.2.2 Extrathyroidal extension")}
            </div>
            
            <div class="subsection">
                <h3 class="subsection-title-large">3.3 N stage (Regional Lymph Nodes)</h3>
                {render_question_item(n_stage_item, "N stage determination")}
                {render_question_item(ln_identified_item, "3.3.1 Number of lymph node identified")}
                {render_question_item(ln_involved_item, "3.3.2 Number of lymph node involved")}
                {render_question_item(ln_site_item, "3.3.3 Site of lymph node involved")}
                {render_question_item(ln_size_item, "3.3.4 Largest size of the involved lymph node")}
            </div>
            
            <div class="subsection">
                <h3 class="subsection-title-large">3.4 M stage (Distant Metastasis)</h3>
                {render_question_item(m_stage_item, "M stage determination")}
                {render_question_item(distant_meta_item, "3.4.1 Site of distant metastasis")}
            </div>

        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">4</div>
            <h2 class="section-title-large">ATA Risk Stratification</h2>
        </div>
        <div class="section-content">
            <div class="ata-main">
                {render_question_item(ata_item, "ATA Risk Category (High → Intermediate → Low hierarchy)")}
            </div>
            <div class="ata-details">
                <h4>Risk Assessment Details:</h4>
                {render_question_item(ata_distant_item, "4.1 Distant metastasis")}
                {render_question_item(ata_margins_item, "4.2 Margins involvement")}
                {render_question_item(ata_histologic_item, "4.3 Histologic type and subtype")}
                {render_question_item(ata_vascular_item, "4.4 Vascular invasion and extensiveness")}
                {render_question_item(ata_ln_involved_item, "4.5 Number of lymph node involved")}
                {render_question_item(ata_ln_size_item, "4.6 Largest size of the involved lymph node")}
                {render_question_item(ata_capsular_item, "4.7 Capsular invasion")}
                {render_question_item(ata_gene_item, "4.8 Gene mutation")}
            </div>
        </div>
    </div>
    
    <div class="analysis-section">
        <div class="section-header">
            <div class="section-number">5</div>
            <h2>Other information</h2>
        </div>
        <div class="section-content">
            {render_question_item(parathyroid_item, "5.1 Inadvertent parathyroidectomy")}
            {render_question_item(thyroiditis_item, "5.2 Lymphocytic thyroiditis")}
        </div>
    </div>'''
    
    # 格式化原始报告内容并添加智能高亮功能
    def create_searchable_content(content):
        """创建可搜索和高亮的内容"""
        # 按行分割内容
        lines = content.split('\n')
        formatted_lines = []
        
        for i, line in enumerate(lines):
            if line.strip():  # 非空行
                # 为每行添加唯一ID，便于搜索和高亮
                line_id = f"line-{i}"
                formatted_lines.append(f'<span id="{line_id}" class="content-line">{line}</span>')
            else:
                formatted_lines.append('<br>')
        
        return '<br>'.join(formatted_lines)
    
    formatted_content = create_searchable_content(original_content)
    
    html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expert Azure GPT-4 Analysis - {filename}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header-title {{ font-size: 18px; font-weight: 600; }}
        .expert-badge {{
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }}
        
        .main-container {{
            display: flex;
            height: calc(100vh - 60px);
        }}
        
        .panel {{
            width: 50%;
            background: white;
            display: flex;
            flex-direction: column;
        }}
        
        .left-panel {{ border-right: 2px solid #e0e6ed; }}
        
        .panel-content {{
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }}
        
                 .original-content {{
             font-family: 'Courier New', monospace;
             font-size: 13px;
             line-height: 1.5;
             color: #333;
         }}
         
         .content-line {{
             display: inline;
             transition: background-color 0.3s ease;
         }}
         
         .content-line.highlighted {{
             background-color: #ff9500 !important;
             color: white !important;
             padding: 2px 4px;
             border-radius: 3px;
             font-weight: bold;
             box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
         }}
         
         .quote-link {{
             cursor: pointer;
             color: #2c5aa0;
             text-decoration: underline;
             transition: all 0.2s ease;
             padding: 2px 4px;
             border-radius: 3px;
             margin: 0 2px;
         }}
         
         .quote-link:hover {{
             background-color: #e3f2fd;
             color: #1565c0;
             transform: translateY(-1px);
         }}
         
         .quote-link.active {{
             background-color: #ff9500;
             color: white;
             font-weight: bold;
         }}
         
         .quotes-container {{
             line-height: 1.6;
         }}
         
         .no-quotes {{
             color: #999;
             font-style: italic;
             font-size: 12px;
         }}
         
         .quote-link {{
             position: relative;
         }}
         
         .quote-link::before {{
             content: "📍";
             font-size: 10px;
             margin-right: 3px;
             opacity: 0.6;
         }}
         
         .system-info-quote {{
             background-color: #e3f2fd;
             color: #1565c0;
             padding: 2px 6px;
             border-radius: 4px;
             font-size: 12px;
             border-left: 3px solid #2196f3;
             margin: 0 2px;
             position: relative;
         }}
         
         .system-info-quote::before {{
             content: "📊";
             font-size: 10px;
             margin-right: 3px;
         }}
         
         .system-info-quote:hover {{
             background-color: #bbdefb;
         }}
        
        .analysis-section {{ margin-bottom: 30px; }}
        
        .section-header {{
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }}
        
        .section-number {{
            width: 28px; height: 28px;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .section-header h2 {{
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }}
        
        /* 放大指定字段的字体 */
        .section-title-large {{
            font-size: 22px !important;
            font-weight: 700 !important;
        }}
        
        .subsection-title-large {{
            font-size: 18px !important;
            font-weight: 600 !important;
        }}
        
        /* 患者摘要样式 */
        .patient-summary {{
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.1);
        }}
        
        .patient-summary h2 {{
            color: #155724;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .summary-content {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            line-height: 1.6;
        }}
        
        .section-content {{ margin-left: 40px; }}
        .question-item {{ margin-bottom: 20px; }}
        .subsection {{ margin: 20px 0; padding-left: 20px; border-left: 3px solid #e9ecef; }}
        .subsection h3 {{ color: #495057; font-size: 16px; margin-bottom: 10px; margin-left: -40px; }}
        .final-stage {{ border-left: 3px solid #28a745; background: #f8fff9; padding: 15px; }}
        .ata-main {{ background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px; }}
        .ata-details {{ background: #f8f9fa; padding: 15px; border-radius: 8px; }}
        
        .question-item h4 {{
            color: #333;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }}
        
        .answer-item {{
            margin-bottom: 4px;
            display: flex;
            align-items: flex-start;
        }}
        
        .answer-item .label {{
            color: #666;
            font-size: 12px;
            min-width: 100px;
            margin-right: 8px;
        }}
        
        .answer-item .value {{
            color: #333;
            font-size: 12px;
            font-weight: 500;
            flex: 1;
        }}
        
        .panel-content::-webkit-scrollbar {{ width: 6px; }}
        .panel-content::-webkit-scrollbar-track {{ background: #f1f1f1; }}
        .panel-content::-webkit-scrollbar-thumb {{ background: #c1c1c1; border-radius: 3px; }}
        .panel-content::-webkit-scrollbar-thumb:hover {{ background: #a8a8a8; }}
        
        @media (max-width: 1024px) {{
            .main-container {{ flex-direction: column; height: auto; }}
            .panel {{ width: 100%; height: 50vh; }}
            body {{ overflow: auto; }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">Expert Pathology Analysis (TNM 8th + ATA Risk)</div>
        <div class="expert-badge">🩺 Azure GPT-4 Expert</div>
    </div>
    
    <div class="main-container">
        <div class="panel left-panel">
            <div class="panel-content">
                <div class="original-content">{formatted_content}</div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-content">
                {analysis_sections}
            </div>
                 </div>
     </div>
     
     <script>
         // 智能高亮和滚动功能
         let currentHighlighted = null;
         let currentActiveQuote = null;
         
         function highlightQuote(element) {{
             const quote = element.getAttribute('data-quote');
             if (!quote || quote.trim() === '') return;
             
             // 清除之前的高亮
             clearHighlights();
             
             // 设置当前活跃的quote
             if (currentActiveQuote) {{
                 currentActiveQuote.classList.remove('active');
             }}
             currentActiveQuote = element;
             element.classList.add('active');
             
             // 在左侧内容中搜索并高亮匹配的文本
             const originalContent = document.querySelector('.original-content');
             const contentLines = originalContent.querySelectorAll('.content-line');
             
             let bestMatch = null;
             let bestScore = 0;
             
             // 智能匹配算法
             for (let line of contentLines) {{
                 const lineText = line.textContent.toLowerCase();
                 const quoteText = quote.toLowerCase();
                 
                 // 直接匹配
                 if (lineText.includes(quoteText)) {{
                     bestMatch = line;
                     bestScore = 100;
                     break;
                 }}
                 
                 // 部分匹配 - 计算相似度
                 const words = quoteText.split(/\\s+/).filter(w => w.length > 2);
                 let matchedWords = 0;
                 
                 for (let word of words) {{
                     if (lineText.includes(word)) {{
                         matchedWords++;
                     }}
                 }}
                 
                 const score = (matchedWords / words.length) * 100;
                 if (score > bestScore && score > 30) {{
                     bestMatch = line;
                     bestScore = score;
                 }}
             }}
             
             // 如果找到匹配，高亮并滚动
             if (bestMatch) {{
                 bestMatch.classList.add('highlighted');
                 currentHighlighted = bestMatch;
                 
                 // 滚动到匹配位置
                 const leftPanel = document.querySelector('.left-panel .panel-content');
                 const lineTop = bestMatch.offsetTop;
                 const panelHeight = leftPanel.clientHeight;
                 
                 leftPanel.scrollTo({{
                     top: Math.max(0, lineTop - panelHeight / 2),
                     behavior: 'smooth'
                 }});
                 
                 // 添加闪烁效果
                 bestMatch.style.animation = 'highlight-flash 0.6s ease-in-out';
                 setTimeout(() => {{
                     if (bestMatch.style) {{
                         bestMatch.style.animation = '';
                     }}
                 }}, 600);
             }} else {{
                 // 如果没找到精确匹配，显示提示
                 showSearchHint(quote);
             }}
         }}
         
         function clearHighlights() {{
             if (currentHighlighted) {{
                 currentHighlighted.classList.remove('highlighted');
                 currentHighlighted = null;
             }}
         }}
         
         function showSearchHint(quote) {{
             // 创建临时提示
             const hint = document.createElement('div');
             hint.style.cssText = `
                 position: fixed;
                 top: 50%;
                 left: 50%;
                 transform: translate(-50%, -50%);
                 background: #ff9500;
                 color: white;
                 padding: 10px 20px;
                 border-radius: 5px;
                 z-index: 10000;
                 font-size: 14px;
                 box-shadow: 0 4px 12px rgba(0,0,0,0.3);
             `;
             hint.textContent = '未找到完全匹配的文本，请检查原文';
             document.body.appendChild(hint);
             
             setTimeout(() => {{
                 document.body.removeChild(hint);
             }}, 2000);
         }}
         
         // 添加CSS动画
         const style = document.createElement('style');
         style.textContent = `
             @keyframes highlight-flash {{
                 0% {{ box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.7); }}
                 50% {{ box-shadow: 0 0 0 10px rgba(255, 149, 0, 0.3); }}
                 100% {{ box-shadow: 0 0 0 0 rgba(255, 149, 0, 0); }}
             }}
         `;
         document.head.appendChild(style);
         
         // 双击清除所有高亮
         document.addEventListener('dblclick', function(e) {{
             if (e.target.closest('.original-content')) {{
                 clearHighlights();
                 if (currentActiveQuote) {{
                     currentActiveQuote.classList.remove('active');
                     currentActiveQuote = null;
                 }}
             }}
         }});
     </script>
 </body>
 </html>'''
    
    return html_template

def main():
    """主函数 - 专家版本"""
    print("🩺 Azure GPT-4 专家级病理报告分析器 (TNM 8th + ATA)")
    print("=" * 60)
    
    # 项目内的相对路径
    input_dir = "./txts/txts"
    output_dir = "./html_reports_expert"
    
    print(f"📂 输入目录: {input_dir}")
    print(f"📁 输出目录: {output_dir}")
    print(f"🔑 Azure API: {AZURE_OPENAI_API_KEY[:15]}...{AZURE_OPENAI_API_KEY[-10:]}")
    print(f"🌐 端点: {ENDPOINT_URL}")
    print(f"🚀 部署: {DEPLOYMENT_NAME}")
    print(f"📋 分析规则: TNM第8版分期 + ATA风险分层")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载年龄数据
    age_dict = load_age_data()
    
    # 获取所有TXT文件
    txt_files = glob.glob(os.path.join(input_dir, "*.txt"))
    
    if not txt_files:
        print("❌ 未找到TXT文件")
        print(f"   请确认 {input_dir} 目录下有.txt文件")
        return
    
    print(f"🔍 找到 {len(txt_files)} 个TXT文件")
    print("=" * 60)
    
    # 处理统计
    success_count = 0
    failed_count = 0
    
    # 处理所有文件
    for i, txt_file in enumerate(txt_files, 1):
        filename = os.path.basename(txt_file)
        base_name = os.path.splitext(filename)[0]
        
        print(f"[{i:2d}/{len(txt_files)}] {filename:<30} ", end="")
        
        try:
            # 读取文件内容
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用Azure GPT-4专家分析
            analysis_results = analyze_with_azure_gpt4_expert(content, filename, age_dict)
            
            # 检查分析是否成功
            analysis_success = any(r.get("answer", "") != "Analysis failed" for r in analysis_results)
            
            # 生成HTML报告
            html_content = generate_expert_html(content, analysis_results, base_name)
            
            # 保存HTML文件
            html_filename = f"{base_name}_expert_analysis.html"
            html_path = os.path.join(output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            if analysis_success:
                success_count += 1
                print("")
            else:
                failed_count += 1
                print("(使用备用结果)")
            
            # API请求限制控制 - 专家分析需要更多时间
            if i % 3 == 0:
                time.sleep(12)  # 每3个文件后稍作停顿
            else:
                time.sleep(8)  # 基本延迟
            
        except Exception as e:
            print(f"❌ 文件处理错误: {e}")
            failed_count += 1
    
    # 生成索引页面
    print("\n📋 生成索引页面...")
    generate_expert_index_page(output_dir, success_count, failed_count, len(txt_files))
    
    # 处理完成报告
    print("\n" + "=" * 60)
    print("🎉 Azure GPT-4 专家级分析完成！")
    print("=" * 60)
    print(f"✅ 成功分析: {success_count}/{len(txt_files)}")
    print(f"⚠️  备用结果: {failed_count}/{len(txt_files)}")
    print(f"📁 查看结果: {os.path.abspath(output_dir)}")
    print(f"🌐 索引页面: {os.path.abspath(output_dir)}/index.html")
    print(f"🩺 技术: Azure OpenAI GPT-4.1 专家级TNM+ATA分析")
    
    # 成本估算
    estimated_cost = success_count * 0.12  # 专家分析成本更高
    print(f"💰 预估API成本: ${estimated_cost:.2f}")

def generate_expert_index_page(output_dir, success_count, failed_count, total_count):
    """生成专家级索引页面"""
    
    # 获取所有生成的HTML文件
    html_files = glob.glob(os.path.join(output_dir, "*_expert_analysis.html"))
    html_files.sort()
    
    index_html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure GPT-4 专家级分析结果 (TNM 8th + ATA)</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }}
        .header {{
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        .expert-badge {{
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-top: 10px;
        }}
        .stats {{
            background: #f8f9fa;
            padding: 25px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
        }}
        .file-grid {{
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }}
        .file-item {{
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
        }}
        .file-item:hover {{
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #2c5aa0;
        }}
        .file-link {{
            display: block;
            color: #2c5aa0;
            text-decoration: none;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
        }}
        .file-link:hover {{
            color: #1e3d72;
        }}
        .file-id {{
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 15px;
        }}
        .expert-tag {{
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }}
        .features {{
            font-size: 11px;
            color: #6c757d;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }}
        .feature-tag {{
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🩺 TCGA 甲状腺癌专家级病理分析</h1>
            <p>Azure GPT-4 专家级TNM第8版分期 + ATA风险分层系统</p>
            <div class="expert-badge">🩺 Expert Medical Analysis</div>
        </div>
        <div class="stats">
            <h3 style="margin: 0; color: #2c5aa0; margin-bottom: 15px;">📊 Azure GPT-4 专家级分析统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{len(html_files)}</div>
                    <div>专家级报告</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">TNM 8th</div>
                    <div>分期系统</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">ATA</div>
                    <div>风险分层</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{success_count}</div>
                    <div>成功分析</div>
                </div>
            </div>
        </div>
        <div class="file-grid">'''
    
    for html_file in html_files:
        filename = os.path.basename(html_file)
        base_name = filename.replace('_expert_analysis.html', '')
        
        index_html += f'''
            <div class="file-item">
                <div class="expert-tag">Expert</div>
                <a href="{filename}" class="file-link" target="_blank">📄 {base_name}</a>
                <div class="file-id">TCGA 样本 | Azure GPT-4 专家级TNM+ATA分析</div>
                <div class="features">
                    <span class="feature-tag">TNM第8版</span>
                    <span class="feature-tag">ATA风险分层</span>
                    <span class="feature-tag">年龄分期</span>
                    <span class="feature-tag">专家规则</span>
                </div>
            </div>'''
    
    index_html += '''
        </div>
    </div>
</body>
</html>'''
    
    # 保存索引文件
    index_path = os.path.join(output_dir, 'index.html')
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(index_html)

if __name__ == "__main__":
    main()