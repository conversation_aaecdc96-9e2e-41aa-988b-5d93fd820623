#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门比较前四个问题的准确性
"""

import os
import pandas as pd
from bs4 import BeautifulSoup

def load_ground_truth_debug():
    """调试加载ground truth数据"""
    print("🔍 调试ground truth文件加载...")
    
    # 检查文件是否存在
    csv_file = 'ground truth_20 MIMIC_20250714(Sheet2).csv'
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return {}
    
    print(f"✅ 文件存在: {csv_file}")
    print(f"📁 文件大小: {os.path.getsize(csv_file)} bytes")
    
    # 尝试不同编码
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252', 'iso-8859-1', 'windows-1252']
    
    for encoding in encodings:
        try:
            print(f"🧪 尝试编码: {encoding}")
            df = pd.read_csv(csv_file, encoding=encoding)
            print(f"✅ 成功使用 {encoding} 编码")
            print(f"📊 数据形状: {df.shape}")
            print(f"📋 前5列: {list(df.columns[:5])}")
            
            # 显示前几行数据
            print("\n📄 前3行数据:")
            print(df.head(3))
            
            return process_ground_truth(df)
            
        except Exception as e:
            print(f"❌ {encoding} 编码失败: {str(e)[:100]}")
            continue
    
    print("❌ 所有编码都失败")
    return {}

def process_ground_truth(df):
    """处理ground truth DataFrame"""
    try:
        print(f"\n🏷️ 原始列名: {list(df.columns)}")
        
        # 第一行包含实际的列名
        if len(df) > 0:
            first_row = df.iloc[0].values
            print(f"📋 第一行内容: {first_row[:10]}")
            
            # 使用第一行作为列名
            new_columns = []
            for i, val in enumerate(first_row):
                if pd.notna(val) and str(val).strip():
                    new_columns.append(str(val).strip())
                else:
                    new_columns.append(f'Col_{i}')
            
            df.columns = new_columns
            df = df.drop(df.index[0])  # 删除第一行
            df = df.reset_index(drop=True)
            
            print(f"✅ 更新后的列名: {list(df.columns)}")
        
        # 查找关键列
        case_col = None
        note_col = None
        question_col = None
        answer_col = None
        
        for col in df.columns:
            col_lower = str(col).lower()
            if 'case' in col_lower and 'no' in col_lower:
                case_col = col
            elif 'note' in col_lower and 'id' in col_lower:
                note_col = col
            elif col == 'Question':  # 精确匹配
                question_col = col
            elif 'answer to each question' in col_lower:  # 精确匹配答案列
                answer_col = col
        
        print(f"🔍 找到的列:")
        print(f"  Case列: {case_col}")
        print(f"  Note ID列: {note_col}")
        print(f"  Question列: {question_col}")
        print(f"  Answer列: {answer_col}")
        
        if not all([note_col, question_col, answer_col]):
            print("❌ 找不到必要的列")
            return {}
        
        # 处理数据
        ground_truth = {}
        processed_count = 0
        target_questions = ['BCLC staging', 'Child-Pugh score', 'MELD score', 'ALBI score']
        
        for idx, row in df.iterrows():
            try:
                case_no = row.get(case_col, '') if case_col else ''
                note_id = row.get(note_col, '')
                question = row.get(question_col, '')
                answer = row.get(answer_col, '')
                
                if note_id and question:
                    key = str(note_id).strip()
                    if key not in ground_truth:
                        ground_truth[key] = {}
                    
                    ground_truth[key][question] = {
                        'answer': str(answer).strip(),
                        'case_no': case_no
                    }
                    processed_count += 1
                    
                    # 只显示前四个问题
                    if question in target_questions:
                        print(f"  📝 {note_id} - {question}: {answer}")
                    
            except Exception as e:
                print(f"❌ 处理第{idx}行时出错: {e}")
                continue
        
        print(f"✅ 成功处理 {processed_count} 条记录，涉及 {len(ground_truth)} 个案例")
        
        # 显示每个案例的前四个问题
        for note_id in list(ground_truth.keys())[:3]:  # 只显示前3个案例
            print(f"\n📂 案例 {note_id} 的前四个问题:")
            for q in target_questions:
                if q in ground_truth[note_id]:
                    ans = ground_truth[note_id][q]['answer']
                    print(f"    {q}: {ans}")
        
        return ground_truth
        
    except Exception as e:
        print(f"❌ 处理ground truth数据时出错: {e}")
        return {}

def extract_first_four_from_html(html_file):
    """从HTML文件中提取前四个问题的答案"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        answers = {}
        
        # 查找score-section (前四个问题)
        score_sections = soup.find_all('div', class_='score-section')
        print(f"  🔍 找到 {len(score_sections)} 个score-section")
        
        for section in score_sections:
            h4_elem = section.find('h4')
            if h4_elem:
                question_title = h4_elem.get_text(strip=True)
                print(f"    📝 问题: {question_title}")
                
                # 查找答案 - 只提取Answer:后面的内容，不包括reasoning
                answer_text = "未找到答案"
                
                # 方法1：查找包含"Answer:"的div，然后提取strong标签后的内容
                score_details = section.find('div', class_='score-details')
                if score_details:
                    answer_divs = score_details.find_all('div')
                    for div in answer_divs:
                        strong_elem = div.find('strong')
                        if strong_elem and 'Answer:' in strong_elem.get_text():
                            # 提取strong标签后的文本内容
                            answer_text = div.get_text().replace('Answer:', '').strip()
                            # 如果有多个部分，只取第一部分（去掉reasoning）
                            if 'Based on' in answer_text:
                                answer_text = answer_text.split('Based on')[0].strip()
                            print(f"    ✅ 答案: {answer_text}")
                            break
                
                # 方法2：如果方法1失败，尝试直接查找Answer:
                if answer_text == "未找到答案":
                    all_divs = section.find_all('div')
                    for div in all_divs:
                        div_text = div.get_text(strip=True)
                        if 'Answer:' in div_text and div.find('strong'):
                            # 只提取Answer:后面的部分，到reasoning之前
                            answer_part = div_text.replace('Answer:', '').strip()
                            if 'Based on' in answer_part:
                                answer_part = answer_part.split('Based on')[0].strip()
                            answer_text = answer_part
                            print(f"    ✅ 答案: {answer_text}")
                            break
                
                answers[question_title] = answer_text
        
        return answers
        
    except Exception as e:
        print(f"❌ 解析HTML文件 {html_file} 时出错: {e}")
        return {}

def compare_accuracy():
    """比较前四个问题的准确性"""
    print("🚀 开始比较前四个问题的准确性...")
    print("=" * 60)
    
    # 加载ground truth
    ground_truth = load_ground_truth_debug()
    if not ground_truth:
        print("❌ 无法加载ground truth数据")
        return
    
    print(f"\n✅ 加载了 {len(ground_truth)} 个案例的ground truth")
    
    # 处理HTML文件
    html_dir = 'excel_based_analysis_20cases'
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    # HTML中的问题标题映射到ground truth的键
    html_to_gt_mapping = {
        'BCLC Staging': 'BCLC staging',
        'Child-Pugh score': 'Child-Pugh score', 
        'MELD score': 'MELD score',
        'ALBI score': 'ALBI score'
    }
    
    target_questions = ['BCLC staging', 'Child-Pugh score', 'MELD score', 'ALBI score']
    comparison_results = []
    
    for html_file in sorted(html_files):
        note_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
        print(f"\n📄 处理案例: {note_id}")
        
        html_path = os.path.join(html_dir, html_file)
        extracted_answers = extract_first_four_from_html(html_path)
        
        if note_id in ground_truth:
            gt_data = ground_truth[note_id]
            
            for question in target_questions:
                if question in gt_data:
                    gt_answer = gt_data[question]['answer']
                    
                    # 在extracted_answers中查找对应的HTML标题
                    llm_answer = "未找到"
                    for html_title, gt_title in html_to_gt_mapping.items():
                        if gt_title == question and html_title in extracted_answers:
                            llm_answer = extracted_answers[html_title]
                            break
                    
                    # 标准化答案格式
                    def normalize_answer(answer, question_type):
                        """标准化答案格式"""
                        answer = str(answer).strip()
                        
                        if question_type == "BCLC staging":
                            # "Stage A" -> "A", "Stage 0" -> "0"
                            if answer.startswith("Stage "):
                                return answer.replace("Stage ", "")
                            return answer
                            
                        elif question_type == "Child-Pugh score":
                            # "Class A" -> "A", "Class B" -> "B"
                            if answer.startswith("Class "):
                                return answer.replace("Class ", "")
                            return answer
                            
                        else:
                            # MELD score 和 ALBI score 保持原格式
                            return answer
                    
                    # 判断是否正确
                    is_correct = False
                    if gt_answer == '-' and (llm_answer == '未找到' or llm_answer == '-' or llm_answer == ''):
                        is_correct = True
                    elif gt_answer != '-' and llm_answer != '未找到':
                        # 标准化后比较
                        gt_norm = normalize_answer(gt_answer, question).lower()
                        llm_norm = normalize_answer(llm_answer, question).lower()
                        is_correct = gt_norm == llm_norm
                    
                    comparison_results.append({
                        'Note ID': note_id,
                        'Question': question,
                        'Ground Truth': gt_answer,
                        'LLM Answer': llm_answer,
                        'Correct': is_correct
                    })
                    
                    # 显示标准化后的比较结果
                    if gt_answer != '-' and llm_answer != '未找到':
                        gt_norm = normalize_answer(gt_answer, question)
                        llm_norm = normalize_answer(llm_answer, question)
                        print(f"  {question}: GT='{gt_norm}' LLM='{llm_norm}' {'✅' if is_correct else '❌'}")
                    else:
                        print(f"  {question}: GT='{gt_answer}' LLM='{llm_answer}' {'✅' if is_correct else '❌'}")
    
    # 计算统计
    if comparison_results:
        df = pd.DataFrame(comparison_results)
        
        print("\n📊 准确性统计:")
        print("=" * 50)
        
        for question in target_questions:
            q_data = df[df['Question'] == question]
            if len(q_data) > 0:
                accuracy = q_data['Correct'].sum() / len(q_data) * 100
                print(f"{question}: {accuracy:.1f}% ({q_data['Correct'].sum()}/{len(q_data)})")
        
        overall = df['Correct'].sum() / len(df) * 100
        print(f"\n整体准确性: {overall:.1f}% ({df['Correct'].sum()}/{len(df)})")
        
        # 保存结果
        df.to_csv('accuracy_comparison_first_four.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 结果已保存: accuracy_comparison_first_four.csv")
        
        return df
    else:
        print("❌ 没有找到可比较的数据")
        return None

if __name__ == "__main__":
    compare_accuracy() 