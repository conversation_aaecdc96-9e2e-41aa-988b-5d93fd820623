#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个医疗案例文件格式化工具
专门处理断句、缩进、换行问题，提高可读性
保持所有内容完全不变，只优化格式
"""

import re
from pathlib import Path

def format_medical_case(content):
    """
    格式化医疗案例文本，改善可读性但不改变内容
    """
    lines = content.split('\n')
    formatted_lines = []
    
    for line in lines:
        # 移除行尾空白但保留原有内容
        line = line.rstrip()
        
        # 保持原有的空行
        if not line.strip():
            formatted_lines.append('')
            continue
            
        # 处理不同类型的行
        if re.match(r'^Case ID:', line):
            # Case ID行：确保格式清晰
            formatted_lines.append('=' * 80)
            formatted_lines.append(line)
            formatted_lines.append('=' * 80)
            
        elif re.match(r'^={40,}', line):
            # 已有的分隔线，保持不变
            formatted_lines.append(line)
            
        elif re.match(r'^MEDICAL TEXT:', line):
            # 医疗文本标题
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('=' * 50)
            
        elif re.match(r'^[A-Z][A-Z\s]+:', line) and len(line.split(':')[0]) < 40:
            # 主要章节标题（如"History of Present Illness:", "Physical Exam:"等）
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('-' * min(50, len(line)))
            
        elif re.match(r'^[A-Z\s]+:$', line) and len(line) < 30:
            # 简短的全大写标题
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('-' * min(30, len(line)))
            
        elif re.match(r'^#\s+[A-Z]', line):
            # 医疗记录中的段落标记（如"# Hepatocellular carcinoma:"）
            formatted_lines.append('')
            formatted_lines.append(line)
            
        elif re.match(r'^\d+\.\s+', line):
            # 编号列表项
            formatted_lines.append('')
            formatted_lines.append(line)
            
        elif re.match(r'^[A-Z][a-z]+\s+(Medications|Instructions|Diagnosis|Condition):', line):
            # 出院相关标题
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('-' * min(40, len(line)))
            
        elif re.match(r'^(ADMISSION|DISCHARGE|RELEVANT)', line):
            # 实验室结果标题
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('=' * min(20, len(line)))
            
        elif re.match(r'^(PATHOLOGY|IMAGING|IMPRESSION):', line):
            # 病理和影像标题
            formatted_lines.append('')
            formatted_lines.append(line)
            formatted_lines.append('=' * min(15, len(line)))
            
        else:
            # 普通文本行：处理长行的换行
            if len(line) > 120 and not re.match(r'^\s*[\w\-]+:', line):
                # 长行且不是标签行，尝试在合适位置换行
                words = line.split()
                current_line = ''
                indent = len(line) - len(line.lstrip())  # 保持原有缩进
                
                for word in words:
                    if len(current_line + word) > 80:
                        if current_line:
                            formatted_lines.append(current_line.rstrip())
                            current_line = ' ' * (indent + 2) + word + ' '  # 续行增加缩进
                        else:
                            current_line = word + ' '
                    else:
                        current_line += word + ' '
                
                if current_line:
                    formatted_lines.append(current_line.rstrip())
            else:
                formatted_lines.append(line)
    
    return '\n'.join(formatted_lines)

def process_case_file(input_file):
    """处理单个案例文件"""
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"❌ 文件不存在: {input_file}")
        return
    
    # 读取原始文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 格式化内容
    formatted_content = format_medical_case(content)
    
    # 生成输出文件名
    output_file = input_path.parent / f"formatted_{input_path.name}"
    
    # 保存格式化后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(formatted_content)
        print(f"✅ 格式化完成: {output_file}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return
    
    # 统计改进效果
    original_lines = content.count('\n') + 1
    formatted_lines = formatted_content.count('\n') + 1
    long_lines_original = len([line for line in content.split('\n') if len(line) > 120])
    long_lines_formatted = len([line for line in formatted_content.split('\n') if len(line) > 120])
    
    print(f"📊 格式化统计:")
    print(f"  • 原始行数: {original_lines}")
    print(f"  • 格式化后行数: {formatted_lines}")
    print(f"  • 增加行数: {formatted_lines - original_lines}")
    print(f"  • 减少超长行: {long_lines_original - long_lines_formatted}")

if __name__ == "__main__":
    # 处理指定的文件
    input_file = "extracted_cases_20250715/11265636-DS-13.txt"
    process_case_file(input_file)