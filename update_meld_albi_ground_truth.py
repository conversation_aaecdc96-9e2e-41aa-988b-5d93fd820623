#!/usr/bin/env python3
"""
更新MELD和ALBI评分的标准答案并重新计算准确性
"""

import pandas as pd
import numpy as np

def main():
    # 读取现有的准确性比较文件
    df = pd.read_csv('accuracy_comparison_20_cases_main_questions.csv')
    
    # 新的MELD评分标准答案
    new_meld_gt = {
        '10056223-DS-5': 13,
        '10151324-DS-18': 9,
        '10225793-DS-34': 14,
        '10747475-DS-4': 13,
        '10880579-DS-11': 19,
        '10960817-DS-14': 22,
        '11102747-DS-2': 12,
        '11198012-DS-6': 11,
        '11265636-DS-13': 16,
        '11327487-DS-19': 9,
        '11329198-DS-5': 16,
        '11349875-DS-8': 8,
        '11417994-DS-9': 24,
        '11419849-DS-24': 9,
        '11455644-DS-15': 11,
        '11714491-DS-4': 16,
        '11914986-DS-12': 24,
        '11960904-DS-5': 14,
        '12032388-DS-16': 14,
        '12344358-DS-12': 15
    }
    
    # 新的ALBI评分标准答案
    new_albi_gt = {
        '10056223-DS-5': -1.37,
        '10151324-DS-18': -2.79,
        '10225793-DS-34': -1.66,
        '10747475-DS-4': -1.43,
        '10880579-DS-11': -1.02,
        '10960817-DS-14': -2.35,
        '11102747-DS-2': -1.91,
        '11198012-DS-6': -2.32,
        '11265636-DS-13': -2.23,
        '11327487-DS-19': -2.22,
        '11329198-DS-5': -1.6,
        '11349875-DS-8': -2.59,
        '11417994-DS-9': -0.3,
        '11419849-DS-24': -2.33,
        '11455644-DS-15': -1.64,
        '11714491-DS-4': -1.27,
        '11914986-DS-12': -0.68,
        '11960904-DS-5': -1.91,
        '12032388-DS-16': -1.49,
        '12344358-DS-12': -2.85
    }
    
    print("正在更新MELD和ALBI评分的标准答案...")
    
    # 更新标准答案
    for idx, row in df.iterrows():
        case_id = row['case_id']
        
        # 更新MELD标准答案
        if case_id in new_meld_gt:
            df.at[idx, 'meld_gt'] = new_meld_gt[case_id]
        
        # 更新ALBI标准答案
        if case_id in new_albi_gt:
            df.at[idx, 'albi_gt'] = new_albi_gt[case_id]
    
    # 重新计算MELD准确性
    def calculate_meld_accuracy(gt, pred):
        if pd.isna(gt) or pd.isna(pred) or gt == '-' or pred == '-':
            return "No Ground Truth", 0.0
        
        try:
            gt_val = float(gt)
            pred_val = float(pred)
            
            if abs(gt_val - pred_val) == 0:
                return "Correct", 1.0
            elif abs(gt_val - pred_val) <= 1:
                return "Close (±1)", 0.8
            else:
                return "Incorrect", 0.0
        except:
            return "Error", 0.0
    
    # 重新计算ALBI准确性
    def calculate_albi_accuracy(gt, pred):
        if pd.isna(gt) or pd.isna(pred) or gt == '-' or pred == '-':
            return "No Ground Truth", 0.0
        
        try:
            gt_val = float(gt)
            pred_val = float(pred)
            
            if abs(gt_val - pred_val) == 0:
                return "Correct", 1.0
            elif abs(gt_val - pred_val) <= 0.2:
                return "Close (±0.2)", 0.8
            else:
                return "Incorrect", 0.0
        except:
            return "Error", 0.0
    
    # 应用新的评估标准
    for idx, row in df.iterrows():
        # MELD评估
        meld_status, meld_score = calculate_meld_accuracy(row['meld_gt'], row['meld_llm'])
        df.at[idx, 'meld_status'] = meld_status
        df.at[idx, 'meld_score'] = meld_score
        
        # ALBI评估
        albi_status, albi_score = calculate_albi_accuracy(row['albi_gt'], row['albi_llm'])
        df.at[idx, 'albi_status'] = albi_status
        df.at[idx, 'albi_score'] = albi_score
    
    # 保存更新后的结果
    output_file = 'accuracy_comparison_20_cases_updated.csv'
    df.to_csv(output_file, index=False)
    
    print(f"更新后的准确性比较已保存到: {output_file}")
    
    # 计算并显示统计信息
    print("\n=== 更新后的准确性统计 ===")
    
    # MELD统计
    meld_correct = (df['meld_score'] == 1.0).sum()
    meld_close = (df['meld_score'] == 0.8).sum()
    meld_incorrect = (df['meld_score'] == 0.0).sum()
    meld_total = len(df)
    
    print(f"\nMELD评分准确性:")
    print(f"  完全正确: {meld_correct}/{meld_total} ({meld_correct/meld_total*100:.1f}%)")
    print(f"  接近正确: {meld_close}/{meld_total} ({meld_close/meld_total*100:.1f}%)")
    print(f"  错误: {meld_incorrect}/{meld_total} ({meld_incorrect/meld_total*100:.1f}%)")
    print(f"  平均得分: {df['meld_score'].mean():.3f}")
    
    # ALBI统计
    albi_correct = (df['albi_score'] == 1.0).sum()
    albi_close = (df['albi_score'] == 0.8).sum()
    albi_incorrect = (df['albi_score'] == 0.0).sum()
    albi_total = len(df)
    
    print(f"\nALBI评分准确性:")
    print(f"  完全正确: {albi_correct}/{albi_total} ({albi_correct/albi_total*100:.1f}%)")
    print(f"  接近正确: {albi_close}/{albi_total} ({albi_close/albi_total*100:.1f}%)")
    print(f"  错误: {albi_incorrect}/{albi_total} ({albi_incorrect/albi_total*100:.1f}%)")
    print(f"  平均得分: {df['albi_score'].mean():.3f}")
    
    # 显示详细的比较结果
    print(f"\n=== 详细比较结果 ===")
    print("案例ID | MELD (标准/预测/状态) | ALBI (标准/预测/状态)")
    print("-" * 80)
    
    for idx, row in df.iterrows():
        case_id = row['case_id']
        meld_gt = row['meld_gt']
        meld_llm = row['meld_llm']
        meld_status = row['meld_status']
        albi_gt = row['albi_gt']
        albi_llm = row['albi_llm']
        albi_status = row['albi_status']
        
        print(f"{case_id} | MELD ({meld_gt}/{meld_llm}/{meld_status}) | ALBI ({albi_gt}/{albi_llm}/{albi_status})")
    
    return df

if __name__ == "__main__":
    main() 