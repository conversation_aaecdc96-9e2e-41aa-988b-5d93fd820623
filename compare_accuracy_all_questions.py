#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较前20个案例所有问题的准确性
对比生成的HTML报告答案与MIMIC标准答案
"""

import json
import pandas as pd
import re
import os
from bs4 import BeautifulSoup
from hcc_analyzer_simple import load_hcc_questions, load_classification_data

def load_ground_truth():
    """加载标准答案"""
    try:
        with open('ground_truth_20cases.json', 'r', encoding='utf-8') as f:
            ground_truth = json.load(f)
        print(f"📊 加载了 {len(ground_truth)} 个案例的标准答案")
        return ground_truth
    except Exception as e:
        print(f"❌ 加载标准答案失败: {e}")
        return {}

def extract_answers_from_html(html_file):
    """从HTML报告中提取问题和答案"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 找到所有问题项
        question_items = soup.find_all('div', class_='question-item')
        
        answers = {}
        for item in question_items:
            # 提取问题标题
            h4 = item.find('h4')
            if h4:
                question = h4.get_text().strip()
                
                # 提取答案
                answer_spans = item.find_all('span', class_='value')
                if len(answer_spans) >= 1:
                    answer = answer_spans[0].get_text().strip()
                    
                    # 提取置信度
                    confidence_span = item.find('span', class_='confidence-score')
                    confidence = confidence_span.get_text().strip() if confidence_span else "N/A"
                    
                    answers[question] = {
                        'answer': answer,
                        'confidence': confidence
                    }
        
        return answers
    
    except Exception as e:
        print(f"❌ 解析HTML失败 {html_file}: {e}")
        return {}

def create_question_mapping():
    """创建问题与标准答案字段的映射"""
    
    # 基于MIMIC数据集的字段映射
    # 注意：只映射MIMIC数据集中有对应字段的问题
    mapping = {
        # 基本信息 - MIMIC数据集中没有age和gender字段
        "1.1 Age": {"field": "age", "extract_func": "extract_age"},
        "1.2 Gender": {"field": "gender", "extract_func": "extract_gender"},
        
        # 肿瘤特征 
        "2.1 Number of confirmed HCC": {"field": "number", "type": "direct"},
        "2.2 Largest size of HCC": {"field": "size", "type": "direct"},
        
        # Child-Pugh相关
        "3. Child-Pugh classification": {"field": "CP.1", "type": "direct"},
        
        # Performance Status - MIMIC数据集中有PS字段
        "4.1 ECOG performance status": {"field": "PS", "type": "direct"},
        # "4.2 Extent of physical activity restriction": 没有对应字段
        "4.3 Activity status": {"field": "activity_status", "extract_func": "extract_activity_status"},
        # "4.4 Deceased or expired": 没有对应字段
        
        # 肝性脑病 - MIMIC数据集中有HE字段
        "5.1 Grade of encephalopathy": {"field": "HE", "type": "direct"},
        "5.2 Diagnosis of encephalopathy": {"field": "HE", "extract_func": "extract_he_diagnosis"},
        # "5.3 Mental status": 没有对应字段
        # "5.4 level of consciousness": 没有对应字段
        
        # 腹水 - MIMIC数据集中有Ascites字段
        "6.1 Grade of ascites": {"field": "Ascites", "type": "direct"},
        "6.2 Diagnosis of ascites": {"field": "Ascites", "extract_func": "extract_ascites_diagnosis"},
        # "6.3 Discharge for ascites": 没有对应字段
        
        # 实验室指标
        "7.1 Total bilirubin": {"field": "Total bil", "type": "direct"},
        "7.2 Albumin": {"field": "Albumin", "type": "direct"},
        # "7.3 Sodium": 没有对应字段
        # "7.4 Prothrombin time": 没有对应字段
        "7.5 International normalized ratio": {"field": "INR.1", "type": "direct"},
        # "7.6 Creatinine": 没有对应字段
        # "7.7 Alpha-fetoprotein level": 没有对应字段
        # "7.8 Indocyanine green retention at 15 minutes": 没有对应字段
        
        # 血管侵犯和转移
        "8.1 Presence of macrovascular invasion": {"field": "MacroVI", "type": "direct"},
        # "8.2 Presence of lymphovascular invasion": 没有对应字段
        # "8.3 Extent of invasion": 没有对应字段
        # "8.4 Presence of portal vein thrombosis": 没有对应字段
        
        # 转移
        # "9.1 Presence of metastatic lymph node": 没有对应字段
        "9.2 Presence of extrahepatic spread": {"field": "ES", "type": "direct"},
        # "9.3 Presence of metastatic HCC": 没有对应字段
        # "9.4 Site of distant metastasis": 没有对应字段
        
        # 其他
        "10.1 Presence of cirrhosis": {"field": "cirrhosis", "extract_func": "extract_cirrhosis"},
        # "10.2. Severe comorbidities": 没有对应字段但可能可以用Comorbid_sig
        # "11. Prior treatment received": 没有对应字段
        # "12. Liver transplant": 没有对应字段
        # "13.1 Histological tumour grade": 没有对应字段
        # "13.2 Histological tumour type": 没有对应字段
        # "13.3 Margin status": 没有对应字段
        # "13.4 Presence of satellitosis": 没有对应字段
    }
    
    return mapping

def normalize_answer(answer, question_type=None):
    """标准化答案格式"""
    if not answer or answer == "N/A" or answer == "Not mentioned" or answer == "Not available":
        return "Not available"
    
    answer = str(answer).strip().lower()
    
    # Child-Pugh规范化
    if "child" in answer or "class" in answer:
        if "a" in answer:
            return "A"
        elif "b" in answer:
            return "B"
        elif "c" in answer:
            return "C"
    
    # BCLC规范化
    if "stage" in answer or "bclc" in answer:
        if "0" in answer:
            return "0"
        elif "a" in answer:
            return "A"
        elif "b" in answer:
            return "B"
        elif "c" in answer:
            return "C"
        elif "d" in answer:
            return "D"
    
    # 是/否问题规范化
    if answer in ["yes", "present", "positive", "detected", "found"]:
        return "Yes"
    elif answer in ["no", "absent", "not detected", "not found", "none"]:
        return "No"
    elif answer in ["negative"]:
        return "Negative"
    
    # 数值规范化
    if re.match(r'^[\d\.]+', answer):
        numeric_value = re.findall(r'[\d\.]+', answer)[0]
        try:
            # 尝试转换为浮点数再转回字符串，统一格式
            return str(float(numeric_value))
        except:
            return numeric_value
    
    return answer.title()

def compare_single_case(case_id, html_answers, ground_truth_case, question_mapping):
    """比较单个案例的答案"""
    
    results = []
    
    for question, answer_data in html_answers.items():
        html_answer = answer_data['answer']
        confidence = answer_data['confidence']
        
        # 查找映射
        mapping_info = question_mapping.get(question)
        if not mapping_info:
            # 如果没有直接映射，跳过
            continue
        
        # 获取标准答案
        field = mapping_info['field']
        ground_truth_answer = ground_truth_case.get(field)
        
        # 标准化答案
        normalized_html = normalize_answer(html_answer)
        normalized_gt = normalize_answer(ground_truth_answer)
        
        # 判断是否匹配
        is_correct = False
        if normalized_html == normalized_gt:
            is_correct = True
        elif normalized_html == "Not available" and (not normalized_gt or normalized_gt == "nan"):
            is_correct = True
        elif (normalized_html == "No" and normalized_gt == "Not available") or (normalized_html == "Not available" and normalized_gt == "No"):
            # 只在特定的是/否问题中认为 "No" 和 "Not available" 等价
            # 这主要适用于存在性问题（presence questions）
            if any(keyword in question.lower() for keyword in ["presence", "diagnosis", "grade", "status"]):
                is_correct = True
        
        results.append({
            'case_id': case_id,
            'question': question,
            'html_answer': html_answer,
            'ground_truth': ground_truth_answer,
            'normalized_html': normalized_html,
            'normalized_gt': normalized_gt,
            'is_correct': is_correct,
            'confidence': confidence,
            'field': field
        })
    
    return results

def generate_accuracy_report():
    """生成完整的准确率报告"""
    
    print("🔍 开始生成准确率报告...")
    
    # 加载数据
    ground_truth = load_ground_truth()
    if not ground_truth:
        return
    
    question_mapping = create_question_mapping()
    
    # 获取前20个案例ID
    classification_data = load_classification_data()
    case_ids = list(classification_data.keys())[:20]
    
    all_results = []
    processed_cases = 0
    
    print(f"\n📊 开始比较 {len(case_ids)} 个案例...")
    
    for case_id in case_ids:
        print(f"\n📄 处理案例: {case_id}")
        
        # 检查HTML文件是否存在
        html_file = f"excel_based_analysis_20cases/hcc_analysis_{case_id}.html"
        if not os.path.exists(html_file):
            print(f"⚠️ HTML文件不存在: {html_file}")
            continue
        
        # 检查标准答案是否存在
        if case_id not in ground_truth:
            print(f"⚠️ 标准答案不存在: {case_id}")
            continue
        
        # 提取HTML答案
        html_answers = extract_answers_from_html(html_file)
        if not html_answers:
            print(f"⚠️ 无法提取HTML答案: {case_id}")
            continue
        
        print(f"  📋 提取到 {len(html_answers)} 个问题的答案")
        
        # 比较答案
        case_results = compare_single_case(
            case_id, 
            html_answers, 
            ground_truth[case_id], 
            question_mapping
        )
        
        all_results.extend(case_results)
        processed_cases += 1
        
        # 显示案例统计
        correct_count = sum(1 for r in case_results if r['is_correct'])
        total_count = len(case_results)
        if total_count > 0:
            accuracy = correct_count / total_count * 100
            print(f"  ✅ 案例准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")
    
    print(f"\n📊 处理完成! 总共处理了 {processed_cases} 个案例")
    
    # 生成总体统计
    if all_results:
        df = pd.DataFrame(all_results)
        
        print(f"\n📈 总体统计:")
        print(f"总问题数: {len(all_results)}")
        
        correct_total = df['is_correct'].sum()
        total_questions = len(df)
        overall_accuracy = correct_total / total_questions * 100
        
        print(f"正确答案: {correct_total}")
        print(f"总体准确率: {correct_total}/{total_questions} ({overall_accuracy:.1f}%)")
        
        # 清理confidence列，转换为数值
        df['confidence_numeric'] = pd.to_numeric(df['confidence'], errors='coerce')
        
        # 按问题类型统计
        print(f"\n📋 按问题统计:")
        question_stats = df.groupby('question').agg({
            'is_correct': ['count', 'sum'],
            'confidence_numeric': 'mean'
        }).round(2)
        
        question_stats.columns = ['总数', '正确数', '平均置信度']
        question_stats['准确率%'] = (question_stats['正确数'] / question_stats['总数'] * 100).round(1)
        
        print(question_stats.to_string())
        
        # 保存详细结果
        output_file = "accuracy_comparison_detailed_final.csv"
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n📁 详细结果已保存到: {output_file}")
        
        # 保存统计结果
        stats_file = "accuracy_comparison_summary_final.csv"
        question_stats.to_csv(stats_file, encoding='utf-8')
        print(f"📁 统计结果已保存到: {stats_file}")
        
        return df, question_stats
    
    else:
        print("❌ 没有成功比较任何结果")
        return None, None

if __name__ == "__main__":
    detailed_results, summary_stats = generate_accuracy_report()
    
    if detailed_results is not None:
        print(f"\n✅ 准确率比较完成!")
        print(f"📊 详细结果: {len(detailed_results)} 条记录")
        print(f"📈 总体准确率: {detailed_results['is_correct'].mean()*100:.1f}%")
    else:
        print("❌ 准确率比较失败") 