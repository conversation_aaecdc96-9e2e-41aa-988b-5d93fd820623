import pandas as pd

# Read the Excel file
df = pd.read_excel('HCC cross-referencing platform structured questions_20250704.xlsx')

print('Columns:', df.columns.tolist())
print('Shape:', df.shape)
print('\nAll data:')

for i, row in df.iterrows():
    structure = row.iloc[1] if pd.notna(row.iloc[1]) else ""
    questions = row.iloc[2] if pd.notna(row.iloc[2]) else ""
    remarks = row.iloc[3] if pd.notna(row.iloc[3]) else ""
    
    print(f'Row {i}:')
    print(f'  Structure: {repr(structure)}')
    print(f'  Questions: {repr(questions)}')
    print(f'  Remarks: {repr(remarks)}')
    print('---')
