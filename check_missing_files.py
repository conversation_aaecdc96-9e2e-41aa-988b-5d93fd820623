#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查哪些文件不存在
"""

import os

# 配置
TXT_FOLDER = r"C:\Users\<USER>\Desktop\case_new\txts"

# 需要处理的文件列表
FILES_TO_PROCESS = [
    "10056223-DS-5",
    "10151324-DS-16",
    "10151324-DS-18",
    "10225793-DS-33",
    "10225793-DS-34",
    "10225793-DS-36",
    "10262565-DS-19",
    "10262565-DS-20",
    "10388675-DS-19",
    "10388675-DS-20",
    "10666715-DS-8",
    "10747475-DS-4",
    "10760122-DS-21",
    "10880579-DS-11",
    "10902714-DS-17",
    "10923555-DS-9",
    "10960817-DS-14",
    "11102747-DS-2",
    "11198012-DS-6",
    "11198012-DS-7"
]

def main():
    """主函数"""
    print("检查文件是否存在...")
    
    missing_files = []
    existing_files = []
    
    for file_id in FILES_TO_PROCESS:
        txt_path = os.path.join(TXT_FOLDER, f"{file_id}.txt")
        if not os.path.exists(txt_path):
            missing_files.append(file_id)
        else:
            existing_files.append(file_id)
    
    print(f"\n✅ 存在的文件 ({len(existing_files)}/{len(FILES_TO_PROCESS)}):")
    for file_id in existing_files:
        print(f"- {file_id}.txt")
    
    print(f"\n❌ 缺失的文件 ({len(missing_files)}/{len(FILES_TO_PROCESS)}):")
    for file_id in missing_files:
        print(f"- {file_id}.txt")

if __name__ == "__main__":
    main() 