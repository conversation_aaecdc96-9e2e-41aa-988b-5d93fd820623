#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较HTML报告中的前四个问题与标准答案文件的准确率
"""

import os
import re
from bs4 import BeautifulSoup

def parse_ground_truth_file(file_path):
    """解析标准答案文件"""
    ground_truth = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        parts = line.strip().split('\t')

        if len(parts) >= 10:
            case_id = parts[1]
            question_num = parts[2]
            question_type = parts[4]
            answer = parts[9].strip()  # 答案在第10列（索引9）

            if case_id not in ground_truth:
                ground_truth[case_id] = {}

            # 映射问题类型
            if 'BCLC staging' in question_type:
                ground_truth[case_id]['BCLC'] = answer
            elif 'Child-Pugh score' in question_type:
                ground_truth[case_id]['Child-Pugh'] = answer
            elif 'MELD score' in question_type:
                ground_truth[case_id]['MELD'] = answer
            elif 'ALBI score' in question_type:
                ground_truth[case_id]['ALBI'] = answer
    
    return ground_truth

def extract_scores_from_html(html_file):
    """从HTML文件中提取前四个问题的答案"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        scores = {
            'BCLC': '',
            'Child-Pugh': '',
            'MELD': '',
            'ALBI': ''
        }

        # 查找所有score-section
        score_sections = soup.find_all('div', class_='score-section')

        for section in score_sections:
            # 获取标题
            title_elem = section.find('h4')
            if not title_elem:
                continue

            title = title_elem.get_text(strip=True)

            # 获取答案
            score_details = section.find('div', class_='score-details')
            if not score_details:
                continue

            # 查找Answer行
            answer_div = score_details.find('div')
            if answer_div:
                answer_text = answer_div.get_text(strip=True)
                # 提取Answer:后面的内容
                if 'Answer:' in answer_text:
                    answer = answer_text.split('Answer:')[1].strip()
                else:
                    answer = answer_text.strip()

                # 匹配问题类型
                if 'BCLC' in title and 'Staging' in title:
                    scores['BCLC'] = answer
                elif 'Child-Pugh' in title and 'score' in title:
                    scores['Child-Pugh'] = answer
                elif 'MELD' in title and 'score' in title:
                    scores['MELD'] = answer
                elif 'ALBI' in title and 'score' in title:
                    scores['ALBI'] = answer

        return scores

    except Exception as e:
        print(f"❌ 解析HTML文件失败 {html_file}: {e}")
        return {'BCLC': '', 'Child-Pugh': '', 'MELD': '', 'ALBI': ''}

def normalize_answer(answer):
    """标准化答案格式"""
    if not answer or answer.lower() in ['not available', 'not mentioned', 'not', 'available', 'n/a', 'na']:
        return '-'
    
    # 移除多余的空格和特殊字符
    answer = str(answer).strip()
    
    # 处理ALBI分数的精度
    if answer.startswith('-') and '.' in answer:
        try:
            # 保留两位小数
            float_val = float(answer)
            return f"{float_val:.2f}"
        except:
            pass
    
    return answer

def compare_answers(pred, truth):
    """比较预测答案和标准答案"""
    pred_norm = normalize_answer(pred)
    truth_norm = normalize_answer(truth)
    
    # 特殊处理ALBI分数的小数精度差异
    if pred_norm.startswith('-') and truth_norm.startswith('-'):
        try:
            pred_float = float(pred_norm)
            truth_float = float(truth_norm)
            # 允许0.1的误差
            if abs(pred_float - truth_float) <= 0.1:
                return True
        except:
            pass
    
    return pred_norm == truth_norm

def calculate_accuracy():
    """计算准确率"""
    print("🔍 开始计算HCC分析结果准确率...")
    print("=" * 60)
    
    # 解析标准答案文件
    ground_truth_file = "1 10056223-DS-5 1 0.1 BCLC staging A"
    ground_truth = parse_ground_truth_file(ground_truth_file)

    print(f"📊 加载了 {len(ground_truth)} 个案例的标准答案")
    
    total_questions = 0
    correct_answers = 0
    detailed_results = []
    
    # 统计各指标的准确率
    metrics_stats = {
        'BCLC': {'correct': 0, 'total': 0},
        'Child-Pugh': {'correct': 0, 'total': 0},
        'MELD': {'correct': 0, 'total': 0},
        'ALBI': {'correct': 0, 'total': 0}
    }
    
    # 遍历每个case
    for case_id, truth_data in ground_truth.items():
        html_file = f"html_reports_split_cases_20250104/hcc_analysis_{case_id}.html"
        
        if not os.path.exists(html_file):
            print(f"⚠️  文件不存在: {html_file}")
            continue
        
        print(f"\n📄 分析 {case_id}:")
        
        # 提取预测结果
        predicted = extract_scores_from_html(html_file)
        
        case_results = {
            'case_id': case_id,
            'BCLC': {'pred': '', 'truth': '', 'correct': False},
            'Child-Pugh': {'pred': '', 'truth': '', 'correct': False},
            'MELD': {'pred': '', 'truth': '', 'correct': False},
            'ALBI': {'pred': '', 'truth': '', 'correct': False}
        }
        
        # 比较每个指标
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            if metric in truth_data:
                pred_value = predicted.get(metric, '')
                truth_value = truth_data[metric]
                
                is_correct = compare_answers(pred_value, truth_value)
                
                case_results[metric] = {
                    'pred': normalize_answer(pred_value),
                    'truth': normalize_answer(truth_value),
                    'correct': is_correct
                }
                
                # 更新统计
                metrics_stats[metric]['total'] += 1
                total_questions += 1
                
                if is_correct:
                    metrics_stats[metric]['correct'] += 1
                    correct_answers += 1
                
                # 显示结果
                status = "✅" if is_correct else "❌"
                print(f"  {metric:12}: {normalize_answer(pred_value):8} vs {normalize_answer(truth_value):8} {status}")
        
        detailed_results.append(case_results)
    
    # 计算总体准确率
    overall_accuracy = (correct_answers / total_questions * 100) if total_questions > 0 else 0
    
    print("\n" + "=" * 60)
    print("📊 准确率统计结果:")
    print("=" * 60)
    
    # 各指标准确率
    for metric, stats in metrics_stats.items():
        if stats['total'] > 0:
            accuracy = stats['correct'] / stats['total'] * 100
            print(f"{metric:12}: {stats['correct']:2d}/{stats['total']:2d} = {accuracy:5.1f}%")
    
    print(f"\n{'总体准确率':12}: {correct_answers:2d}/{total_questions:2d} = {overall_accuracy:5.1f}%")
    
    # 错误分析
    print("\n" + "=" * 60)
    print("❌ 错误分析:")
    print("=" * 60)
    
    for result in detailed_results:
        case_id = result['case_id']
        errors = []
        
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            if not result[metric]['correct']:
                pred = result[metric]['pred']
                truth = result[metric]['truth']
                errors.append(f"{metric}: {pred}→{truth}")
        
        if errors:
            print(f"{case_id}: {', '.join(errors)}")
    
    return overall_accuracy

if __name__ == "__main__":
    accuracy = calculate_accuracy()
    print(f"\n🎯 最终准确率: {accuracy:.1f}%")
