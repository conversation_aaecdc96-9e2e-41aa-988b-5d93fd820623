#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HCC案例准确性分析结果可视化
生成准确性对比图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_accuracy_charts():
    """创建准确性分析图表"""
    
    # 数据
    questions = ['Child-Pugh\nScore', 'BCLC\nStaging', 'MELD\nScore', 'ALBI\nScore']
    accuracies = [95.8, 87.5, 66.7, 54.2]
    correct_counts = [23, 21, 16, 13]
    total_count = 24
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('HCC案例分析 - 前四个主要问题准确性分析结果', fontsize=16, fontweight='bold')
    
    # 1. 准确性条形图
    colors = ['#2E8B57', '#4682B4', '#FF6347', '#FF4500']  # 绿色->蓝色->橙色->红色
    bars = ax1.bar(questions, accuracies, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax1.set_title('各问题准确率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('准确率 (%)', fontsize=12)
    ax1.set_ylim(0, 100)
    
    # 添加数据标签
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc}%', ha='center', va='bottom', fontweight='bold')
    
    # 添加参考线
    ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='优秀线(80%)')
    ax1.axhline(y=70, color='orange', linestyle='--', alpha=0.7, label='良好线(70%)')
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 2. 正确/错误数量堆叠图
    incorrect_counts = [total_count - correct for correct in correct_counts]
    
    ax2.bar(questions, correct_counts, label='正确', color='#90EE90', alpha=0.8)
    ax2.bar(questions, incorrect_counts, bottom=correct_counts, label='错误', color='#FFB6C1', alpha=0.8)
    
    ax2.set_title('正确/错误案例数量分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('案例数量', fontsize=12)
    ax2.set_ylim(0, total_count + 2)
    ax2.legend()
    
    # 添加数据标签
    for i, (correct, incorrect) in enumerate(zip(correct_counts, incorrect_counts)):
        ax2.text(i, correct/2, str(correct), ha='center', va='center', fontweight='bold')
        if incorrect > 0:
            ax2.text(i, correct + incorrect/2, str(incorrect), ha='center', va='center', fontweight='bold')
    
    # 3. 饼图 - 总体表现
    overall_correct = sum(correct_counts)
    overall_total = len(questions) * total_count
    overall_incorrect = overall_total - overall_correct
    
    pie_labels = [f'正确\n{overall_correct}/{overall_total}\n({overall_correct/overall_total*100:.1f}%)',
                  f'错误\n{overall_incorrect}/{overall_total}\n({overall_incorrect/overall_total*100:.1f}%)']
    pie_colors = ['#90EE90', '#FFB6C1']
    
    wedges, texts, autotexts = ax3.pie([overall_correct, overall_incorrect], 
                                      labels=pie_labels, 
                                      colors=pie_colors, 
                                      autopct='', 
                                      startangle=90,
                                      textprops={'fontsize': 11, 'fontweight': 'bold'})
    
    ax3.set_title('总体准确性分布', fontsize=14, fontweight='bold')
    
    # 4. 雷达图 - 各维度表现
    angles = np.linspace(0, 2 * np.pi, len(questions), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    accuracies_radar = accuracies + [accuracies[0]]  # 闭合
    
    ax4 = plt.subplot(2, 2, 4, projection='polar')
    ax4.plot(angles, accuracies_radar, 'o-', linewidth=2, color='#4682B4')
    ax4.fill(angles, accuracies_radar, alpha=0.25, color='#4682B4')
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(questions, fontsize=10)
    ax4.set_ylim(0, 100)
    ax4.set_yticks([20, 40, 60, 80, 100])
    ax4.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=8)
    ax4.set_title('各维度准确率雷达图', fontsize=14, fontweight='bold', pad=20)
    ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig('hcc_accuracy_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('hcc_accuracy_analysis.pdf', bbox_inches='tight')
    print("📊 图表已保存为: hcc_accuracy_analysis.png 和 hcc_accuracy_analysis.pdf")
    
    return fig

def create_detailed_comparison_chart():
    """创建详细的对比分析图表"""
    
    # 数据
    data = {
        'Question': ['Child-Pugh Score', 'BCLC Staging', 'MELD Score', 'ALBI Score'],
        'Accuracy': [95.8, 87.5, 66.7, 54.2],
        'Correct': [23, 21, 16, 13],
        'Close': [0, 0, 2, 3],  # 接近正确的数量
        'Incorrect': [1, 3, 6, 8]
    }
    
    df = pd.DataFrame(data)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('HCC案例分析详细对比 - 前四个主要问题', fontsize=16, fontweight='bold')
    
    # 1. 详细准确性分析（包含"接近"类别）
    x = np.arange(len(data['Question']))
    width = 0.6
    
    # 堆叠条形图
    p1 = ax1.bar(x, df['Correct'], width, label='完全正确', color='#2E8B57', alpha=0.8)
    p2 = ax1.bar(x, df['Close'], width, bottom=df['Correct'], label='接近正确', color='#FFD700', alpha=0.8)
    p3 = ax1.bar(x, df['Incorrect'], width, bottom=df['Correct']+df['Close'], label='错误', color='#FF6347', alpha=0.8)
    
    ax1.set_title('详细准确性分析（含接近正确）', fontsize=14, fontweight='bold')
    ax1.set_ylabel('案例数量', fontsize=12)
    ax1.set_xlabel('问题类型', fontsize=12)
    ax1.set_xticks(x)
    ax1.set_xticklabels(data['Question'], rotation=45)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 添加数据标签
    for i, (correct, close, incorrect) in enumerate(zip(df['Correct'], df['Close'], df['Incorrect'])):
        if correct > 0:
            ax1.text(i, correct/2, str(correct), ha='center', va='center', fontweight='bold', color='white')
        if close > 0:
            ax1.text(i, correct + close/2, str(close), ha='center', va='center', fontweight='bold')
        if incorrect > 0:
            ax1.text(i, correct + close + incorrect/2, str(incorrect), ha='center', va='center', fontweight='bold', color='white')
    
    # 2. 准确率趋势图
    ax2.plot(data['Question'], data['Accuracy'], 'o-', linewidth=3, markersize=8, color='#4682B4')
    ax2.fill_between(data['Question'], data['Accuracy'], alpha=0.3, color='#4682B4')
    
    ax2.set_title('准确率趋势分析', fontsize=14, fontweight='bold')
    ax2.set_ylabel('准确率 (%)', fontsize=12)
    ax2.set_xlabel('问题类型', fontsize=12)
    ax2.set_ylim(40, 100)
    ax2.grid(True, alpha=0.3)
    
    # 添加数据标签
    for i, acc in enumerate(data['Accuracy']):
        ax2.text(i, acc + 2, f'{acc}%', ha='center', va='bottom', fontweight='bold')
    
    # 添加参考线
    ax2.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='优秀标准(80%)')
    ax2.axhline(y=70, color='orange', linestyle='--', alpha=0.7, label='良好标准(70%)')
    ax2.legend()
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('hcc_detailed_comparison.png', dpi=300, bbox_inches='tight')
    print("📊 详细对比图已保存为: hcc_detailed_comparison.png")
    
    return fig

def main():
    """主函数"""
    print("🎨 开始生成HCC案例准确性分析可视化图表...")
    
    # 创建主要图表
    fig1 = create_accuracy_charts()
    
    # 创建详细对比图表
    fig2 = create_detailed_comparison_chart()
    
    # 显示图表（如果在交互环境中）
    try:
        plt.show()
    except:
        pass
    
    print("✅ 所有图表生成完成！")
    print("📁 生成的文件:")
    print("   - hcc_accuracy_analysis.png (主要分析图表)")
    print("   - hcc_accuracy_analysis.pdf (PDF格式)")
    print("   - hcc_detailed_comparison.png (详细对比图表)")

if __name__ == "__main__":
    main() 