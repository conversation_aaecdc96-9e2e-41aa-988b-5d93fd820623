#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure OpenAI 客户端模块 - 处理API调用和响应
"""

import json
from openai import AzureOpenAI
from config import AZURE_CONFIG, ANALYSIS_CONFIG
from medical_rules import TNMStagingRules, ATARiskRules, QuestionTemplate

class AzureGPTClient:
    """Azure OpenAI GPT-4客户端"""
    
    def __init__(self):
        self.client = AzureOpenAI(
            api_key=AZURE_CONFIG['api_key'],
            azure_endpoint=AZURE_CONFIG['endpoint'],
            api_version=AZURE_CONFIG['api_version']
        )
        
    def create_prompt(self, pathology_content, age_info=None):
        """创建分析提示词"""
        
        tnm_rules = TNMStagingRules.get_tnm_definitions()
        ata_rules = ATARiskRules.get_ata_criteria()
        questions = QuestionTemplate.get_structured_questions()
        
        age_instruction = ""
        if age_info:
            age_instruction = f"\n**重要：根据临床数据，该患者的年龄是 {age_info} 岁。请在3.1问题中使用此年龄信息。**\n"
        
        prompt = f"""你是一位甲状腺癌病理学专家。请根据以下病理报告，按照严格的医学标准进行分析。

{age_instruction}

## 病理报告内容：
{pathology_content}

## TNM第8版分期标准：
{tnm_rules}

## ATA风险分层标准：
{ata_rules}

## 请回答以下问题：
{questions}

## **关键要求：**

### 1. 支持引用要求（非常重要）：
- **所有的supporting_quote必须是病理报告原文的直接引用**
- **绝对不能引用医学规则、分期标准、或任何系统生成的内容**
- **不能引用包含"criteria", "classification", "meets criteria", "according to", "based on", "therefore", "rationale"等词汇的内容**
- **如果某个答案基于医学推理而非原文直接提及，supporting_quote应为空字符串""**

### 2. 数据格式要求：
- 年龄：仅返回数字，不带单位
- 肿瘤大小：包含单位（cm或mm）
- 淋巴结数量：仅返回整数，如无则返回NaN
- 淋巴结大小：包含单位，如无则返回NaN

### 3. T/N分期特殊要求：
- **T分期和N分期必须基于医学标准生成，不能直接从报告中提取**
- **如果从报告中直接提取，请在答案后标注"(directly extracted from pathology report)"**

### 4. 返回格式：
请严格按照以下JSON格式返回，每个问题包含answer和supporting_quote两个字段：

```json
{{
  "1.1": {{
    "answer": "具体答案",
    "supporting_quote": "病理报告原文引用或空字符串"
  }},
  "1.2": {{
    "answer": "具体答案", 
    "supporting_quote": "病理报告原文引用或空字符串"
  }},
  ... （继续所有问题）
}}
```

请确保JSON格式完整有效，所有问题都有对应答案。"""

        return prompt
    
    def analyze_pathology(self, content, age_info=None):
        """分析病理报告"""
        try:
            # 内容长度限制
            if len(content) > ANALYSIS_CONFIG['max_content_length']:
                content = content[:ANALYSIS_CONFIG['max_content_length']] + "..."
                print(f"⚠️ 内容过长，已截取至 {ANALYSIS_CONFIG['max_content_length']} 字符")
            
            prompt = self.create_prompt(content, age_info)
            
            print("🔄 正在调用Azure OpenAI API...")
            
            response = self.client.chat.completions.create(
                model=AZURE_CONFIG['deployment'],
                messages=[
                    {"role": "system", "content": "你是一位专业的甲状腺癌病理学专家，具有丰富的TNM分期和ATA风险分层经验。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=ANALYSIS_CONFIG['temperature'],
                max_tokens=ANALYSIS_CONFIG['max_tokens'],
                timeout=ANALYSIS_CONFIG['request_timeout']
            )
            
            result = response.choices[0].message.content.strip()
            print("✅ API调用成功")
            
            return self.parse_json_response(result)
            
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            return None
    
    def parse_json_response(self, response_text):
        """解析JSON响应"""
        try:
            # 提取JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                print("❌ 响应中未找到JSON格式")
                return None
            
            json_str = response_text[json_start:json_end]
            result = json.loads(json_str)
            
            print(f"✅ 成功解析JSON，包含 {len(result)} 个问题的答案")
            return result
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始响应: {response_text[:500]}...")
            return None 