#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析MELD和ALBI分数的差异
"""

import os
import pandas as pd
import numpy as np
from bs4 import BeautifulSoup

def load_ground_truth():
    """加载ground truth数据"""
    print("🔍 加载ground truth数据...")
    
    csv_file = 'ground truth_20 MIMIC_20250714(Sheet2).csv'
    
    # 尝试不同编码
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252', 'iso-8859-1', 'windows-1252']
    
    for encoding in encodings:
        try:
            df = pd.read_csv(csv_file, encoding=encoding)
            print(f"✅ 成功使用 {encoding} 编码加载数据")
            break
        except Exception:
            continue
    else:
        print("❌ 无法读取CSV文件")
        return {}
    
    # 处理CSV结构
    if len(df.columns) > 10:
        # 使用第一行作为列名
        new_columns = []
        first_row = df.iloc[0]
        for i, val in enumerate(first_row):
            if pd.notna(val) and str(val).strip():
                new_columns.append(str(val).strip())
            else:
                new_columns.append(f'Col_{i}')
        
        df.columns = new_columns
        df = df.drop(0).reset_index(drop=True)
    
    # 处理数据
    ground_truth = {}
    
    for idx, row in df.iterrows():
        try:
            case_no = row.get('Case No', '')
            note_id = row.get('Note ID', '')
            question = row.get('Question', '')
            answer = row.get('Answer to each question', '')
            
            if pd.notna(note_id) and pd.notna(question) and pd.notna(answer):
                note_id = str(note_id).strip()
                question = str(question).strip()
                answer = str(answer).strip()
                
                if note_id not in ground_truth:
                    ground_truth[note_id] = {}
                
                ground_truth[note_id][question] = {
                    'answer': answer,
                    'case_no': case_no
                }
                
        except Exception as e:
            continue
    
    return ground_truth

def extract_meld_albi_from_html(html_file):
    """从HTML文件中提取MELD和ALBI分数"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        answers = {}
        
        # 查找score-section
        score_sections = soup.find_all('div', class_='score-section')
        
        for section in score_sections:
            h4_elem = section.find('h4')
            if h4_elem:
                question_title = h4_elem.get_text(strip=True)
                
                # 只关注MELD和ALBI
                if question_title in ['MELD score', 'ALBI score']:
                    answer_text = "未找到答案"
                    
                    # 查找答案
                    score_details = section.find('div', class_='score-details')
                    if score_details:
                        answer_divs = score_details.find_all('div')
                        for div in answer_divs:
                            strong_elem = div.find('strong')
                            if strong_elem and 'Answer:' in strong_elem.get_text():
                                answer_text = div.get_text().replace('Answer:', '').strip()
                                if 'Based on' in answer_text:
                                    answer_text = answer_text.split('Based on')[0].strip()
                                break
                    
                    # 如果方法1失败，尝试方法2
                    if answer_text == "未找到答案":
                        all_divs = section.find_all('div')
                        for div in all_divs:
                            div_text = div.get_text(strip=True)
                            if 'Answer:' in div_text and div.find('strong'):
                                answer_part = div_text.replace('Answer:', '').strip()
                                if 'Based on' in answer_part:
                                    answer_part = answer_part.split('Based on')[0].strip()
                                answer_text = answer_part
                                break
                    
                    answers[question_title] = answer_text
        
        return answers
        
    except Exception as e:
        print(f"❌ 解析HTML文件 {html_file} 时出错: {e}")
        return {}

def analyze_meld_albi_differences():
    """详细分析MELD和ALBI分数的差异"""
    print("🔍 开始详细分析MELD和ALBI分数差异...")
    print("=" * 80)
    
    # 加载ground truth
    ground_truth = load_ground_truth()
    if not ground_truth:
        print("❌ 无法加载ground truth数据")
        return
    
    print(f"✅ 加载了 {len(ground_truth)} 个案例的ground truth")
    
    # 处理HTML文件
    html_dir = 'excel_based_analysis_20cases'
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    # 存储详细分析结果
    detailed_results = []
    
    # 分类统计
    categories = {
        'MELD': {
            'gt_missing_llm_calculated': [],  # GT是"-"，LLM计算出数值
            'both_have_values_match': [],     # 都有数值且匹配
            'both_have_values_differ': [],    # 都有数值但不匹配
            'gt_has_llm_missing': [],         # GT有数值，LLM没找到
            'both_missing': []                # 都没有数值
        },
        'ALBI': {
            'gt_missing_llm_calculated': [],
            'both_have_values_match': [],
            'both_have_values_differ': [],
            'gt_has_llm_missing': [],
            'both_missing': []
        }
    }
    
    for html_file in sorted(html_files):
        note_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
        
        html_path = os.path.join(html_dir, html_file)
        extracted_answers = extract_meld_albi_from_html(html_path)
        
        if note_id in ground_truth:
            gt_data = ground_truth[note_id]
            
            for score_type in ['MELD score', 'ALBI score']:
                if score_type in gt_data:
                    gt_answer = gt_data[score_type]['answer']
                    llm_answer = extracted_answers.get(score_type, "未找到答案")
                    
                    # 分析差异类型
                    category_key = score_type.split()[0]  # 'MELD' 或 'ALBI'
                    
                    is_gt_missing = (gt_answer == '-' or gt_answer == '' or pd.isna(gt_answer))
                    is_llm_missing = (llm_answer == "未找到答案" or llm_answer == '-' or llm_answer == '')
                    
                    result_entry = {
                        'Note_ID': note_id,
                        'Score_Type': score_type,
                        'GT_Answer': gt_answer,
                        'LLM_Answer': llm_answer,
                        'GT_Missing': is_gt_missing,
                        'LLM_Missing': is_llm_missing
                    }
                    
                    if is_gt_missing and not is_llm_missing:
                        # GT缺失但LLM计算出了数值
                        categories[category_key]['gt_missing_llm_calculated'].append(result_entry)
                        result_entry['Category'] = 'GT缺失，LLM计算出数值'
                        
                    elif not is_gt_missing and not is_llm_missing:
                        # 都有数值，比较是否匹配
                        try:
                            gt_num = float(gt_answer)
                            llm_num = float(llm_answer)
                            diff = abs(gt_num - llm_num)
                            result_entry['Numeric_Difference'] = diff
                            
                            if diff < 0.01:  # 认为基本相等
                                categories[category_key]['both_have_values_match'].append(result_entry)
                                result_entry['Category'] = '数值匹配'
                            else:
                                categories[category_key]['both_have_values_differ'].append(result_entry)
                                result_entry['Category'] = '数值不匹配'
                                
                        except ValueError:
                            # 无法转换为数值，直接比较字符串
                            if str(gt_answer).strip().lower() == str(llm_answer).strip().lower():
                                categories[category_key]['both_have_values_match'].append(result_entry)
                                result_entry['Category'] = '文本匹配'
                            else:
                                categories[category_key]['both_have_values_differ'].append(result_entry)
                                result_entry['Category'] = '文本不匹配'
                                
                    elif not is_gt_missing and is_llm_missing:
                        # GT有数值但LLM没找到
                        categories[category_key]['gt_has_llm_missing'].append(result_entry)
                        result_entry['Category'] = 'GT有数值，LLM未找到'
                        
                    else:
                        # 都缺失
                        categories[category_key]['both_missing'].append(result_entry)
                        result_entry['Category'] = '都缺失数值'
                    
                    detailed_results.append(result_entry)
    
    # 打印详细分析结果
    print("\n" + "="*80)
    print("📊 MELD和ALBI分数详细差异分析")
    print("="*80)
    
    for score_name in ['MELD', 'ALBI']:
        print(f"\n🔍 {score_name} Score 分析:")
        print("-" * 60)
        
        cat = categories[score_name]
        
        print(f"1️⃣ GT缺失(-),LLM计算出数值: {len(cat['gt_missing_llm_calculated'])} 个")
        for entry in cat['gt_missing_llm_calculated']:
            print(f"   📄 {entry['Note_ID']}: GT='{entry['GT_Answer']}' → LLM='{entry['LLM_Answer']}'")
        
        print(f"\n2️⃣ 都有数值且匹配: {len(cat['both_have_values_match'])} 个")
        for entry in cat['both_have_values_match']:
            print(f"   ✅ {entry['Note_ID']}: GT='{entry['GT_Answer']}' = LLM='{entry['LLM_Answer']}'")
        
        print(f"\n3️⃣ 都有数值但不匹配: {len(cat['both_have_values_differ'])} 个")
        for entry in cat['both_have_values_differ']:
            diff_str = ""
            if 'Numeric_Difference' in entry:
                diff_str = f" (差异: {entry['Numeric_Difference']:.3f})"
            print(f"   ❌ {entry['Note_ID']}: GT='{entry['GT_Answer']}' ≠ LLM='{entry['LLM_Answer']}'{diff_str}")
        
        print(f"\n4️⃣ GT有数值,LLM未找到: {len(cat['gt_has_llm_missing'])} 个")
        for entry in cat['gt_has_llm_missing']:
            print(f"   ⚠️  {entry['Note_ID']}: GT='{entry['GT_Answer']}' → LLM='未找到'")
        
        print(f"\n5️⃣ 都缺失数值: {len(cat['both_missing'])} 个")
        for entry in cat['both_missing']:
            print(f"   ➖ {entry['Note_ID']}: GT='{entry['GT_Answer']}' = LLM='{entry['LLM_Answer']}'")
    
    # 生成汇总统计
    print(f"\n" + "="*80)
    print("📈 汇总统计")
    print("="*80)
    
    for score_name in ['MELD', 'ALBI']:
        cat = categories[score_name]
        total = sum(len(cat[key]) for key in cat.keys())
        
        print(f"\n🎯 {score_name} Score 汇总 (总计: {total} 个):")
        print(f"   • GT缺失,LLM计算: {len(cat['gt_missing_llm_calculated'])} ({len(cat['gt_missing_llm_calculated'])/total*100:.1f}%)")
        print(f"   • 数值匹配: {len(cat['both_have_values_match'])} ({len(cat['both_have_values_match'])/total*100:.1f}%)")
        print(f"   • 数值不匹配: {len(cat['both_have_values_differ'])} ({len(cat['both_have_values_differ'])/total*100:.1f}%)")
        print(f"   • GT有值,LLM缺失: {len(cat['gt_has_llm_missing'])} ({len(cat['gt_has_llm_missing'])/total*100:.1f}%)")
        print(f"   • 都缺失: {len(cat['both_missing'])} ({len(cat['both_missing'])/total*100:.1f}%)")
        
        # 计算实际准确率（排除GT缺失的情况）
        gt_has_values = len(cat['both_have_values_match']) + len(cat['both_have_values_differ']) + len(cat['gt_has_llm_missing'])
        if gt_has_values > 0:
            accuracy_when_gt_exists = len(cat['both_have_values_match']) / gt_has_values * 100
            print(f"   ⭐ 当GT有数值时的准确率: {accuracy_when_gt_exists:.1f}% ({len(cat['both_have_values_match'])}/{gt_has_values})")
    
    # 保存详细结果到CSV
    if detailed_results:
        df_detailed = pd.DataFrame(detailed_results)
        df_detailed.to_csv('meld_albi_detailed_analysis.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 详细分析结果已保存到: meld_albi_detailed_analysis.csv")
    
    print(f"\n✅ 分析完成!")
    return detailed_results

if __name__ == "__main__":
    analyze_meld_albi_differences() 