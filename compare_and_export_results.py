#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较前四个问题的准确性并导出所有结果到Excel
"""

import os
import pandas as pd
import json
import re
from bs4 import BeautifulSoup
import openpyxl
from datetime import datetime

def extract_answers_from_html(html_file):
    """从HTML文件中提取问题答案和支持句子"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        questions_data = {}
        
        # 方法1：提取顶部的BCLC、Child-Pugh、MELD、ALBI分数
        score_sections = soup.find_all('div', class_='score-section')
        for section in score_sections:
            h4_elem = section.find('h4')
            if h4_elem:
                question_title = h4_elem.get_text(strip=True)
                
                # 查找答案
                answer_div = section.find('div', string=lambda text: text and 'Answer:' in text)
                if answer_div:
                    answer_text = answer_div.get_text(strip=True).replace('Answer:', '').strip()
                else:
                    # 备用方法：查找包含"Answer:"的任何div
                    answer_divs = section.find_all('div')
                    answer_text = "未找到答案"
                    for div in answer_divs:
                        if div.get_text() and 'Answer:' in div.get_text():
                            answer_text = div.get_text().split('Answer:')[1].strip()
                            break
                
                # 查找reasoning作为支持句子
                reasoning_elem = section.find('div', class_='reasoning')
                supporting_sentences = []
                if reasoning_elem:
                    supporting_sentences.append(reasoning_elem.get_text(strip=True))
                
                questions_data[question_title] = {
                    'answer': answer_text,
                    'supporting_sentences': supporting_sentences
                }
        
        # 方法2：提取其他问题（question-item类）
        question_items = soup.find_all('div', class_='question-item')
        for item in question_items:
            h4_elem = item.find('h4')
            if h4_elem:
                question_title = h4_elem.get_text(strip=True)
                
                # 查找答案
                answer_elem = item.find('span', class_='value')
                answer_text = answer_elem.get_text(strip=True) if answer_elem else "未找到答案"
                
                # 查找支持句子
                supporting_sentences = []
                quote_links = item.find_all('span', class_='quote-link')
                for link in quote_links:
                    supporting_sentences.append(link.get_text(strip=True))
                
                questions_data[question_title] = {
                    'answer': answer_text,
                    'supporting_sentences': supporting_sentences
                }
        
        return questions_data
        
    except Exception as e:
        print(f"❌ 解析HTML文件 {html_file} 时出错: {e}")
        return {}

def load_ground_truth():
    """加载ground truth数据"""
    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252', 'iso-8859-1']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv('ground truth_20 MIMIC_20250714(Sheet2).csv', encoding=encoding)
                print(f"✅ 成功使用 {encoding} 编码加载CSV文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print("❌ 尝试所有编码都失败")
            return {}
        
        # 清理列名
        df.columns = df.columns.str.strip()
        print(f"📊 CSV文件包含 {len(df)} 行数据")
        print(f"📋 列名: {list(df.columns)}")
        
        ground_truth = {}
        for _, row in df.iterrows():
            case_no = row['Case No']
            note_id = row['Note ID']
            question_no = row['Question No.']
            question = row['Question']
            answer = row['Answer to each question']
            
            key = f"{note_id}"
            if key not in ground_truth:
                ground_truth[key] = {}
            
            ground_truth[key][question] = {
                'answer': answer,
                'question_no': question_no,
                'case_no': case_no
            }
        
        print(f"✅ 成功加载 {len(ground_truth)} 个案例的ground truth数据")
        return ground_truth
        
    except Exception as e:
        print(f"❌ 加载ground truth数据时出错: {e}")
        return {}

def compare_first_four_questions():
    """比较前四个问题的准确性"""
    print("🔍 开始比较前四个问题的准确性...")
    
    ground_truth = load_ground_truth()
    target_questions = ['BCLC staging', 'Child-Pugh score', 'MELD score', 'ALBI score']
    
    comparison_results = []
    
    # 获取excel_based_analysis_20cases文件夹中的HTML文件
    html_dir = 'excel_based_analysis_20cases'
    if not os.path.exists(html_dir):
        print(f"❌ 文件夹不存在: {html_dir}")
        return
    
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    for html_file in sorted(html_files):
        # 从文件名提取note_id
        note_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
        
        print(f"📄 处理案例: {note_id}")
        
        # 提取HTML中的答案
        html_path = os.path.join(html_dir, html_file)
        extracted_answers = extract_answers_from_html(html_path)
        
        if note_id in ground_truth:
            gt_data = ground_truth[note_id]
            
            for question in target_questions:
                if question in gt_data:
                    gt_answer = gt_data[question]['answer']
                    case_no = gt_data[question]['case_no']
                    question_no = gt_data[question]['question_no']
                    
                    # 在提取的答案中查找匹配的问题
                    extracted_answer = "未找到"
                    for q_text, q_data in extracted_answers.items():
                        if question.lower() in q_text.lower():
                            extracted_answer = q_data['answer']
                            break
                    
                    # 判断是否匹配
                    is_correct = False
                    if gt_answer == '-' and (extracted_answer == '未找到' or extracted_answer == '-' or extracted_answer == ''):
                        is_correct = True
                    elif gt_answer != '-' and extracted_answer != '未找到':
                        # 简单的字符串匹配
                        is_correct = str(gt_answer).strip().lower() == str(extracted_answer).strip().lower()
                    
                    comparison_results.append({
                        'Case No': case_no,
                        'Note ID': note_id,
                        'Question No': question_no,
                        'Question': question,
                        'Ground Truth': gt_answer,
                        'LLM Answer': extracted_answer,
                        'Correct': is_correct
                    })
    
    # 计算准确性统计
    df_comparison = pd.DataFrame(comparison_results)
    
    if len(df_comparison) == 0:
        print("\n❌ 没有数据可以比较，请检查ground truth文件或HTML文件")
        return pd.DataFrame()
    
    print("\n📊 前四个问题准确性统计:")
    print("=" * 50)
    
    for question in target_questions:
        question_data = df_comparison[df_comparison['Question'] == question]
        if len(question_data) > 0:
            accuracy = question_data['Correct'].sum() / len(question_data) * 100
            print(f"{question}: {accuracy:.1f}% ({question_data['Correct'].sum()}/{len(question_data)})")
    
    if len(df_comparison) > 0:
        overall_accuracy = df_comparison['Correct'].sum() / len(df_comparison) * 100
        print(f"\n整体准确性: {overall_accuracy:.1f}% ({df_comparison['Correct'].sum()}/{len(df_comparison)})")
    
        # 保存比较结果
        df_comparison.to_csv('first_four_questions_accuracy_comparison.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 比较结果已保存到: first_four_questions_accuracy_comparison.csv")
    
    return df_comparison

def extract_all_questions_to_excel():
    """提取所有案例的所有问题答案到Excel"""
    print("\n📝 开始提取所有问题答案到Excel...")
    
    all_results = []
    
    # 获取excel_based_analysis_20cases文件夹中的HTML文件
    html_dir = 'excel_based_analysis_20cases'
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    for html_file in sorted(html_files):
        note_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
        print(f"📄 提取案例: {note_id}")
        
        html_path = os.path.join(html_dir, html_file)
        extracted_answers = extract_answers_from_html(html_path)
        
        for question, data in extracted_answers.items():
            all_results.append({
                'Note ID': note_id,
                'Question': question,
                'Answer': data['answer'],
                'Supporting Sentences': ' | '.join(data['supporting_sentences']) if data['supporting_sentences'] else ''
            })
    
    # 创建DataFrame并保存到Excel
    df_all = pd.DataFrame(all_results)
    
    # 按Note ID和Question排序
    df_all = df_all.sort_values(['Note ID', 'Question'])
    
    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f'HCC_All_Questions_Answers_{timestamp}.xlsx'
    
    # 保存到Excel
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        df_all.to_excel(writer, sheet_name='所有问题答案', index=False)
        
        # 调整列宽
        worksheet = writer.sheets['所有问题答案']
        worksheet.column_dimensions['A'].width = 20  # Note ID
        worksheet.column_dimensions['B'].width = 50  # Question
        worksheet.column_dimensions['C'].width = 30  # Answer
        worksheet.column_dimensions['D'].width = 100 # Supporting Sentences
    
    print(f"💾 所有问题答案已保存到: {excel_filename}")
    print(f"📊 总共提取了 {len(df_all)} 个问题答案")
    
    return df_all

def main():
    """主函数"""
    print("🚀 开始处理HCC分析结果...")
    print("=" * 60)
    
    # 1. 比较前四个问题的准确性
    comparison_df = compare_first_four_questions()
    
    # 2. 提取所有问题答案到Excel
    all_questions_df = extract_all_questions_to_excel()
    
    print("\n✅ 所有任务完成!")
    print("=" * 60)
    
    # 显示结果摘要
    if len(comparison_df) > 0:
        print(f"\n📈 准确性比较结果:")
        print(f"  - 比较了 {len(comparison_df)} 个问题答案")
        print(f"  - 正确答案: {comparison_df['Correct'].sum()} 个")
        print(f"  - 整体准确率: {comparison_df['Correct'].sum() / len(comparison_df) * 100:.1f}%")
    
    if len(all_questions_df) > 0:
        print(f"\n📝 导出结果:")
        print(f"  - 导出了 {len(all_questions_df)} 个问题答案")
        print(f"  - 涉及 {all_questions_df['Note ID'].nunique()} 个案例")
    
    return comparison_df, all_questions_df

if __name__ == "__main__":
    main() 