import os
import openpyxl
from bs4 import BeautifulSoup

# 路径配置
dir_html = 'extracted_cases_20250717_html'
excel_path = 'selected_questions_summary.xlsx'

# 需要提取的所有问题（顺序和命名严格匹配）
TARGET_QUESTIONS = [
    'BCLC Staging',
    'Child-Pugh score',
    'MELD score',
    'ALBI score',
    '2.1 Number of confirmed HCC',
    '2.2 Largest size of HCC',
    '3. Child-Pugh classification',
    '4.1 ECOG performance status',
    '5.1 Grade of encephalopathy',
    '6.1 Grade of ascites',
    '7.1 Total bilirubin',
    '7.2 Albumin',
    '7.5 International normalized ratio',
    '7.6 Creatinine',
    '8.1 Presence of macrovascular invasion',
    '8.4 Presence of portal vein thrombosis',
    '9.1 Presence of metastatic lymph node',
    '9.2 Presence of extrahepatic spread',
    '9.3 Presence of metastatic HCC',
    '12. Liver transplant'
]

# 解析所有HTML文件，收集所有case答案
data = []
for fname in os.listdir(dir_html):
    if not fname.endswith('.html') or not fname.startswith('hcc_analysis_'):
        continue
    case_id = fname.replace('hcc_analysis_', '').replace('.html', '')
    with open(os.path.join(dir_html, fname), 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f, 'lxml')
        # 1. 先提取顶部四个主要分数
        for sec in soup.select('div.top-scores div.score-section'):
            q = sec.find('h4').get_text(strip=True)
            if q in TARGET_QUESTIONS:
                # 修正：直接查找包含Answer:的div
                answer = ''
                for d in sec.select('div.score-details div'):
                    txt = d.get_text(strip=True)
                    if txt.startswith('Answer:'):
                        answer = txt.replace('Answer:', '').strip()
                        break
                # 支持性说明
                reasoning = sec.select_one('div.reasoning')
                quotes_str = reasoning.get_text(strip=True) if reasoning else ''
                data.append([case_id, q, answer, quotes_str])
        # 2. 再提取所有问题项
        for qdiv in soup.select('div.question-item'):
            qtitle = qdiv.find('h4').get_text(strip=True)
            if qtitle in TARGET_QUESTIONS:
                ans = qdiv.select_one('span.value')
                answer = ans.get_text(strip=True) if ans else ''
                # 支持性语句
                quotes = []
                for q in qdiv.select('span.quotes-container span.quote-link'):
                    quotes.append(q.get_text(strip=True))
                quotes_str = '; '.join(quotes)
                data.append([case_id, qtitle, answer, quotes_str])

# 写入Excel（覆盖）
wb = openpyxl.Workbook()
ws = wb.active
ws.title = 'Sheet1'
ws.append(['Case ID', 'Question', 'Answer', 'Supporting Quotes'])
for row in data:
    ws.append(row)
wb.save(excel_path)
print(f'已覆盖写入: {excel_path}') 