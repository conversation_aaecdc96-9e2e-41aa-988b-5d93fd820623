#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理格式化后的医疗案例文件
使用 hcc_analyzer_simple.py 处理 formatted_cases_20250715 中的20个案例
输出到 html_reports_20250715_v2 文件夹
"""

import os
import subprocess
import time
from datetime import datetime

# 输入和输出目录
INPUT_DIR = 'formatted_cases_20250715'
OUTPUT_DIR = 'html_reports_20250715_v2'

def create_output_directory():
    """创建输出目录"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"✅ 已创建输出目录: {OUTPUT_DIR}")
    else:
        print(f"📁 输出目录已存在: {OUTPUT_DIR}")

def process_single_case(filename):
    """处理单个案例文件"""
    input_path = os.path.join(INPUT_DIR, filename)
    case_id = filename.replace('.txt', '')
    
    print(f"🔍 正在处理案例: {case_id}")
    
    try:
        # 调用 hcc_analyzer_simple.py 处理单个文件
        cmd = ['python3', 'hcc_analyzer_simple.py', '--file', input_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            # 查找生成的HTML文件
            expected_html = f"hcc_analysis_{case_id}.html"
            if os.path.exists(expected_html):
                # 移动到新的输出目录
                new_path = os.path.join(OUTPUT_DIR, expected_html)
                os.rename(expected_html, new_path)
                print(f"✅ 成功处理: {case_id} -> {new_path}")
                return True
            else:
                print(f"❌ 未找到生成的HTML文件: {expected_html}")
                return False
        else:
            print(f"❌ 处理失败: {case_id}")
            print(f"错误输出: {result.stderr[:200]}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ 处理超时: {case_id}")
        return False
    except Exception as e:
        print(f"❌ 处理异常: {case_id} - {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始批量处理格式化后的医疗案例")
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📂 输出目录: {OUTPUT_DIR}")
    
    # 创建输出目录
    create_output_directory()
    
    # 获取所有案例文件
    if not os.path.exists(INPUT_DIR):
        print(f"❌ 输入目录不存在: {INPUT_DIR}")
        return
    
    case_files = [f for f in os.listdir(INPUT_DIR) if f.endswith('.txt') and not f.startswith('formatting_report')]
    if not case_files:
        print(f"❌ 在 {INPUT_DIR} 中没有找到案例文件")
        return
    
    case_files.sort()  # 按文件名排序
    print(f"📄 找到 {len(case_files)} 个案例文件需要处理")
    
    # 处理每个案例
    processed_cases = []
    failed_cases = []
    
    for i, filename in enumerate(case_files, 1):
        print(f"\n📝 处理案例 {i}/{len(case_files)}: {filename}")
        
        if process_single_case(filename):
            processed_cases.append(filename)
        else:
            failed_cases.append(filename)
        
        # 添加延迟避免API限制
        if i < len(case_files):
            print("⏳ 等待1分钟避免API限制...")
            time.sleep(60)
    
    # 生成处理报告
    print(f"\n📊 批量处理完成!")
    print(f"✅ 成功: {len(processed_cases)} 个案例")
    print(f"❌ 失败: {len(failed_cases)} 个案例")
    
    # 创建处理报告
    report_content = f"""# 格式化案例批量分析报告

## 处理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 处理统计
- 总案例数: {len(case_files)}
- 成功处理: {len(processed_cases)}
- 处理失败: {len(failed_cases)}

## 成功处理的案例
"""
    
    for i, filename in enumerate(processed_cases, 1):
        case_id = filename.replace('.txt', '')
        report_content += f"{i}. {case_id}\n"
    
    if failed_cases:
        report_content += f"\n## 处理失败的案例\n"
        for i, filename in enumerate(failed_cases, 1):
            case_id = filename.replace('.txt', '')
            report_content += f"{i}. {case_id}\n"
    
    report_content += f"""
## 输出位置
- HTML报告保存在: {OUTPUT_DIR}/
- 格式化案例来源: {INPUT_DIR}/

## 说明
所有案例都使用格式化后的文本进行分析，以提高GPT-4.1的分析准确性。
使用 hcc_analyzer_simple.py 进行分析，包含完整的HCC评估和问题回答。
"""
    
    report_path = os.path.join(OUTPUT_DIR, 'batch_processing_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 处理报告已保存: {report_path}")
    
    if processed_cases:
        print(f"🎉 所有HTML报告已保存到 {OUTPUT_DIR}/ 目录中")
        print("📋 接下来可以进行准确性比较分析")

if __name__ == "__main__":
    main() 