#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML报告生成模块 - 创建专业的HCC病理分析报告
"""

import os
from datetime import datetime
from hcc_questions_mapping import HCCQuestionMapping

class HTMLReportGenerator:
    """HTML报告生成器"""

    def __init__(self):
        # 使用HCC问题映射
        hcc_mapping = HCCQuestionMapping()
        self.question_titles = hcc_mapping.question_titles
    
    def create_html_report(self, filename, original_content, analysis_result, age_info=None, validation_stats=None):
        """创建HTML报告"""
        
        tcga_id = os.path.splitext(filename)[0]
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCC Medical Report Analysis - {tcga_id}</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 HCC Medical Report Analysis</h1>
        <div class="header-info">
            <span class="tcga-id">Case ID: {tcga_id}</span>
            <span class="timestamp">Generated: {timestamp}</span>
        </div>
    </div>

    <div class="container">
        <div class="analysis-panel">
            <h2>📊 Expert Analysis Results</h2>
            {self._generate_analysis_content(analysis_result, age_info, validation_stats)}
        </div>

        <div class="report-panel">
            <h2>📋 Original Medical Report</h2>
            <div class="report-content" id="reportContent">
                {self._escape_html(original_content)}
            </div>
        </div>
    </div>

    <script>
        {self._get_javascript()}
    </script>
</body>
</html>"""
        
        return html_content
    
    def _get_css_styles(self):
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            font-size: 0.95em;
            color: #666;
        }

        .tcga-id {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
            min-height: calc(100vh - 120px);
        }

        .analysis-panel, .report-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            max-height: calc(100vh - 140px);
        }

        .analysis-panel h2, .report-panel h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            font-size: 1.4em;
        }

        .section {
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }

        .section h3 {
            color: #2980b9;
            margin-bottom: 15px;
            margin-left: -40px;
            font-size: 1.2em;
        }

        .subsection {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(52, 152, 219, 0.05);
            border: 1px solid rgba(52, 152, 219, 0.1);
        }

        .subsection h4 {
            color: #34495e;
            margin-bottom: 8px;
            font-size: 1em;
            font-weight: 600;
        }

        .answer {
            background: rgba(46, 204, 113, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 4px solid #2ecc71;
            font-weight: 500;
        }

        .quote {
            background: rgba(241, 196, 15, 0.1);
            padding: 8px;
            border-radius: 6px;
            font-style: italic;
            border-left: 4px solid #f1c40f;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quote:hover {
            background: rgba(241, 196, 15, 0.2);
            transform: translateX(5px);
        }

        .quote-icon {
            font-size: 1.2em;
        }

        .system-info {
            background: rgba(52, 152, 219, 0.1);
            padding: 8px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
            display: flex;
            align-items: center;
            gap: 8px;
            font-style: italic;
            color: #2980b9;
        }

        .system-info-icon {
            font-size: 1.2em;
        }

        .report-content {
            background: #fafafa;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.5;
            border: 1px solid #e0e0e0;
        }

        .highlight {
            background: linear-gradient(120deg, #ff9a56 0%, #ff6b6b 100%);
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .stats-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-info h4 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px;
            border-radius: 6px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .header-info {
                flex-direction: column;
                gap: 10px;
            }
        }
        """
    
    def _generate_analysis_content(self, analysis_result, age_info=None, validation_stats=None):
        """生成分析内容"""
        if not analysis_result:
            return "<p class='error'>❌ 分析结果为空</p>"
        
        content = ""
        
        # 验证统计信息
        if validation_stats:
            content += f"""
            <div class="stats-info">
                <h4>📈 Quote Validation Statistics</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value">{validation_stats['total']}</span>
                        <span class="stat-label">Total Quotes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{validation_stats['valid']}</span>
                        <span class="stat-label">Valid Quotes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{validation_stats['invalid']}</span>
                        <span class="stat-label">Invalid Quotes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{validation_stats['validity_rate']}%</span>
                        <span class="stat-label">Validity Rate</span>
                    </div>
                </div>
            </div>
            """
        
        # 按部分组织内容 - HCC结构
        sections = {
            "0": [],  # Top level scoring systems (BCLC, Child-Pugh, MELD, ALBI)
            "1": [],  # Patient characteristics
            "2": [],  # Tumor characteristics
            "3": [],  # Child-Pugh classification
            "4": [],  # Performance status
            "5": [],  # Encephalopathy
            "6": [],  # Ascites
            "7": [],  # Lab parameters
            "8": [],  # Vascular invasion
            "9": [],  # Extrahepatic spread
            "10": [], # Comorbidities
            "11": [], # Prior treatment received
            "12": [], # Liver transplant
            "13": []  # Pathology findings
        }
        
        for question_id, data in analysis_result.items():
            section_key = question_id.split('.')[0]
            if section_key in sections:
                sections[section_key].append((question_id, data))
        
        # 生成各部分内容
        for section_key, questions in sections.items():
            if not questions:
                continue
                
            section_title = self.question_titles.get(section_key, f"Section {section_key}")
            content += f'<div class="section">\n<h3>{section_key}. {section_title}</h3>\n'
            
            for question_id, data in questions:
                if question_id == section_key:  # 跳过主标题
                    continue
                    
                question_title = self.question_titles.get(question_id, f"Question {question_id}")
                
                if isinstance(data, dict):
                    answer = data.get('answer', 'N/A')
                    quote = data.get('supporting_quote', '')
                    
                    content += f"""
                    <div class="subsection">
                        <h4>{question_id} {question_title}</h4>
                        <div class="answer">{self._escape_html(str(answer))}</div>
                    """
                    
                    # 处理支持引用
                    if quote and quote.strip():
                        content += f"""
                        <div class="quote" onclick="highlightText('{self._escape_js(quote)}')">
                            <span class="quote-icon">📍</span>
                            <span>Supporting Quote: {self._escape_html(quote)}</span>
                        </div>
                        """
                    elif question_id == "1.1" and age_info:
                        # 年龄的系统信息
                        content += f"""
                        <div class="system-info">
                            <span class="system-info-icon">📊</span>
                            <span>System Info: Age data from clinical data table</span>
                        </div>
                        """
                    
                    content += "</div>\n"
                else:
                    content += f"""
                    <div class="subsection">
                        <h4>{question_id} {question_title}</h4>
                        <div class="answer">{self._escape_html(str(data))}</div>
                    </div>
                    """
            
            content += "</div>\n"
        
        return content
    
    def _get_javascript(self):
        """获取JavaScript代码"""
        return """
        function highlightText(text) {
            const reportContent = document.getElementById('reportContent');
            const originalText = reportContent.textContent;
            
            // 清除之前的高亮
            reportContent.innerHTML = reportContent.textContent;
            
            // 查找并高亮文本
            if (text && text.trim() !== '') {
                const regex = new RegExp(text.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');
                const highlightedText = reportContent.innerHTML.replace(regex, '<span class="highlight">$&</span>');
                reportContent.innerHTML = highlightedText;
                
                // 滚动到高亮位置
                const highlighted = reportContent.querySelector('.highlight');
                if (highlighted) {
                    highlighted.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'center' 
                    });
                }
            }
        }
        
        // 添加点击清除高亮功能
        document.getElementById('reportContent').addEventListener('click', function() {
            this.innerHTML = this.textContent;
        });
        """
    
    def _escape_html(self, text):
        """HTML转义"""
        if not isinstance(text, str):
            text = str(text)
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def _escape_js(self, text):
        """JavaScript转义"""
        if not isinstance(text, str):
            text = str(text)
        return (text.replace('\\', '\\\\')
                   .replace("'", "\\'")
                   .replace('"', '\\"')
                   .replace('\n', '\\n')
                   .replace('\r', '\\r'))
    
    def save_html_report(self, html_content, output_path):
        """保存HTML报告"""
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML报告已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存HTML报告失败: {e}")
            return False 