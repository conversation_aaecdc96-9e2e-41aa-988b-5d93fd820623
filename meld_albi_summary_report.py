#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MELD和ALBI分数差异总结报告
"""

import pandas as pd
import numpy as np

def generate_summary_report():
    """生成MELD和ALBI分数差异的总结报告"""
    
    print("📊 MELD和ALBI分数差异详细总结报告")
    print("=" * 80)
    
    # 读取详细分析结果
    try:
        df = pd.read_csv('meld_albi_detailed_analysis.csv', encoding='utf-8-sig')
    except:
        print("❌ 找不到详细分析文件，请先运行 detailed_meld_albi_analysis.py")
        return
    
    # 分别分析MELD和ALBI
    for score_type in ['MELD score', 'ALBI score']:
        score_name = score_type.split()[0]
        score_data = df[df['Score_Type'] == score_type]
        
        print(f"\n🎯 {score_name} Score 详细分析")
        print("=" * 60)
        
        # 统计各种情况
        categories = score_data['Category'].value_counts()
        
        print(f"📈 总体统计:")
        for category, count in categories.items():
            percentage = count / len(score_data) * 100
            print(f"   • {category}: {count}个 ({percentage:.1f}%)")
        
        print(f"\n🔍 具体案例分析:")
        
        # 1. GT缺失但LLM计算出数值的案例
        gt_missing = score_data[score_data['Category'] == 'GT缺失，LLM计算出数值']
        if len(gt_missing) > 0:
            print(f"\n1️⃣ GT缺失但LLM计算出数值 ({len(gt_missing)}个案例):")
            print("   这些案例说明LLM能够从临床数据中计算出GT中缺失的分数")
            for _, row in gt_missing.iterrows():
                print(f"   📄 {row['Note_ID']}: LLM计算值 = {row['LLM_Answer']}")
        
        # 2. 数值完全匹配的案例
        exact_match = score_data[score_data['Category'] == '数值匹配']
        if len(exact_match) > 0:
            print(f"\n2️⃣ 数值完全匹配 ({len(exact_match)}个案例):")
            print("   这些案例表明LLM计算准确")
            for _, row in exact_match.iterrows():
                print(f"   ✅ {row['Note_ID']}: GT = LLM = {row['GT_Answer']}")
        
        # 3. 数值不匹配的案例 - 详细分析差异
        value_differ = score_data[score_data['Category'] == '数值不匹配']
        if len(value_differ) > 0:
            print(f"\n3️⃣ 数值不匹配 ({len(value_differ)}个案例):")
            print("   需要仔细分析差异原因")
            
            # 计算差异统计
            differences = value_differ['Numeric_Difference'].dropna()
            if len(differences) > 0:
                print(f"   📊 差异统计:")
                print(f"      - 平均差异: {differences.mean():.3f}")
                print(f"      - 最大差异: {differences.max():.3f}")
                print(f"      - 最小差异: {differences.min():.3f}")
                
                # 按差异大小分类
                small_diff = differences[differences <= 0.1]
                medium_diff = differences[(differences > 0.1) & (differences <= 0.5)]
                large_diff = differences[differences > 0.5]
                
                print(f"   🔍 差异分类:")
                print(f"      - 小差异 (≤0.1): {len(small_diff)}个")
                print(f"      - 中等差异 (0.1-0.5): {len(medium_diff)}个") 
                print(f"      - 大差异 (>0.5): {len(large_diff)}个")
            
            print(f"   📋 具体差异:")
            for _, row in value_differ.iterrows():
                diff = row.get('Numeric_Difference', 0)
                print(f"   ❌ {row['Note_ID']}: GT={row['GT_Answer']} vs LLM={row['LLM_Answer']} (差异:{diff:.3f})")
        
        # 4. GT有值但LLM未找到
        gt_has_llm_missing = score_data[score_data['Category'] == 'GT有数值，LLM未找到']
        if len(gt_has_llm_missing) > 0:
            print(f"\n4️⃣ GT有数值但LLM未找到 ({len(gt_has_llm_missing)}个案例):")
            print("   这些案例可能是LLM提取失败或计算逻辑问题")
            for _, row in gt_has_llm_missing.iterrows():
                print(f"   ⚠️  {row['Note_ID']}: GT={row['GT_Answer']} → LLM=未找到")
        
        # 计算实际准确率
        has_gt_value = score_data[~score_data['GT_Missing']]
        if len(has_gt_value) > 0:
            correct_when_gt_exists = len(has_gt_value[has_gt_value['Category'] == '数值匹配'])
            accuracy_when_gt_exists = correct_when_gt_exists / len(has_gt_value) * 100
            
            print(f"\n📊 关键指标:")
            print(f"   • 当GT有数值时的准确率: {accuracy_when_gt_exists:.1f}% ({correct_when_gt_exists}/{len(has_gt_value)})")
            
            # 如果考虑小差异也算正确
            small_error_threshold = 0.1 if score_name == 'ALBI' else 1
            acceptable_cases = has_gt_value[
                (has_gt_value['Category'] == '数值匹配') | 
                ((has_gt_value['Category'] == '数值不匹配') & 
                 (has_gt_value['Numeric_Difference'] <= small_error_threshold))
            ]
            if len(acceptable_cases) > 0:
                acceptable_accuracy = len(acceptable_cases) / len(has_gt_value) * 100
                threshold_desc = f"±{small_error_threshold}"
                print(f"   • 允许{threshold_desc}误差的准确率: {acceptable_accuracy:.1f}% ({len(acceptable_cases)}/{len(has_gt_value)})")
    
    # 整体结论
    print(f"\n" + "="*80)
    print("🎯 整体结论与建议")
    print("="*80)
    
    print("\n✅ LLM的优势:")
    print("   1. 能够计算出GT中缺失的MELD和ALBI分数")
    print("   2. 当GT有数值时，大部分计算是准确的")
    print("   3. 即使有差异，多数也在可接受范围内")
    
    print("\n⚠️ 需要改进的地方:")
    print("   1. ALBI分数的计算精度需要提高")
    print("   2. 某些案例中LLM未能提取到足够信息进行计算")
    print("   3. 部分数值计算可能使用了不同的公式或参数")
    
    print("\n💡 建议:")
    print("   1. 对于GT缺失的情况，LLM计算的数值实际上是有价值的补充")
    print("   2. 可以考虑将小差异（如MELD±1, ALBI±0.1）视为可接受范围")
    print("   3. 需要验证LLM使用的计算公式与GT标准是否一致")
    
    # 生成简化的汇总表
    print(f"\n" + "="*80)
    print("📋 简化汇总表")
    print("="*80)
    
    summary_table = []
    for score_type in ['MELD score', 'ALBI score']:
        score_name = score_type.split()[0]
        score_data = df[df['Score_Type'] == score_type]
        
        gt_missing = len(score_data[score_data['Category'] == 'GT缺失，LLM计算出数值'])
        exact_match = len(score_data[score_data['Category'] == '数值匹配'])
        value_differ = len(score_data[score_data['Category'] == '数值不匹配'])
        llm_missing = len(score_data[score_data['Category'] == 'GT有数值，LLM未找到'])
        
        has_gt = exact_match + value_differ + llm_missing
        accuracy = (exact_match / has_gt * 100) if has_gt > 0 else 0
        
        summary_table.append({
            'Score': score_name,
            'GT缺失_LLM计算': gt_missing,
            '完全匹配': exact_match,
            '数值差异': value_differ,
            'LLM缺失': llm_missing,
            '总计': len(score_data),
            '准确率': f"{accuracy:.1f}%"
        })
    
    summary_df = pd.DataFrame(summary_table)
    print(summary_df.to_string(index=False))
    
    # 保存汇总报告
    summary_df.to_csv('meld_albi_summary_table.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 汇总表已保存到: meld_albi_summary_table.csv")

if __name__ == "__main__":
    generate_summary_report() 