#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HCC (Hepatocellular Carcinoma) Questions Mapping
Based on HCC cross-referencing platform structured questions Excel file
"""

class HCCQuestionMapping:
    """HCC问题映射类"""
    
    def __init__(self):
        # 基于Excel文件的HCC问题结构
        self.question_titles = {
            # Top level scoring systems (display answer only)
            "0.1": "BCLC Staging",
            "0.2": "Child-Pugh score", 
            "0.3": "MELD score",
            "0.4": "ALBI score",
            
            # Main analysis sections (display answer/confidence/supporting quotes)
            "1": "Patient characteristics",
            "1.1": "Age",
            "1.2": "Gender",
            
            "2": "Tumor characteristics", 
            "2.1": "Number of confirmed HCC",
            "2.2": "Largest size of HCC",
            
            "3": "Child-Pugh classification",
            
            "4": "Performance status",
            "4.1": "ECOG performance status",
            "4.2": "Extent of physical activity restriction", 
            "4.3": "Activity status",
            "4.4": "Deceased or expired",
            
            "5": "Encephalopathy",
            "5.1": "Grade of encephalopathy",
            "5.2": "Diagnosis of encephalopathy",
            "5.3": "Mental status",
            "5.4": "Level of consciousness",
            
            "6": "Ascites",
            "6.1": "Grade of ascites",
            "6.2": "Diagnosis of ascites", 
            "6.3": "Discharge for ascites",
            
            "7": "Lab parameters",
            "7.1": "Total bilirubin",
            "7.2": "Albumin",
            "7.3": "Sodium",
            "7.4": "Prothrombin time",
            "7.5": "International normalized ratio",
            "7.6": "Creatinine",
            "7.7": "Alpha-fetoprotein level",
            "7.8": "Indocyanine green retention at 15 minutes",
            
            "8": "Vascular invasion",
            "8.1": "Presence of macrovascular invasion",
            "8.2": "Presence of lymphovascular invasion",
            "8.3": "Extent of invasion",
            "8.4": "Presence of portal vein thrombosis",
            
            "9": "Extrahepatic spread",
            "9.1": "Presence of metastatic lymph node",
            "9.2": "Presence of extrahepatic spread",
            "9.3": "Presence of metastatic HCC",
            "9.4": "Site of distant metastasis",
            
            "10": "Comorbidities",
            "10.1": "Presence of cirrhosis",
            "10.2": "Severe comorbidities",
            
            "11": "Prior treatment received",
            
            "12": "Liver transplant",
            
            "13": "Pathology findings",
            "13.1": "Histological tumour grade",
            "13.2": "Histological tumour type", 
            "13.3": "Margin status",
            "13.4": "Presence of satellitosis"
        }
        
        # 详细问题定义
        self.question_details = {
            "0.1": {
                "question": "see BCLC staging rules",
                "supplement": "",
                "display_type": "answer_only"
            },
            "0.2": {
                "question": "same as 3",
                "supplement": "",
                "display_type": "answer_only"
            },
            "0.3": {
                "question": "MELD = 9.57 * ln(creatinine) + 3.78 * ln(bilirubin) + 11.2 *ln(INR) + 6.43, where creatinine is in mg/dL, and bilirubin is in mg/dL",
                "supplement": "",
                "display_type": "answer_only"
            },
            "0.4": {
                "question": "ALBI = (log10 bilirubin * 0.66) + (albumin * -0.085), where bilirubin is in μmol/L, and albumin in g/L",
                "supplement": "albumin-bilirubin score",
                "display_type": "answer_only"
            },
            "1.1": {
                "question": "Age of patient: Return the result without unit.",
                "supplement": "",
                "display_type": "full"
            },
            "1.2": {
                "question": "Gender of patient.",
                "supplement": "",
                "display_type": "full"
            },
            "2.1": {
                "question": "Number of confirmed HCC: This refers to the latest number of existing HCC of patient only. If there were multiple imaging scans on liver, only refer to the latest one and do not consider the previous scan results. Count the number of lesion including adenomas. Do not count the unconfirmed HCCs indicating as dysplastic nodules, satellite nodules and nodules with description of \"fat containing lesions concerning for HCC\". Return an actual integer whenever possible. \"ill-defined lesion\" and \"stable liver lesion\" mean there was 1 HCC. The note may only mention 'multiple' or 'multifocal' HCC such as \"multiple arterially enhancing lesions\", such that the number of HCC could not be counted, return 'multiple' in that case. If there was no mention of HCC, return \"\".",
                "supplement": "",
                "display_type": "full"
            },
            "2.2": {
                "question": "Largest size of HCC: This refers to the largest size of HCC of patient. If there are multiple imaging scans on liver, only refer to the latest one and do not consider the previous scan results. Return the exact result with unit whenever. If the exact size was not mentioned but there was phrase related to size such as 'very large', return that phrase. If there is no mention of HCC size in the latest imaging scan, return \"\" and do not return results from previous scans. Do not return the size of other tumor such as chest mass.",
                "supplement": "",
                "display_type": "full"
            },
            "3": {
                "question": "Child-Pugh classification.",
                "supplement": "let GPT 4.1 generate the answer first. If information is missing, extract Child-Pugh score from report",
                "display_type": "full"
            },
            "4.1": {
                "question": "ECOG performance status: Return if the ECOG PS was explicitly mentioned.",
                "supplement": "let GPT 4.1 generate the answer first, need to consider the presence of severe comorbidities",
                "display_type": "full"
            },
            "4.2": {
                "question": "Extent of physical activity restriction.",
                "supplement": "",
                "display_type": "full"
            },
            "4.3": {
                "question": "Activity status.",
                "supplement": "",
                "display_type": "full"
            },
            "4.4": {
                "question": "Deceased or expired.",
                "supplement": "",
                "display_type": "full"
            },
            "5.1": {
                "question": "Grade of encephalopathy: return none, grade 1, grade 2, grade 3, or grade 4.",
                "supplement": "let GPT 4.1 generate the answer first, refer to Child-Pugh Score rule",
                "display_type": "full"
            },
            "5.2": {
                "question": "Prior diagnosis of encephalopathy.",
                "supplement": "",
                "display_type": "full"
            },
            "5.3": {
                "question": "Mental status.",
                "supplement": "",
                "display_type": "full"
            },
            "5.4": {
                "question": "Level of consciousness.",
                "supplement": "",
                "display_type": "full"
            },
            "6.1": {
                "question": "what is the grade of the ascites: return absent, slight of moderate",
                "supplement": "let GPT 4.1 generate the answer first, refer to Child-Pugh Score rule",
                "display_type": "full"
            },
            "6.2": {
                "question": "Diagnosis of ascites: Return the description from the note, and also the degree of ascites such as \"mod-large\" if mentioned.",
                "supplement": "",
                "display_type": "full"
            },
            "6.3": {
                "question": "Discharge for ascites.",
                "supplement": "",
                "display_type": "full"
            },
            "7.1": {
                "question": "Total bilirubin: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.2": {
                "question": "Albumin: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.3": {
                "question": "Sodium: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.4": {
                "question": "Prothrombin time: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.5": {
                "question": "International normalized ratio: Return the first result at admission without unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.6": {
                "question": "Creatinine: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.7": {
                "question": "Alpha-fetoprotein level: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "7.8": {
                "question": "Indocyanine green retention at 15 minutes: Return the first result at admission with unit.",
                "supplement": "",
                "display_type": "full"
            },
            "8.1": {
                "question": "Presence of macrovascular invasion: Return the description from the note.",
                "supplement": "",
                "display_type": "full"
            },
            "8.2": {
                "question": "Presence of lymphovascular invasion.",
                "supplement": "",
                "display_type": "full"
            },
            "8.3": {
                "question": "Extent of invasion.",
                "supplement": "",
                "display_type": "full"
            },
            "8.4": {
                "question": "Presence of portal vein thrombosis.",
                "supplement": "if yes, also indicate macrovascular invasion",
                "display_type": "full"
            },
            "9.1": {
                "question": "Presence of metastatic HCC.",
                "supplement": "if any of the items is yes, it means the patient has extrahepatic spread",
                "display_type": "full"
            },
            "9.2": {
                "question": "Presence of extrahepatic spread.",
                "supplement": "",
                "display_type": "full"
            },
            "9.3": {
                "question": "Presence of metastatic HCC.",
                "supplement": "",
                "display_type": "full"
            },
            "9.4": {
                "question": "Site of distant metastasis.",
                "supplement": "",
                "display_type": "full"
            },
            "10.1": {
                "question": "Presence of cirrhosis.",
                "supplement": "",
                "display_type": "full"
            },
            "10.2": {
                "question": "Does the patient have any of the comorbidities: chronic heart failure, atrial fibrillation, coronary artery disease, stroke, chronic kidney disease, end-stage renal failure, Kidney dialysis, ruptured brain aneurysm, brain shrinkage, uncontrolled systemic infections, poorly controlled diabetes, hepatic hydrothorax, hepatorenal syndrome",
                "supplement": "the status of severe comorbidities will affect ECOG score",
                "display_type": "full"
            },
            "11": {
                "question": "Prior treatment received: Prior treatment includes medications and HCC resection.",
                "supplement": "",
                "display_type": "full"
            },
            "12": {
                "question": "Presence of liver transplant.",
                "supplement": "",
                "display_type": "full"
            },
            "13.1": {
                "question": "Histological tumour grade.",
                "supplement": "",
                "display_type": "full"
            },
            "13.2": {
                "question": "Histological tumour type.",
                "supplement": "",
                "display_type": "full"
            },
            "13.3": {
                "question": "Margin status.",
                "supplement": "",
                "display_type": "full"
            },
            "13.4": {
                "question": "Presence of satellitosis.",
                "supplement": "",
                "display_type": "full"
            }
        }

    def get_structured_questions(self):
        """获取结构化分析问题"""
        questions_text = ""

        # Top level scoring systems (first 4 questions - leave space for user to add content)
        questions_text += "**Top Level Scoring Systems (Display answer ONLY):**\n"
        questions_text += "**0.1 BCLC Staging**: see BCLC staging rules\n"
        questions_text += "**0.2 Child-Pugh score**: same as 3\n"
        questions_text += "**0.3 MELD score**: MELD = 9.57 * ln(creatinine) + 3.78 * ln(bilirubin) + 11.2 *ln(INR) + 6.43, where creatinine is in mg/dL, and bilirubin is in mg/dL\n"
        questions_text += "**0.4 ALBI score**: ALBI = (log10 bilirubin * 0.66) + (albumin * -0.085), where bilirubin is in μmol/L, and albumin in g/L (albumin-bilirubin score)\n\n"

        # Main analysis sections
        questions_text += "**Main Analysis (Display answer/confidence score/supporting quotes):**\n\n"

        for question_id in sorted(self.question_details.keys()):
            if question_id.startswith("0."):  # Skip top level scoring systems
                continue

            detail = self.question_details[question_id]
            title = self.question_titles.get(question_id, f"Question {question_id}")

            questions_text += f"**{question_id} {title}**: {detail['question']}\n"
            if detail['supplement']:
                questions_text += f"   Supplement: {detail['supplement']}\n"
            questions_text += "\n"

        return questions_text
