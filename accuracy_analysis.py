#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HCC分析结果准确率计算脚本
对比生成的HTML报告与标答文件，计算前四个关键问题的准确率
"""

import os
import re
from bs4 import BeautifulSoup
import pandas as pd

# 标准答案数据（从用户提供的文件中提取）
GROUND_TRUTH = {
    '10056223-DS-5': {'BCLC': 'A', 'Child-Pugh': 'B', 'MELD': '13', 'ALBI': '-1.37'},
    '10151324-DS-16': {'BCLC': 'A', 'Child-Pugh': 'A', 'MELD': '-', 'ALBI': '-'},
    '10151324-DS-18': {'BCLC': 'A', 'Child-Pugh': 'A', 'MELD': '9', 'ALBI': '-2.79'},
    '10225793-DS-33': {'BCLC': '0', 'Child-Pugh': 'B', 'MELD': '15', 'ALBI': '-'},
    '10225793-DS-34': {'BCLC': '0', 'Child-Pugh': 'C', 'MELD': '14', 'ALBI': '-1.66'},
    '10225793-DS-36': {'BCLC': '0', 'Child-Pugh': 'C', 'MELD': '15', 'ALBI': '-1.22'},
    '10262565-DS-19': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-1.48'},
    '10262565-DS-20': {'BCLC': 'D', 'Child-Pugh': 'B/C', 'MELD': '-', 'ALBI': '-'},
    '10388675-DS-19': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-'},
    '10388675-DS-20': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-1.75'},
    '10666715-DS-8': {'BCLC': 'B', 'Child-Pugh': 'A', 'MELD': '-', 'ALBI': '-1.83'},
    '10747475-DS-4': {'BCLC': 'A', 'Child-Pugh': 'B', 'MELD': '13', 'ALBI': '-1.43'},
    '10760122-DS-21': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-'},
    '10880579-DS-11': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '19', 'ALBI': '-1.02'},
    '10902714-DS-17': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-2.67'},
    '10923555-DS-9': {'BCLC': 'A', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-1.3'},
    '10960817-DS-14': {'BCLC': 'D', 'Child-Pugh': 'B', 'MELD': '22', 'ALBI': '-2.35'},
    '11102747-DS-2': {'BCLC': 'B', 'Child-Pugh': 'A', 'MELD': '12', 'ALBI': '-1.91'},
    '11198012-DS-6': {'BCLC': 'C', 'Child-Pugh': 'A', 'MELD': '11', 'ALBI': '-2.32'},
    '11198012-DS-7': {'BCLC': 'C', 'Child-Pugh': 'B', 'MELD': '-', 'ALBI': '-1.75'},
}

def extract_scores_from_html(html_file):
    """从HTML文件中提取评分结果"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        scores = {}
        
        # 查找所有score-section
        score_sections = soup.find_all('div', class_='score-section')
        
        for section in score_sections:
            h4 = section.find('h4')
            if h4:
                title = h4.text.strip()
                
                # 查找包含"Answer:"的div，注意Answer可能在strong标签内
                answer_div = None
                for div in section.find_all('div'):
                    if div.text and 'Answer:' in div.text:
                        answer_div = div
                        break
                
                if answer_div:
                    answer_text = answer_div.text.strip()
                    # 提取Answer:后面的内容
                    if 'Answer:' in answer_text:
                        answer = answer_text.split('Answer:')[1].strip()
                        
                        if 'BCLC' in title:
                            scores['BCLC'] = answer
                        elif 'Child-Pugh' in title:
                            # 提取Class后面的内容
                            if 'Class' in answer:
                                scores['Child-Pugh'] = answer.split('Class')[1].strip()
                            else:
                                scores['Child-Pugh'] = answer
                        elif 'MELD' in title:
                            scores['MELD'] = answer
                        elif 'ALBI' in title:
                            scores['ALBI'] = answer
        
        return scores
        
    except Exception as e:
        print(f"读取文件 {html_file} 时出错: {e}")
        return {}

def normalize_answer(answer):
    """标准化答案格式"""
    if not answer:
        return '-'
    
    answer = str(answer).strip()
    
    # 处理Class X格式
    if answer.startswith('Class '):
        answer = answer[6:].strip()
    
    # 处理一些常见格式
    answer = answer.replace('Stage ', '').replace('stage ', '')
    
    # 处理复杂的文本，取第一个有意义的词
    # 先去掉换行符
    answer = answer.replace('\n', ' ')
    
    # 分割并取第一个词
    parts = answer.split()
    if parts:
        first_part = parts[0]
        # 去掉括号内容
        first_part = first_part.split('(')[0]
        # 去掉常见的后缀
        first_part = first_part.rstrip('.,;:')
        
        # 检查是否是有效的答案格式
        if first_part in ['0', 'A', 'B', 'C', 'D', '-'] or first_part.startswith('-') or first_part.replace('.', '').replace('-', '').isdigit():
            answer = first_part
        else:
            # 如果第一个词不是有效答案，可能需要更智能的解析
            for part in parts:
                part = part.split('(')[0].rstrip('.,;:')
                if part in ['0', 'A', 'B', 'C', 'D', '-'] or part.startswith('-') or part.replace('.', '').replace('-', '').isdigit():
                    answer = part
                    break
    
    # 标准化一些值
    if answer.lower() in ['not available', 'not mentioned', 'not', 'available']:
        return '-'
    
    return answer

def calculate_accuracy():
    """计算准确率"""
    print("🔍 开始计算HCC分析结果准确率...")
    print("=" * 60)
    
    total_questions = 0
    correct_answers = 0
    detailed_results = []
    
    # 统计各指标的准确率
    metrics_stats = {
        'BCLC': {'correct': 0, 'total': 0},
        'Child-Pugh': {'correct': 0, 'total': 0},
        'MELD': {'correct': 0, 'total': 0},
        'ALBI': {'correct': 0, 'total': 0}
    }
    
    # 遍历每个case
    for case_id, ground_truth in GROUND_TRUTH.items():
        html_file = f"hcc_analysis_{case_id}.html"
        
        if not os.path.exists(html_file):
            print(f"⚠️  文件不存在: {html_file}")
            continue
        
        print(f"\n📄 分析 {case_id}:")
    
        # 提取预测结果
        predicted = extract_scores_from_html(html_file)
        
        case_results = {
            'case_id': case_id,
            'BCLC': {'pred': '', 'truth': '', 'correct': False},
            'Child-Pugh': {'pred': '', 'truth': '', 'correct': False},
            'MELD': {'pred': '', 'truth': '', 'correct': False},
            'ALBI': {'pred': '', 'truth': '', 'correct': False}
        }
        
        # 对比每个指标
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            pred_raw = predicted.get(metric, '-')
            truth_raw = ground_truth.get(metric, '-')
            
            pred_norm = normalize_answer(pred_raw)
            truth_norm = normalize_answer(truth_raw)
            
            case_results[metric]['pred'] = pred_norm
            case_results[metric]['truth'] = truth_norm
            
            # 判断是否正确
            is_correct = pred_norm == truth_norm
            case_results[metric]['correct'] = is_correct
            
            # 统计
            total_questions += 1
            metrics_stats[metric]['total'] += 1
            
                    if is_correct:
                correct_answers += 1
                metrics_stats[metric]['correct'] += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"  {metric:12}: {pred_norm:8} vs {truth_norm:8} {status}")
        
        detailed_results.append(case_results)
    
    # 计算总体准确率
    overall_accuracy = (correct_answers / total_questions * 100) if total_questions > 0 else 0
    
    print("\n" + "=" * 60)
    print("📊 准确率统计结果:")
    print("=" * 60)
    
    # 各指标准确率
    for metric, stats in metrics_stats.items():
        if stats['total'] > 0:
            accuracy = stats['correct'] / stats['total'] * 100
            print(f"{metric:12}: {stats['correct']:2d}/{stats['total']:2d} = {accuracy:5.1f}%")
    
    print(f"\n{'总体准确率':12}: {correct_answers:2d}/{total_questions:2d} = {overall_accuracy:5.1f}%")
    
    # 错误分析
    print("\n" + "=" * 60)
    print("❌ 错误分析:")
    print("=" * 60)
    
    for result in detailed_results:
        case_id = result['case_id']
        errors = []
        
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            if not result[metric]['correct']:
                pred = result[metric]['pred']
                truth = result[metric]['truth']
                errors.append(f"{metric}: {pred}→{truth}")
        
        if errors:
            print(f"{case_id}: {', '.join(errors)}")
    
    # 生成详细报告
    generate_detailed_report(detailed_results, metrics_stats, overall_accuracy)
    
    return overall_accuracy

def generate_detailed_report(detailed_results, metrics_stats, overall_accuracy):
    """生成详细的准确率报告"""
    try:
        report_content = f"""
# HCC分析结果准确率报告

## 总体统计
- **总体准确率**: {overall_accuracy:.1f}%
- **总问题数**: {sum(stats['total'] for stats in metrics_stats.values())}
- **正确答案数**: {sum(stats['correct'] for stats in metrics_stats.values())}

## 各指标准确率
"""
        
        for metric, stats in metrics_stats.items():
            if stats['total'] > 0:
                accuracy = stats['correct'] / stats['total'] * 100
                report_content += f"- **{metric}**: {stats['correct']}/{stats['total']} = {accuracy:.1f}%\n"
        
        report_content += "\n## 详细结果\n\n"
        report_content += "| Case ID | BCLC | Child-Pugh | MELD | ALBI |\n"
        report_content += "|---------|------|------------|------|------|\n"
        
        for result in detailed_results:
            case_id = result['case_id']
            row = f"| {case_id} |"
            
            for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
                pred = result[metric]['pred']
                truth = result[metric]['truth']
                status = "✅" if result[metric]['correct'] else "❌"
                row += f" {pred}({truth}){status} |"
            
            report_content += row + "\n"
        
        # 保存报告
        with open('accuracy_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📝 详细报告已保存到: accuracy_report.md")
        
    except Exception as e:
        print(f"⚠️ 生成详细报告时出错: {e}")

if __name__ == "__main__":
    accuracy = calculate_accuracy()
    print(f"\n🎯 最终准确率: {accuracy:.1f}%") 