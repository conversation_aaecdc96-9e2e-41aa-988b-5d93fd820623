#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较前四个主要问题的准确性分析
对比LLM结果与标准答案(Ground Truth)

前四个主要问题:
1. BCLC staging (0.1)
2. Child-Pugh score (0.2) 
3. MELD score (0.3)
4. ALBI score (0.4)
"""

import os
import pandas as pd
import re
from bs4 import BeautifulSoup
from pathlib import Path
import csv

def load_ground_truth(csv_file):
    """从CSV文件加载标准答案"""
    print(f"📊 加载标准答案文件: {csv_file}")
    
    # 尝试不同编码读取CSV文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252', 'latin1']
    df = None
    
    for encoding in encodings:
        try:
            df = pd.read_csv(csv_file, encoding=encoding)
            print(f"✅ 成功使用编码: {encoding}")
            break
        except UnicodeDecodeError:
            continue
    
    if df is None:
        raise ValueError(f"无法使用任何编码读取文件: {csv_file}")
    
    # 创建标准答案字典
    ground_truth = {}
    
    for i, row in df.iterrows():
        case_no = row.iloc[0]  # Case No
        note_id = row.iloc[1]  # Note ID
        question_no = row.iloc[2]  # Question No.
        question = row.iloc[4] if len(row) > 4 else ""  # Question
        answer = row.iloc[9] if len(row) > 9 else ""  # Answer to each question
        
        # 只处理前四个主要问题 (检查小数格式)
        # 将question_no转换为浮点数进行比较
        try:
            q_num = float(question_no)
            if q_num in [1.0, 2.0, 3.0, 4.0]:  # 对应0.1, 0.2, 0.3, 0.4但在CSV中可能显示为1,2,3,4
                if note_id not in ground_truth:
                    ground_truth[note_id] = {}
                
                # 标准化问题类型
                if q_num == 1.0:  # BCLC staging
                    ground_truth[note_id]['bclc'] = str(answer).strip()
                elif q_num == 2.0:  # Child-Pugh score
                    ground_truth[note_id]['child_pugh'] = str(answer).strip()
                elif q_num == 3.0:  # MELD score
                    ground_truth[note_id]['meld'] = str(answer).strip()
                elif q_num == 4.0:  # ALBI score
                    ground_truth[note_id]['albi'] = str(answer).strip()
        except (ValueError, TypeError):
            # 如果无法转换为数字，跳过
            continue
    
    print(f"✅ 加载了 {len(ground_truth)} 个案例的标准答案")
    return ground_truth

def extract_llm_answers_from_html(html_file):
    """从HTML文件提取LLM的答案"""
    if not os.path.exists(html_file):
        return None
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找top-scores部分
        answers = {}
        score_sections = soup.find_all('div', class_='score-section')
        
        for section in score_sections:
            h4 = section.find('h4')
            if not h4:
                continue
                
            title = h4.text.strip()
            answer_div = section.find('div', class_='score-details')
            
            if answer_div:
                # 查找Answer部分
                answer_text = ""
                for div in answer_div.find_all('div'):
                    if div.find('strong') and 'Answer:' in div.text:
                        answer_text = div.text.replace('Answer:', '').strip()
                        break
                
                # 根据标题分类答案
                if 'BCLC' in title:
                    answers['bclc'] = answer_text
                elif 'Child-Pugh' in title:
                    answers['child_pugh'] = answer_text
                elif 'MELD' in title:
                    answers['meld'] = answer_text
                elif 'ALBI' in title:
                    answers['albi'] = answer_text
        
        return answers
        
    except Exception as e:
        print(f"⚠️ 解析HTML文件失败 {html_file}: {e}")
        return None

def normalize_answer(answer, question_type):
    """标准化答案格式便于比较"""
    if pd.isna(answer) or str(answer).lower() in ['nan', 'none', 'not available', '-', '']:
        return 'Not available'
    
    answer = str(answer).strip()
    
    if question_type == 'bclc':
        # BCLC staging: 提取阶段
        # 可能的格式: "A", "Stage A", "BCLC A", etc.
        match = re.search(r'(Stage\s*)?([0ABCD])', answer, re.IGNORECASE)
        if match:
            return match.group(2).upper()
        return answer
        
    elif question_type == 'child_pugh':
        # Child-Pugh: 提取类别
        # 可能的格式: "B", "Class B", "Child-Pugh B", etc.
        match = re.search(r'(Class\s*)?([ABC])', answer, re.IGNORECASE)
        if match:
            return match.group(2).upper()
        return answer
        
    elif question_type in ['meld', 'albi']:
        # MELD/ALBI: 提取数值
        # 处理数值或"-"
        if answer in ['-', 'Not available', 'N/A']:
            return '-'
        
        # 提取数值
        match = re.search(r'([\d\.]+)', answer)
        if match:
            try:
                value = float(match.group(1))
                if question_type == 'meld':
                    return str(int(round(value)))  # MELD取整数
                else:
                    return f"{value:.2f}"  # ALBI保留2位小数
            except:
                pass
        return answer
    
    return answer

def compare_answers(ground_truth, llm_answer, question_type):
    """比较答案准确性"""
    gt_norm = normalize_answer(ground_truth, question_type)
    llm_norm = normalize_answer(llm_answer, question_type)
    
    # 完全匹配
    if gt_norm == llm_norm:
        return 'Correct', 1.0
    
    # 特殊情况：都是"不可用"
    if gt_norm in ['Not available', '-'] and llm_norm in ['Not available', '-']:
        return 'Correct (Both N/A)', 1.0
    
    # 数值容差比较 (仅适用于MELD和ALBI)
    if question_type in ['meld', 'albi']:
        try:
            if gt_norm != '-' and llm_norm != '-':
                gt_val = float(gt_norm)
                llm_val = float(llm_norm)
                
                if question_type == 'meld':
                    # MELD score容差±1
                    if abs(gt_val - llm_val) <= 1:
                        return 'Close (±1)', 0.8
                elif question_type == 'albi':
                    # ALBI score容差±0.2
                    if abs(gt_val - llm_val) <= 0.2:
                        return 'Close (±0.2)', 0.8
        except:
            pass
    
    return 'Incorrect', 0.0

def main():
    """主函数"""
    print("🎯 开始比较前四个主要问题的准确性")
    print("=" * 80)
    
    # 指定要分析的20个案例
    target_cases = [
        "10056223-DS-5", "10151324-DS-18", "10225793-DS-34", "10747475-DS-4",
        "10880579-DS-11", "10960817-DS-14", "11102747-DS-2", "11198012-DS-6",
        "11265636-DS-13", "11327487-DS-19", "11329198-DS-5", "11349875-DS-8",
        "11417994-DS-9", "11419849-DS-24", "11455644-DS-15", "11714491-DS-4",
        "11914986-DS-12", "11960904-DS-5", "12032388-DS-16", "12344358-DS-12"
    ]
    
    print(f"📋 指定分析案例数: {len(target_cases)}")
    print(f"📋 案例列表: {', '.join(target_cases[:5])}...")
    
    # 文件路径
    csv_file = "ground truth_20 MIMIC_20250714(Sheet2).csv"
    html_dir = "html_reports_20250715"
    
    # 检查文件存在性
    if not os.path.exists(csv_file):
        print(f"❌ 找不到标准答案文件: {csv_file}")
        return
    
    if not os.path.exists(html_dir):
        print(f"❌ 找不到HTML报告目录: {html_dir}")
        return
    
    # 加载标准答案
    ground_truth = load_ground_truth(csv_file)
    
    # 获取HTML文件列表
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    # 统计结果
    results = []
    question_stats = {
        'bclc': {'correct': 0, 'total': 0, 'scores': []},
        'child_pugh': {'correct': 0, 'total': 0, 'scores': []},
        'meld': {'correct': 0, 'total': 0, 'scores': []},
        'albi': {'correct': 0, 'total': 0, 'scores': []}
    }
    
    print(f"\n📋 处理HTML文件 ({len(html_files)}个)")
    print("-" * 40)
    
    processed_cases = 0
    
    for html_file in sorted(html_files):
        # 从文件名提取case ID
        match = re.search(r'hcc_analysis_(.+)\.html', html_file)
        if not match:
            continue
            
        case_id = match.group(1)
        
        # 检查是否在指定的20个案例中
        if case_id not in target_cases:
            continue
            
        # 检查是否在标准答案中
        if case_id not in ground_truth:
            print(f"   ⚠️ 案例 {case_id} 在标准答案中未找到")
            continue
        
        processed_cases += 1
        print(f"📄 处理案例 {processed_cases}: {case_id}")
        
        # 提取LLM答案
        html_path = os.path.join(html_dir, html_file)
        llm_answers = extract_llm_answers_from_html(html_path)
        
        if llm_answers is None:
            print(f"   ⚠️ 无法解析HTML文件")
            continue
        
        gt_data = ground_truth[case_id]
        case_result = {'case_id': case_id}
        
        # 比较四个主要问题
        questions = ['bclc', 'child_pugh', 'meld', 'albi']
        question_names = ['BCLC Staging', 'Child-Pugh Score', 'MELD Score', 'ALBI Score']
        
        for q_type, q_name in zip(questions, question_names):
            gt_answer = gt_data.get(q_type, 'Not available')
            llm_answer = llm_answers.get(q_type, 'Not available')
            
            status, score = compare_answers(gt_answer, llm_answer, q_type)
            
            case_result[f'{q_type}_gt'] = gt_answer
            case_result[f'{q_type}_llm'] = llm_answer
            case_result[f'{q_type}_status'] = status
            case_result[f'{q_type}_score'] = score
            
            # 更新统计
            question_stats[q_type]['total'] += 1
            question_stats[q_type]['scores'].append(score)
            if score >= 0.8:  # 认为0.8以上为正确
                question_stats[q_type]['correct'] += 1
            
            print(f"   📊 {q_name}: {status}")
            print(f"      标准答案: {gt_answer}")
            print(f"      LLM答案: {llm_answer}")
        
        results.append(case_result)
        print()
    
    # 生成总结报告
    print("=" * 80)
    print("📊 准确性分析总结")
    print("=" * 80)
    
    overall_scores = []
    
    for q_type, q_name in zip(['bclc', 'child_pugh', 'meld', 'albi'], 
                             ['BCLC Staging', 'Child-Pugh Score', 'MELD Score', 'ALBI Score']):
        stats = question_stats[q_type]
        if stats['total'] > 0:
            accuracy = stats['correct'] / stats['total'] * 100
            avg_score = sum(stats['scores']) / len(stats['scores']) * 100
            overall_scores.extend(stats['scores'])
            
            print(f"\n🎯 {q_name}:")
            print(f"   准确率: {stats['correct']}/{stats['total']} ({accuracy:.1f}%)")
            print(f"   平均得分: {avg_score:.1f}%")
        else:
            print(f"\n🎯 {q_name}: 无数据")
    
    # 总体准确性
    if overall_scores:
        overall_accuracy = (sum(1 for s in overall_scores if s >= 0.8) / len(overall_scores)) * 100
        overall_avg_score = sum(overall_scores) / len(overall_scores) * 100
        
        print(f"\n🏆 总体表现:")
        print(f"   整体准确率: {overall_accuracy:.1f}%")
        print(f"   整体平均得分: {overall_avg_score:.1f}%")
        print(f"   处理案例数: {processed_cases}")
    
    # 保存详细结果到CSV
    output_file = "accuracy_comparison_20_cases_main_questions.csv"
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        if results:
            writer = csv.DictWriter(f, fieldnames=results[0].keys())
            writer.writeheader()
            writer.writerows(results)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    # 生成简化摘要
    summary_file = "accuracy_summary_20_cases_main_questions.csv"
    with open(summary_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Question', 'Correct', 'Total', 'Accuracy (%)', 'Average Score (%)'])
        
        for q_type, q_name in zip(['bclc', 'child_pugh', 'meld', 'albi'], 
                                 ['BCLC Staging', 'Child-Pugh Score', 'MELD Score', 'ALBI Score']):
            stats = question_stats[q_type]
            if stats['total'] > 0:
                accuracy = stats['correct'] / stats['total'] * 100
                avg_score = sum(stats['scores']) / len(stats['scores']) * 100
                writer.writerow([q_name, stats['correct'], stats['total'], f"{accuracy:.1f}", f"{avg_score:.1f}"])
        
        # 添加总体行
        if overall_scores:
            overall_correct = sum(1 for s in overall_scores if s >= 0.8)
            overall_total = len(overall_scores)
            overall_accuracy = overall_correct / overall_total * 100
            overall_avg_score = sum(overall_scores) / len(overall_scores) * 100
            writer.writerow(['Overall', overall_correct, overall_total, f"{overall_accuracy:.1f}", f"{overall_avg_score:.1f}"])
    
    print(f"📋 摘要结果已保存到: {summary_file}")
    
    print("\n✅ 分析完成！")

if __name__ == "__main__":
    main() 