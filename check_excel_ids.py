#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件中是否有所有的文件ID
"""

import pandas as pd

# 配置
EXCEL_PATH = r"C:\Users\<USER>\Desktop\case_new\MIMIC_HCC_GroundTruth.xlsx"

# 需要处理的文件列表
FILES_TO_PROCESS = [
    "10056223-DS-5",
    "10151324-DS-16",
    "10151324-DS-18",
    "10225793-DS-33",
    "10225793-DS-34",
    "10225793-DS-36",
    "10262565-DS-19",
    "10262565-DS-20",
    "10388675-DS-19",
    "10388675-DS-20",
    "10666715-DS-8",
    "10747475-DS-4",
    "10760122-DS-21",
    "10880579-DS-11",
    "10902714-DS-17",
    "10923555-DS-9",
    "10960817-DS-14",
    "11102747-DS-2",
    "11198012-DS-6",
    "11198012-DS-7"
]

def main():
    """主函数"""
    print("检查Excel文件中的文件ID...")
    
    # 读取Excel文件
    try:
        df = pd.read_excel(EXCEL_PATH)
        
        found_ids = []
        missing_ids = []
        
        for file_id in FILES_TO_PROCESS:
            row = df[df['note_id'] == file_id]
            if row.empty:
                missing_ids.append(file_id)
            else:
                found_ids.append(file_id)
        
        print(f"\n✅ Excel中存在的文件ID ({len(found_ids)}/{len(FILES_TO_PROCESS)}):")
        for file_id in found_ids:
            print(f"- {file_id}")
        
        print(f"\n❌ Excel中缺失的文件ID ({len(missing_ids)}/{len(FILES_TO_PROCESS)}):")
        for file_id in missing_ids:
            print(f"- {file_id}")
        
        # 检查Excel中的文件ID是否有重复
        note_ids = df['note_id'].dropna().tolist()
        duplicates = set([x for x in note_ids if note_ids.count(x) > 1])
        
        if duplicates:
            print(f"\n⚠️ Excel中有重复的文件ID:")
            for dup in duplicates:
                count = note_ids.count(dup)
                print(f"- {dup} (出现 {count} 次)")
        else:
            print("\n✅ Excel中没有重复的文件ID")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")

if __name__ == "__main__":
    main() 