#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动填充 Excel 工具 - 保持原始格式版本
一键运行，自动将HTML文件数据填入Excel的E、F、G列，保持原始样式不变

使用方法：
python auto_fill_excel.py

或者指定文件：
python auto_fill_excel.py --excel "your_excel_file.xlsx" --html_dir "html_reports_expert"
"""

import pandas as pd
import os
import re
import argparse
from bs4 import BeautifulSoup
from datetime import datetime
import openpyxl
from openpyxl import load_workbook

def extract_data_from_html(html_file_path):
    """从HTML文件中提取数据"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        extracted_data = {}
        
        # HCC问题映射表
        question_mappings = {
            "0.1": ["BCLC Staging"],
            "0.2": ["Child-Pugh score"],
            "0.3": ["MELD score"],
            "0.4": ["ALBI score"],
            "1.1": ["Age"],
            "1.2": ["Gender"],
            "2.1": ["Number of confirmed HCC"],
            "2.2": ["Largest size of HCC"],
            "3": ["Child-Pugh classification"],
            "4.1": ["ECOG performance status"],
            "4.2": ["Extent of physical activity restriction"],
            "4.3": ["Activity status"],
            "4.4": ["Deceased or expired"],
            "5.1": ["Grade of encephalopathy"],
            "5.2": ["Diagnosis of encephalopathy"],
            "5.3": ["Mental status"],
            "5.4": ["Level of consciousness"],
            "6.1": ["Grade of ascites"],
            "6.2": ["Diagnosis of ascites"],
            "6.3": ["Discharge for ascites"],
            "7.1": ["Total bilirubin"],
            "7.2": ["Albumin"],
            "7.3": ["Sodium"],
            "7.4": ["Prothrombin time"],
            "7.5": ["International normalized ratio"],
            "7.6": ["Creatinine"],
            "7.7": ["Alpha-fetoprotein level"],
            "7.8": ["Indocyanine green retention at 15 minutes"],
            "8.1": ["Presence of macrovascular invasion"],
            "8.2": ["Presence of lymphovascular invasion"],
            "8.3": ["Extent of invasion"],
            "8.4": ["Presence of portal vein thrombosis"],
            "9.1": ["Presence of metastatic lymph node"],
            "9.2": ["Presence of extrahepatic spread"],
            "9.3": ["Presence of metastatic HCC"],
            "9.4": ["Site of distant metastasis"],
            "10.1": ["Presence of cirrhosis"],
            "10.2": ["Severe comorbidities"],
            "11": ["Prior treatment received"],
            "12": ["Liver transplant"],
            "13.1": ["Histological tumour grade"],
            "13.2": ["Histological tumour type"],
            "13.3": ["Margin status"],
            "13.4": ["Presence of satellitosis"]
        }
        
        # 查找所有问题项
        question_items = soup.find_all('div', class_='question-item')
        
        for item in question_items:
            h4_tag = item.find('h4')
            if not h4_tag:
                continue
                
            question_text = h4_tag.get_text().strip()
            
            # 提取问题编号
            question_match = re.match(r'^(\d+(?:\.\d+)*)', question_text)
            question_no = None
            
            if question_match:
                question_no = question_match.group(1)
            else:
                # 特殊格式查找
                for standard_no, special_titles in question_mappings.items():
                    for special_title in special_titles:
                        if special_title.lower() in question_text.lower():
                            question_no = standard_no
                            break
                    if question_no:
                        break
            
            if not question_no:
                continue
            
            # 提取答案和引用
            answer_group = item.find('div', class_='answer-group')
            if not answer_group:
                continue
            
            answer = ""
            supporting_quotes = []
            highlighted_quotes = []
            
            answer_items = answer_group.find_all('div', class_='answer-item')
            for answer_item in answer_items:
                label = answer_item.find('span', class_='label')
                value = answer_item.find('span', class_='value')
                
                if label and value:
                    label_text = label.get_text().strip().lower()
                    
                    if label_text == 'answer':
                        answer = value.get_text().strip()
                    elif label_text == 'supporting quotes':
                        # 提取引用链接
                        quote_links = value.find_all('span', class_='quote-link')
                        for quote_link in quote_links:
                            quote_text = quote_link.get('data-quote')
                            if quote_text:
                                quote_clean = quote_text.strip()
                                supporting_quotes.append(quote_clean)
                                highlighted_quotes.append(quote_clean)
                        
                        # 检查无法高亮的文本
                        no_quotes_span = value.find('span', class_='no-quotes')
                        if no_quotes_span:
                            no_quotes_text = no_quotes_span.get_text().strip()
                            supporting_quotes.append(no_quotes_text)
            
            extracted_data[question_no] = {
                'answer': answer,
                'supporting_quotes': supporting_quotes,
                'highlighted_quotes': highlighted_quotes
            }
        
        return extracted_data
        
    except Exception as e:
        print(f"❌ 处理HTML文件失败 {html_file_path}: {e}")
        return {}

def find_html_file(case_id, html_dir="html_reports_expert"):
    """根据case_id查找HTML文件"""
    possible_files = [
        f"{case_id}_HCC_analysis.html",
        f"{case_id}_expert_analysis.html",
        f"{case_id}_validated_quotes.html"
    ]

    for filename in possible_files:
        full_path = os.path.join(html_dir, filename)
        if os.path.exists(full_path):
            return full_path
    return None

def auto_fill_excel_preserve_format(excel_file, html_dir="html_reports_expert", output_file=None):
    """自动填充Excel文件，保持原始格式"""
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    if not os.path.exists(html_dir):
        print(f"❌ HTML目录不存在: {html_dir}")
        return False
    
    # 设置输出文件名
    if output_file is None:
        base_name = os.path.splitext(excel_file)[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{base_name}_filled_{timestamp}.xlsx"
    
    print(f"🔬 开始自动填充Excel文件（保持原始格式）")
    print(f"📁 输入文件: {excel_file}")
    print(f"📁 HTML目录: {html_dir}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    try:
        # 使用openpyxl加载工作簿，保持格式
        wb = load_workbook(excel_file)
        
        # 检查是否有 'accuracy report' 工作表
        if 'accuracy report' not in wb.sheetnames:
            print(f"❌ 找不到 'accuracy report' 工作表")
            return False
        
        ws = wb['accuracy report']
        
        # 使用pandas读取数据来获取数据结构
        df = pd.read_excel(excel_file, sheet_name='accuracy report', skiprows=1)
        print(f"📊 读取到 {len(df)} 行数据")
        
        # 重命名列以便处理
        df.columns = ['Case_No', 'Case_ID', 'Question_No', 'Question', 'LLM_Answer', 'Supporting_Sentences', 'Highlighted_Sentences']
        
        # 统计信息
        unique_cases = df['Case_ID'].dropna().unique()
        total_cases = len(unique_cases)
        success_count = 0
        total_filled = 0
        f_filled = 0
        g_filled = 0
        
        print(f"🎯 需要处理 {total_cases} 个Case ID")
        
        # 处理每个Case ID
        for i, case_id in enumerate(unique_cases, 1):
            print(f"\n[{i}/{total_cases}] 🔍 处理: {case_id}")
            
            # 查找HTML文件
            html_file = find_html_file(case_id, html_dir)
            if not html_file:
                print(f"  ⚠️ 未找到HTML文件")
                continue
            
            # 提取数据
            extracted_data = extract_data_from_html(html_file)
            if not extracted_data:
                print(f"  ⚠️ 无法提取数据")
                continue
            
            # 填充数据到对应的Excel单元格
            case_rows = df[df['Case_ID'] == case_id]
            case_filled = 0
            case_f = 0
            case_g = 0
            
            for idx, row in case_rows.iterrows():
                question_no = str(row['Question_No']).strip()
                
                if question_no in extracted_data:
                    data = extracted_data[question_no]
                    
                    # 计算Excel中的实际行号（考虑跳过的行和header）
                    excel_row = idx + 3  # +1为pandas索引转换，+1为跳过的行，+1为header行
                    
                    # E列：答案（第5列）
                    if data['answer']:
                        ws.cell(row=excel_row, column=5).value = data['answer']
                        case_filled += 1
                        total_filled += 1
                    
                    # F列：支持性语句（第6列）
                    if data['supporting_quotes']:
                        quotes_str = '; '.join(data['supporting_quotes'])
                        ws.cell(row=excel_row, column=6).value = quotes_str
                        case_f += 1
                        f_filled += 1
                    
                    # G列：高亮语句（第7列）
                    if data['highlighted_quotes']:
                        highlighted_str = '; '.join(data['highlighted_quotes'])
                        ws.cell(row=excel_row, column=7).value = highlighted_str
                        case_g += 1
                        g_filled += 1
            
            print(f"  ✅ 答案:{case_filled} F列:{case_f} G列:{case_g}")
            success_count += 1
        
        # 保存结果，保持所有原始格式
        print(f"\n💾 保存结果到: {output_file}")
        wb.save(output_file)
        
        # 打印总结
        print("=" * 60)
        print("🎉 自动填充完成！（格式已保留）")
        print(f"📊 成功处理: {success_count}/{total_cases} 个Case ID")
        print(f"📝 E列(答案)填充: {total_filled} 个")
        print(f"📝 F列(支持性语句)填充: {f_filled} 个")  
        print(f"📝 G列(高亮语句)填充: {g_filled} 个")
        print(f"✅ 输出文件: {output_file}")
        print("🎨 原始Excel格式和样式已完全保留")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动填充Excel工具（保持格式）')
    parser.add_argument('--excel', '-e', 
                       default="GPT4.1_accuracy report table 20250620.xlsx",
                       help='Excel文件路径 (默认: GPT4.1_accuracy report table 20250620.xlsx)')
    parser.add_argument('--html_dir', '-d', 
                       default="html_reports_expert",
                       help='HTML文件目录 (默认: html_reports_expert)')
    parser.add_argument('--output', '-o', 
                       help='输出文件名 (默认: 自动生成)')
    
    args = parser.parse_args()
    
    print("🚀 Excel自动填充工具（保持格式版）")
    print("=" * 60)
    
    success = auto_fill_excel_preserve_format(args.excel, args.html_dir, args.output)
    
    if success:
        print("\n🎊 任务完成！")
        print("💡 提示：原始Excel的所有格式、样式、颜色都已保留")
    else:
        print("\n💥 任务失败！")

if __name__ == "__main__":
    main() 