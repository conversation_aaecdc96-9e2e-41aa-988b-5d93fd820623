#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引用验证模块 - 验证支持引用的有效性
"""

import re

class QuoteValidator:
    """引用验证器"""
    
    # 系统生成内容关键词
    SYSTEM_KEYWORDS = [
        'criteria', 'classification', 'meets criteria', 'according to', 'based on',
        'therefore', 'rationale', 'fulfills', 'stage', 'staging', 'rule',
        'ata risk', 'tnm', 'ajcc', 'age from clinical data', 'years old',
        'clinical n0', 'pathologic n1', 'lymph nodes', 'micrometastases'
    ]
    
    # 医学规则关键词
    MEDICAL_RULE_PATTERNS = [
        r'if.*then',
        r'\d+\s*(cm|mm)',
        r'stage\s+[IVX]+[AB]?',
        r'T[0-4][ab]?',
        r'N[0-1][ab]?',
        r'M[0-1]',
        r'risk.*category',
        r'invasion.*present',
        r'metastasis.*lymph'
    ]
    
    @classmethod
    def is_valid_quote(cls, quote, original_text):
        """验证引用是否有效"""
        if not quote or quote.strip() == "":
            return True  # 空引用是有效的
        
        quote = quote.strip()
        
        # 检查是否包含系统关键词
        quote_lower = quote.lower()
        for keyword in cls.SYSTEM_KEYWORDS:
            if keyword in quote_lower:
                return False
        
        # 检查是否匹配医学规则模式
        for pattern in cls.MEDICAL_RULE_PATTERNS:
            if re.search(pattern, quote_lower):
                return False
        
        # 检查是否在原文中存在
        if quote not in original_text:
            # 尝试模糊匹配（去除标点符号和空格）
            quote_clean = re.sub(r'[^\w\s]', '', quote).replace(' ', '')
            text_clean = re.sub(r'[^\w\s]', '', original_text).replace(' ', '')
            
            if quote_clean not in text_clean:
                return False
        
        return True
    
    @classmethod
    def validate_analysis_quotes(cls, analysis_result, original_text):
        """验证分析结果中的所有引用"""
        if not analysis_result:
            return analysis_result, []
        
        invalid_quotes = []
        validated_result = {}
        
        for question_id, data in analysis_result.items():
            if isinstance(data, dict) and 'supporting_quote' in data:
                quote = data['supporting_quote']
                
                if not cls.is_valid_quote(quote, original_text):
                    invalid_quotes.append({
                        'question_id': question_id,
                        'invalid_quote': quote
                    })
                    # 将无效引用替换为空字符串
                    validated_result[question_id] = {
                        'answer': data['answer'],
                        'supporting_quote': ''
                    }
                else:
                    validated_result[question_id] = data
            else:
                validated_result[question_id] = data
        
        return validated_result, invalid_quotes
    
    @classmethod
    def get_validation_stats(cls, analysis_result, original_text):
        """获取验证统计信息"""
        if not analysis_result:
            return {"total": 0, "valid": 0, "invalid": 0, "validity_rate": 0}
        
        total_quotes = 0
        valid_quotes = 0
        
        for question_id, data in analysis_result.items():
            if isinstance(data, dict) and 'supporting_quote' in data:
                quote = data['supporting_quote']
                if quote and quote.strip():  # 只统计非空引用
                    total_quotes += 1
                    if cls.is_valid_quote(quote, original_text):
                        valid_quotes += 1
        
        validity_rate = (valid_quotes / total_quotes * 100) if total_quotes > 0 else 100
        
        return {
            "total": total_quotes,
            "valid": valid_quotes,
            "invalid": total_quotes - valid_quotes,
            "validity_rate": round(validity_rate, 1)
        } 