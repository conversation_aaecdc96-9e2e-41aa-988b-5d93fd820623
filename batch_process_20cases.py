#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理20个HCC案例的脚本
每个案例之间间隔1分钟
"""

import os
import time
import subprocess
import sys

def run_single_case(case_file):
    """运行单个案例"""
    print(f"\n{'='*60}")
    print(f"开始处理案例: {case_file}")
    print(f"{'='*60}")
    
    try:
        # 运行HCC分析器
        cmd = [sys.executable, "hcc_analyzer_simple.py", "--file", f"extracted_cases_20250717/{case_file}"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print(f"✅ 案例 {case_file} 处理成功")
            print(f"输出: {result.stdout[-200:]}...")  # 显示最后200个字符
        else:
            print(f"❌ 案例 {case_file} 处理失败")
            print(f"错误: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 案例 {case_file} 处理超时")
    except Exception as e:
        print(f"💥 案例 {case_file} 处理异常: {str(e)}")

def main():
    """主函数"""
    # 20个案例文件列表
    case_files = [
        "10056223-DS-5.txt",
        "10151324-DS-18.txt", 
        "10225793-DS-34.txt",
        "10747475-DS-4.txt",
        "10880579-DS-11.txt",
        "10960817-DS-14.txt",
        "11102747-DS-2.txt",
        "11198012-DS-6.txt",
        "11265636-DS-13.txt",
        "11327487-DS-19.txt",
        "11329198-DS-5.txt",
        "11349875-DS-8.txt",
        "11417994-DS-9.txt",
        "11419849-DS-24.txt",
        "11455644-DS-15.txt",
        "11714491-DS-4.txt",
        "11914986-DS-12.txt",
        "11960904-DS-5.txt",
        "12032388-DS-16.txt",
        "12344358-DS-12.txt"
    ]
    
    print(f"🚀 开始批量处理 {len(case_files)} 个HCC案例")
    print(f"⏱️  每个案例之间间隔1分钟")
    print(f"📁 案例目录: extracted_cases_20250717/")
    
    start_time = time.time()
    
    for i, case_file in enumerate(case_files, 1):
        print(f"\n📋 进度: {i}/{len(case_files)}")
        
        # 检查文件是否存在
        file_path = f"extracted_cases_20250717/{case_file}"
        if not os.path.exists(file_path):
            print(f"⚠️  文件不存在: {file_path}")
            continue
            
        # 运行案例
        run_single_case(case_file)
        
        # 如果不是最后一个案例，等待1分钟
        if i < len(case_files):
            print(f"⏳ 等待60秒后处理下一个案例...")
            time.sleep(60)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n{'='*60}")
    print(f"🎉 批量处理完成!")
    print(f"📊 总耗时: {total_time/60:.1f} 分钟")
    print(f"📁 处理了 {len(case_files)} 个案例")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 