#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载模块 - 处理Excel年龄数据和文件读取
"""

import os
import pandas as pd
from config import PATHS

class AgeDataLoader:
    """年龄数据加载器"""
    
    def __init__(self):
        self.age_dict = {}
        self.load_age_data()
    
    def load_age_data(self):
        """从Excel文件加载年龄数据"""
        try:
            df = pd.read_excel(PATHS['excel_file'])
            
            if 'case_submitter_id' in df.columns and 'age_at_index' in df.columns:
                for _, row in df.iterrows():
                    tcga_id = row['case_submitter_id']
                    age = row['age_at_index']
                    if pd.notna(tcga_id) and pd.notna(age):
                        self.age_dict[tcga_id] = int(age)
                        
            print(f"📊 加载了 {len(self.age_dict)} 个样本的年龄数据")
            
        except Exception as e:
            print(f"⚠️ 加载年龄数据失败: {e}")
            self.age_dict = {}
    
    def get_age(self, filename):
        """根据文件名获取年龄"""
        tcga_id = os.path.splitext(filename)[0]
        return self.age_dict.get(tcga_id, None)

class FileLoader:
    """文件加载器"""
    
    @staticmethod
    def read_pathology_report(file_path):
        """读取病理报告文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
            return None
    
    @staticmethod
    def get_pathology_files(input_dir):
        """获取所有病理报告文件列表"""
        import glob
        pattern = os.path.join(input_dir, "*.txt")
        files = glob.glob(pattern)
        return sorted(files) 