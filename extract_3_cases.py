import pandas as pd
import os
from datetime import datetime

# 要提取的3个case ID
target_cases = [
    "11198012-DS-6",
    "11714491-DS-4", 
    "10225793-DS-34"
]

# 目标文件夹
folder_name = "extracted_cases_20250715"
os.makedirs(folder_name, exist_ok=True)
print(f"已创建/确认文件夹: {folder_name}")

# 读取CSV文件
csv_file = "MIMIC_HCC_GroundTruth(MIMIC_HCC_GroundTruth).csv"
print("正在读取CSV文件...")

try:
    df = pd.read_csv(csv_file)
    print(f"CSV文件包含 {len(df)} 行数据")
    
    found_cases = []
    
    # 遍历每一行查找匹配的case
    for index, row in df.iterrows():
        # 将行数据转换为字符串进行搜索
        row_str = ' '.join(str(val) for val in row.values if pd.notna(val))
        
        for case_id in target_cases:
            # 检查完整case ID或主要ID部分
            main_id = case_id.split('-')[0]
            
            if case_id in row_str or main_id in row_str:
                found_cases.append((case_id, index, row))
                print(f"✅ 找到匹配的case: {case_id} (行 {index})")
                
                # 保存为txt文件
                output_file = os.path.join(folder_name, f"{case_id}.txt")
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"Case ID: {case_id}\n")
                    f.write(f"Row Index: {index}\n")
                    f.write("="*50 + "\n")
                    
                    # 写入所有列的数据
                    for col, val in row.items():
                        if pd.notna(val):
                            f.write(f"{col}: {val}\n")
                    
                    # 如果有text列，单独显示
                    if 'text' in df.columns and pd.notna(row['text']):
                        f.write("\n" + "="*50 + "\n")
                        f.write("MEDICAL TEXT:\n")
                        f.write("="*50 + "\n")
                        f.write(str(row['text']))
                
                print(f"📁 已保存: {output_file}")
                break
    
    print(f"\n📊 提取结果:")
    print(f"✅ 成功找到 {len(found_cases)} 个案例")
    
    # 检查未找到的case
    found_case_ids = [case[0] for case in found_cases]
    missing_cases = [case for case in target_cases if case not in found_case_ids]
    
    if missing_cases:
        print(f"❌ 未找到的case: {missing_cases}")
    else:
        print("🎉 所有3个案例都已成功提取!")

except Exception as e:
    print(f"❌ 处理过程中出现错误: {e}")
    import traceback
    traceback.print_exc()