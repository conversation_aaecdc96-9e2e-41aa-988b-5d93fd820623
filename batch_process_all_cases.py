#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理所有20个案例的脚本
每个案例间隔1分钟，结果保存到指定文件夹
"""

import os
import time
import subprocess
import shutil
from datetime import datetime

def process_all_cases():
    """批量处理所有案例"""
    
    # 输入和输出文件夹
    input_folder = "extracted_cases_20250715"
    output_folder = "batch_results_20250715"
    
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 获取所有txt文件
    txt_files = [f for f in os.listdir(input_folder) if f.endswith('.txt')]
    txt_files.sort()  # 按文件名排序
    
    print(f"[INFO] Found {len(txt_files)} case files")
    print(f"[INFO] Input folder: {input_folder}")
    print(f"[INFO] Output folder: {output_folder}")
    print(f"[INFO] Interval between cases: 1 minute")
    print("=" * 60)
    
    start_time = datetime.now()
    
    for i, txt_file in enumerate(txt_files, 1):
        case_name = os.path.splitext(txt_file)[0]
        input_path = os.path.join(input_folder, txt_file)
        
        print(f"\n[PROCESSING] [{i:2d}/{len(txt_files)}] Processing case: {case_name}")
        print(f"[TIME] Start time: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 运行分析
            cmd = f'python hcc_analyzer_simple.py --file "{input_path}"'
            print(f"[CMD] Executing command: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                print(f"[SUCCESS] Analysis completed: {case_name}")
                
                # 移动生成的HTML文件到输出文件夹
                html_file = f"hcc_analysis_{case_name}.html"
                if os.path.exists(html_file):
                    output_path = os.path.join(output_folder, html_file)
                    shutil.move(html_file, output_path)
                    print(f"[SAVED] Result saved: {output_path}")
                else:
                    print(f"[WARNING] Generated HTML file not found: {html_file}")
                    
            else:
                print(f"[ERROR] Analysis failed: {case_name}")
                print(f"[ERROR] Error message: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"[TIMEOUT] Analysis timeout: {case_name} (30 minutes)")
        except Exception as e:
            print(f"[ERROR] Processing error: {case_name} - {str(e)}")
        
        # 如果不是最后一个案例，等待1分钟
        if i < len(txt_files):
            print(f"[WAITING] Waiting 1 minute...")
            time.sleep(60)
    
    end_time = datetime.now()
    total_time = end_time - start_time
    
    print("\n" + "=" * 60)
    print(f"[COMPLETE] Batch processing completed!")
    print(f"[INFO] Total cases: {len(txt_files)}")
    print(f"[TIME] Total time: {total_time}")
    print(f"[INFO] Results saved in: {output_folder}")
    
    # 统计结果
    html_files = [f for f in os.listdir(output_folder) if f.endswith('.html')]
    print(f"[SUCCESS] Successfully generated: {len(html_files)} HTML reports")

if __name__ == "__main__":
    process_all_cases() 