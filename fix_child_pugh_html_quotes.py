#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修正3. Child-<PERSON><PERSON> classification的supporting sentences
"""
import os
import re

HTML_DIR = "batch_results_20250715"
REPLACE_TEXT = "based on total bilirubin, albumin, ascites, ascites, encephalopathy, and INR"

for fname in os.listdir(HTML_DIR):
    if not fname.endswith('.html'):
        continue
    fpath = os.path.join(HTML_DIR, fname)
    with open(fpath, 'r', encoding='utf-8') as f:
        html = f.read()
    # 用正则找到3. Child-<PERSON>ugh classification的supporting quotes并替换
    new_html = re.sub(
        r'(<h4>3\. Child-Pugh classification</h4>[\s\S]*?<span class="label">Supporting quotes:</span>\s*<span class="value quotes-container">)([\s\S]*?)(</span>)',
        r'\1' + REPLACE_TEXT + r'\3',
        html,
        flags=re.IGNORECASE
    )
    with open(fpath, 'w', encoding='utf-8') as f:
        f.write(new_html)
print("[SUCCESS] All Child-Pugh supporting sentences replaced.") 