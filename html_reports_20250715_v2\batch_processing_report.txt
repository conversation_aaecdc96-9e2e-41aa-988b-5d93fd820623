# 格式化案例批量分析报告

## 处理时间
2025-07-15 21:09:31

## 处理统计
- 总案例数: 20
- 成功处理: 20
- 处理失败: 0

## 成功处理的案例
1. 10056223-DS-5
2. 10151324-DS-18
3. 10225793-DS-34
4. 10747475-DS-4
5. 10880579-DS-11
6. 10960817-DS-14
7. 11102747-DS-2
8. 11198012-DS-6
9. 11265636-DS-13
10. 11327487-DS-19
11. 11329198-DS-5
12. 11349875-DS-8
13. 11417994-DS-9
14. 11419849-DS-24
15. 11455644-DS-15
16. 11714491-DS-4
17. 11914986-DS-12
18. 11960904-DS-5
19. 12032388-DS-16
20. 12344358-DS-12

## 输出位置
- HTML报告保存在: html_reports_20250715_v2/
- 格式化案例来源: formatted_cases_20250715/

## 说明
所有案例都使用格式化后的文本进行分析，以提高GPT-4.1的分析准确性。
使用 hcc_analyzer_simple.py 进行分析，包含完整的HCC评估和问题回答。
