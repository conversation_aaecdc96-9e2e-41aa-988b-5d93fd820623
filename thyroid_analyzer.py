#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甲状腺癌病理分析器 - 主分析模块
"""

import os
from data_loader import AgeDataLoader, FileLoader
from azure_client import AzureGPTClient
from quote_validator import QuoteValidator
from html_generator import HTMLReportGenerator
from config import PATHS

class ThyroidPathologyAnalyzer:
    """甲状腺癌病理分析器"""
    
    def __init__(self):
        self.age_loader = AgeDataLoader()
        self.file_loader = FileLoader()
        self.gpt_client = AzureGPTClient()
        self.html_generator = HTMLReportGenerator()
        
        print("🔬 甲状腺癌病理分析系统已初始化")
        print(f"📊 已加载 {len(self.age_loader.age_dict)} 个样本的年龄数据")
    
    def analyze_single_file(self, file_path, output_dir=None):
        """分析单个病理报告文件"""
        
        if output_dir is None:
            output_dir = PATHS['output_dir']
        
        filename = os.path.basename(file_path)
        tcga_id = os.path.splitext(filename)[0]
        
        print(f"\n{'='*50}")
        print(f"🔍 开始分析文件: {filename}")
        print(f"📝 TCGA ID: {tcga_id}")
        
        # 1. 读取病理报告
        content = self.file_loader.read_pathology_report(file_path)
        if not content:
            print(f"❌ 无法读取文件: {file_path}")
            return False
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 2. 获取年龄信息
        age_info = self.age_loader.get_age(filename)
        if age_info:
            print(f"🎂 年龄信息: {age_info} 岁")
        else:
            print("⚠️ 未找到年龄信息")
        
        # 3. 调用GPT-4进行分析
        analysis_result = self.gpt_client.analyze_pathology(content, age_info)
        if not analysis_result:
            print("❌ GPT-4分析失败")
            return False
        
        # 4. 验证支持引用
        validated_result, invalid_quotes = QuoteValidator.validate_analysis_quotes(
            analysis_result, content
        )
        
        validation_stats = QuoteValidator.get_validation_stats(analysis_result, content)
        
        if invalid_quotes:
            print(f"⚠️ 发现 {len(invalid_quotes)} 个无效引用，已自动修正")
        
        print(f"📈 引用验证统计: {validation_stats['valid']}/{validation_stats['total']} " +
              f"有效 ({validation_stats['validity_rate']}%)")
        
        # 5. 生成HTML报告
        html_content = self.html_generator.create_html_report(
            filename, content, validated_result, age_info, validation_stats
        )
        
        # 6. 保存HTML报告
        output_path = os.path.join(output_dir, f"{tcga_id}_validated_quotes.html")
        success = self.html_generator.save_html_report(html_content, output_path)
        
        if success:
            print(f"✅ 分析完成: {output_path}")
            return True
        else:
            print("❌ 报告保存失败")
            return False
    
    def batch_analyze(self, input_dir=None, output_dir=None):
        """批量分析病理报告"""
        
        if input_dir is None:
            input_dir = PATHS['input_dir']
        if output_dir is None:
            output_dir = PATHS['output_dir']
        
        print(f"\n{'='*60}")
        print("🚀 开始批量分析")
        print(f"📂 输入目录: {input_dir}")
        print(f"📁 输出目录: {output_dir}")
        
        # 获取所有病理报告文件
        files = self.file_loader.get_pathology_files(input_dir)
        
        if not files:
            print("❌ 未找到任何.txt文件")
            return
        
        print(f"📋 找到 {len(files)} 个文件待处理")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 统计信息
        success_count = 0
        total_count = len(files)
        
        # 批量处理
        for i, file_path in enumerate(files, 1):
            print(f"\n📊 进度: {i}/{total_count}")
            
            try:
                success = self.analyze_single_file(file_path, output_dir)
                if success:
                    success_count += 1
                else:
                    print(f"❌ 处理失败: {os.path.basename(file_path)}")
                    
            except KeyboardInterrupt:
                print(f"\n⏹️ 用户中断，已处理 {i-1}/{total_count} 个文件")
                break
            except Exception as e:
                print(f"❌ 处理文件时发生错误: {e}")
                continue
        
        # 打印总结
        print(f"\n{'='*60}")
        print("📊 批量分析完成!")
        print(f"✅ 成功处理: {success_count}/{total_count} 个文件")
        print(f"❌ 失败: {total_count - success_count} 个文件")
        print(f"📈 成功率: {success_count/total_count*100:.1f}%")
        print(f"📁 输出目录: {output_dir}")

def main():
    """主函数"""
    import sys
    
    analyzer = ThyroidPathologyAnalyzer()
    
    if len(sys.argv) > 1:
        # 分析指定文件
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            analyzer.analyze_single_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    else:
        # 批量分析
        analyzer.batch_analyze()

if __name__ == "__main__":
    main() 