#!/usr/bin/env python3
"""
分析所有20个案例的HTML报告并与标答进行比较
"""

import os
import re
from bs4 import BeautifulSoup

def load_ground_truth(file_path):
    """加载标答数据"""
    ground_truth = {}

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 6:
                case_id = parts[1]
                question_type = parts[4]
                # 答案在第6列（索引5），但可能有很多空白列，需要找到实际的答案
                answer = None
                for i in range(5, len(parts)):
                    if parts[i].strip():
                        answer = parts[i].strip()
                        break

                if not answer:
                    continue

                if case_id not in ground_truth:
                    ground_truth[case_id] = {}

                if 'BCLC staging' in question_type:
                    ground_truth[case_id]['BCLC'] = answer
                elif 'Child-Pugh score' in question_type:
                    ground_truth[case_id]['Child-Pugh'] = answer
                elif 'MELD score' in question_type:
                    ground_truth[case_id]['MELD'] = answer
                elif 'ALBI score' in question_type:
                    ground_truth[case_id]['ALBI'] = answer

    return ground_truth

def extract_scores_from_html(html_file):
    """从HTML文件中提取分数"""
    scores = {}
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找所有score-section
        score_sections = soup.find_all('div', class_='score-section')
        
        for section in score_sections:
            title_elem = section.find('h4')
            if not title_elem:
                continue
            
            title = title_elem.text.strip()
            
            # 查找Answer
            score_details = section.find('div', class_='score-details')
            if score_details:
                # 查找<div><strong>Answer:</strong> X</div>格式
                for div in score_details.find_all('div', recursive=False):
                    strong = div.find('strong')
                    if strong and 'Answer:' in strong.text:
                        answer = div.text.replace(strong.text, '').strip()
                        
                        if 'BCLC' in title:
                            scores['BCLC'] = answer
                        elif 'Child-Pugh' in title:
                            scores['Child-Pugh'] = answer
                        elif 'MELD' in title:
                            scores['MELD'] = answer
                        elif 'ALBI' in title:
                            scores['ALBI'] = answer
                        break
    
    except Exception as e:
        print(f"Error processing {html_file}: {e}")
    
    return scores

def compare_results(system_result, ground_truth):
    """比较系统结果和标答"""
    comparison = {}
    
    for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
        sys_val = system_result.get(metric, 'N/A')
        gt_val = ground_truth.get(metric, 'N/A')
        
        # 标准化比较
        sys_val_norm = str(sys_val).strip()
        gt_val_norm = str(gt_val).strip()
        
        # 特殊处理ALBI的精度问题
        if metric == 'ALBI' and sys_val_norm != '-' and gt_val_norm != '-':
            try:
                sys_float = float(sys_val_norm)
                gt_float = float(gt_val_norm)
                # 如果差异小于0.1，认为是正确的
                is_correct = abs(sys_float - gt_float) < 0.1
            except:
                is_correct = sys_val_norm == gt_val_norm
        else:
            is_correct = sys_val_norm == gt_val_norm
        
        comparison[metric] = {
            'system': sys_val_norm,
            'ground_truth': gt_val_norm,
            'correct': is_correct
        }
    
    return comparison

def main():
    # 文件路径
    ground_truth_file = "1 10056223-DS-5 1 0.1 BCLC staging A"
    html_reports_dir = "html_reports_split_cases_20250104"
    
    # 加载标答
    print("🔍 加载标答数据...")
    ground_truth = load_ground_truth(ground_truth_file)
    print(f"✅ 加载了 {len(ground_truth)} 个案例的标答数据")
    
    # 分析所有HTML报告
    print("\n📊 分析HTML报告...")
    results = {}
    
    for html_file in sorted(os.listdir(html_reports_dir)):
        if html_file.endswith('.html'):
            case_id = html_file.replace('hcc_analysis_', '').replace('.html', '')
            html_path = os.path.join(html_reports_dir, html_file)
            
            print(f"  处理案例: {case_id}")
            
            # 提取系统结果
            system_scores = extract_scores_from_html(html_path)
            
            # 获取标答
            gt_scores = ground_truth.get(case_id, {})
            
            # 比较结果
            comparison = compare_results(system_scores, gt_scores)
            
            results[case_id] = {
                'system': system_scores,
                'ground_truth': gt_scores,
                'comparison': comparison
            }
    
    # 生成详细报告
    print("\n" + "="*80)
    print("📋 详细分析报告")
    print("="*80)
    
    total_questions = 0
    total_correct = 0
    metric_stats = {'BCLC': [0, 0], 'Child-Pugh': [0, 0], 'MELD': [0, 0], 'ALBI': [0, 0]}
    
    for case_id in sorted(results.keys()):
        result = results[case_id]
        comparison = result['comparison']
        
        print(f"\n📄 案例 {case_id}:")
        
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            comp = comparison[metric]
            status = "✅" if comp['correct'] else "❌"
            print(f"  {metric:12}: {comp['system']:8} vs {comp['ground_truth']:8} {status}")
            
            # 统计
            if comp['ground_truth'] != 'N/A':  # 只统计有标答的
                metric_stats[metric][1] += 1  # 总数
                total_questions += 1
                if comp['correct']:
                    metric_stats[metric][0] += 1  # 正确数
                    total_correct += 1
    
    # 总体统计
    print("\n" + "="*80)
    print("📊 准确率统计")
    print("="*80)
    
    for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
        correct, total = metric_stats[metric]
        if total > 0:
            accuracy = correct / total * 100
            print(f"{metric:12}: {correct:2}/{total:2} = {accuracy:5.1f}%")
        else:
            print(f"{metric:12}: 无数据")
    
    if total_questions > 0:
        overall_accuracy = total_correct / total_questions * 100
        print(f"\n总体准确率: {total_correct}/{total_questions} = {overall_accuracy:.1f}%")
    
    # 错误分析
    print("\n" + "="*80)
    print("❌ 错误分析")
    print("="*80)
    
    for case_id in sorted(results.keys()):
        result = results[case_id]
        comparison = result['comparison']
        
        errors = []
        for metric in ['BCLC', 'Child-Pugh', 'MELD', 'ALBI']:
            comp = comparison[metric]
            if not comp['correct'] and comp['ground_truth'] != 'N/A':
                errors.append(f"{metric}: {comp['system']}→{comp['ground_truth']}")
        
        if errors:
            print(f"{case_id}: {', '.join(errors)}")

if __name__ == "__main__":
    main()
