#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import requests

# Azure OpenAI API配置
AZURE_OPENAI_API_KEY = '3quun9aatHqe8WuDASukJt2TlEjtcOhNpCKMv4wsFQ62ohJgjb4QJQQJ99ALACfhMk5XJ3w3AAABACOG5Mon'
ENDPOINT_URL = 'https://llm4healthcare.openai.azure.com/'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# 要分析的文件列表
FILE_LIST = [
    "10056223-DS-5",
    "10151324-DS-16",
    "10151324-DS-18",
    "10225793-DS-33",
    "10225793-DS-34",
    "10225793-DS-36",
    "10262565-DS-19",
    "10262565-DS-20",
    "10388675-DS-19",
    "10388675-DS-20",
    "10666715-DS-8",
    "10747475-DS-4",
    "10760122-DS-21",
    "10880579-DS-11",
    "10902714-DS-17",
    "10923555-DS-9",
    "10960817-DS-14",
    "11102747-DS-2",
    "11198012-DS-6",
    "11198012-DS-7",
]

# GPT的分析指令
ANALYSIS_PROMPT_TEMPLATE = """

HCC Patient Scoring System - Standardized Clinical Assessment Protocol
You are a clinical decision support system. For each patient case, you MUST provide assessments in the exact format specified below. Follow these steps systematically:
1. ECOG Performance Status Assessment
Step 1: Primary Assessment (From Activity Status)
Mapping Rules:
- "Activity Status: Ambulatory - Independent" → ECOG PS 0
- "Activity Status: Ambulatory - with assistance" → ECOG PS 1-2  
- "walk unaided" → ECOG PS 0
- "walk with stick" → ECOG PS 1
- Patient dies upon discharge → ECOG PS 4
Step 2: Comorbidity Adjustment
Reference Table:
Significant Comorbidities                           ECOG PS
1. Extrahepatic primary cancers
   a. Cancer is cured                               0
   b. Cancer curable with active treatment          1-2
   c. Metastatic cancer                             3-4

2. Chronic heart failure
   a. Well controlled                               1-2
   b. Not well controlled                           3-4

3. Atrial fibrillation, coronary artery disease
   a. Well controlled                               0
   b. Not well controlled                           1-4

4. Brain aneurysm rupture, brain atrophy, stroke    0-4

5. Uncontrolled systemic infections                 3-4

6. Uncontrolled/poorly controlled diabetes          3-4

7. Chronic kidney disease with dialysis             3-4
Adjustment Logic:

If Step 1 ECOG PS ≥ Step 2 ECOG PS → Keep Step 1 score
If Step 1 ECOG PS < Step 2 ECOG PS → Adjust to Step 2 score

2. Child-Pugh Score Calculation
Scoring Table:
Parameter          1 point              2 points             3 points
Bilirubin          <2 mg/dL             2-3 mg/dL            >3 mg/dL
Albumin            >3.5 g/dL            2.8-3.5 g/dL         <2.8 g/dL
Ascites            Absent               Slight               Moderate
Encephalopathy     None                 Grade 1-2            Grade 3-4
INR                <1.7                 1.7-2.3              >2.3
Classification:

Class A = 5-6 points
Class B = 7-9 points
Class C = 10-15 points

Missing Data Rule: If ANY required parameter for calculation is missing → Extract Child-Pugh score directly from the original text
3. MELD Score Calculation
Formula (2016 version):
MELD = 9.57 × ln(Creatinine [mg/dL]) + 3.78 × ln(Bilirubin [mg/dL]) + 11.2 × ln(INR) + 6.43
Required Units:

Creatinine: mg/dL
Bilirubin: mg/dL
INR: ratio

Missing Data Rule: If ANY required parameter (Creatinine, Bilirubin, INR) is missing → MELD score = "-"
4. ALBI Score Calculation
Formula:
ALBI = (log10 bilirubin × 0.66) + (albumin × -0.085)
Required Units:

Bilirubin: μmol/L
Albumin: g/L

Unit Conversion (if needed):

Bilirubin: mg/dL × 17.1 = μmol/L
Albumin: g/dL × 10 = g/L

Missing Data Rule: If ANY required parameter (Bilirubin, Albumin) is missing → ALBI score = "-"
5. BCLC Staging Assessment
Required Data:

Tumor size and number
Macrovascular invasion (present/absent)
Extrahepatic spread (present/absent)
Child-Pugh class
ECOG PS score

Milan Criteria: 1 lesion ≤5cm OR 2-3 lesions ≤3cm, no macroscopic vascular invasion, no extrahepatic spread

MANDATORY OUTPUT FORMAT
For each patient assessment, provide EXACTLY this format:
PATIENT ASSESSMENT RESULTS

1. ECOG PERFORMANCE STATUS
   Step 1 (Primary): [Score with reasoning]
   Step 2 (Comorbidity): [Identified comorbidities and adjustment]
   Final ECOG PS: [Final score]

2. CHILD-PUGH SCORE
   [If calculated from parameters:]
   Bilirubin: [value] mg/dL = [points] points
   Albumin: [value] g/dL = [points] points  
   Ascites: [status] = [points] points
   Encephalopathy: [grade] = [points] points
   INR: [value] = [points] points
   Total: [sum] points = Child-Pugh Class [A/B/C]
   
   [If extracted from text:]
   Child-Pugh: [Class A/B/C] (extracted from original text)

3. MELD SCORE
   Creatinine: [value] mg/dL
   Bilirubin: [value] mg/dL
   INR: [value]
   MELD = 9.57×ln([creatinine]) + 3.78×ln([bilirubin]) + 11.2×ln([INR]) + 6.43
   MELD = [calculated value]
   [If any parameter missing: MELD = -]

4. ALBI SCORE
   Bilirubin: [value] μmol/L (converted from [original value] mg/dL)
   Albumin: [value] g/L (converted from [original value] g/dL)
   ALBI = (log10([bilirubin]) × 0.66) + ([albumin] × -0.085)
   ALBI = [calculated value]
   [If any parameter missing: ALBI = -]

5. BCLC STAGING
   Tumor characteristics: [description]
   Macrovascular invasion: [present/absent]
   Extrahepatic spread: [present/absent]
   Liver function: [Child-Pugh class]
   ECOG PS: [score]
   BCLC Stage: [stage with justification]

6. DATA AVAILABILITY
   Complete: [list complete assessments]
   Incomplete: [list missing data with "not available"]

SIMPLE OUTPUT SUMMARY:
BCLC staging: [0/A/B/C/D]
Child-Pugh score: Class [A/B/C]
MELD score: [numerical value or -]
ALBI score: [numerical value or -]
VALIDATION REQUIREMENTS

Always show your calculations step-by-step
For Child-Pugh: if calculation parameters are missing, extract directly from original text
For MELD and ALBI scores: if ANY required parameter is missing, set result to "-"
For other assessments: if data is missing, state "not available" for that specific parameter
Cross-check final scores for internal consistency
Prioritize patient safety in ambiguous cases
Use exact clinical terminology only
Always convert units when necessary for ALBI calculation

MEDICAL TEXT TO ANALYZE:
---
{medical_text}
---
"""

def analyze_text_with_gpt(content):
    """使用Azure GPT-4.1分析文本"""
    prompt = ANALYSIS_PROMPT_TEMPLATE.format(medical_text=content)
    
    headers = {
        "api-key": AZURE_OPENAI_API_KEY,
        "Content-Type": "application/json"
    }
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a clinical decision support system."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 4000,
        "temperature": 0.0, # 设置为0以获得更具确定性的输出
    }
    
    max_retries = 5
    for attempt in range(max_retries):
        try:
            response = requests.post(API_URL, headers=headers, json=data, timeout=600)
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content'].strip()
                else:
                    return "Error: No response choices from API."
            else:
                error_message = f"Error: API request failed with status code {response.status_code} - {response.text}"
                print(error_message)
                if attempt == max_retries - 1:
                    return error_message
        except requests.exceptions.RequestException as e:
            error_message = f"Error: Request exception - {e}"
            print(error_message)
            if attempt == max_retries - 1:
                return error_message
        
        time.sleep(10 * (attempt + 1)) # 指数退避
        
    return "Error: Max retries reached."

def main():
    output_filename = "batch_analysis_results.txt"
    
    with open(output_filename, "w", encoding="utf-8") as outfile:
        total_files = len(FILE_LIST)
        for i, filename_base in enumerate(FILE_LIST, 1):
            input_filepath = os.path.join("txts", f"{filename_base}.txt")
            
            print(f"[{i}/{total_files}] Analyzing {input_filepath}...")
            
            try:
                with open(input_filepath, "r", encoding="utf-8") as infile:
                    content = infile.read()
            except FileNotFoundError:
                print(f"  -> File not found. Skipping.")
                analysis_result = f"Error: File not found at {input_filepath}"
                continue
            
            analysis_result = analyze_text_with_gpt(content)
            
            outfile.write(f"--- ANALYSIS FOR: {filename_base} ---\n\n")
            outfile.write(analysis_result)
            outfile.write("\n\n\n")
            
            print(f"  -> Analysis complete. Result saved to {output_filename}")

    print(f"\nAll analyses are complete. Check the output file: {output_filename}")

if __name__ == "__main__":
    main()
