#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理txt文件中的GROUND TRUTH DATA部分，然后重新添加R到AF列的信息
"""

import os
import pandas as pd
import re

# 配置
EXCEL_PATH = r"C:\Users\<USER>\Desktop\case_new\MIMIC_HCC_GroundTruth.xlsx"
TXT_FOLDER = r"C:\Users\<USER>\Desktop\case_new\txts"

# 需要处理的文件列表
FILES_TO_PROCESS = [
    "10056223-DS-5",
    "10151324-DS-16",
    "10151324-DS-18",
    "10225793-DS-33",
    "10225793-DS-34",
    "10225793-DS-36",
    "10262565-DS-19",
    "10262565-DS-20",
    "10388675-DS-19",
    "10388675-DS-20",
    "10666715-DS-8",
    "10747475-DS-4",
    "10760122-DS-21",
    "10880579-DS-11",
    "10902714-DS-17",
    "10923555-DS-9",
    "10960817-DS-14",
    "11102747-DS-2",
    "11198012-DS-6",
    "11198012-DS-7"
]

# 需要提取的列
COLUMNS_TO_EXTRACT = {
    "R": "CP.1",
    "S": "INR.1",
    "T": "Albumin",
    "U": "Total bil",
    "V": "Ascites",
    "W": "HE",
    "X": "number",
    "Y": "size",
    "Z": "MacroVI",
    "AA": "ES",
    "AB": "PS",
    "AC": "Comorbid_sig",
    "AD": "PS_adjusted",
    "AE": "calculated CP",
    "AF": "BCLC.1"
}

def clean_file(file_path):
    """清理文件中的GROUND TRUTH DATA部分"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式查找并删除GROUND TRUTH DATA部分
        pattern = r'\n\n=+\nGROUND TRUTH DATA FROM EXCEL\n=+\n[\s\S]*?$'
        cleaned_content = re.sub(pattern, '', content)
        
        # 确保文件末尾没有多余的空行
        cleaned_content = cleaned_content.rstrip() + '\n'
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        return True
    except Exception as e:
        print(f"❌ 清理文件 {file_path} 时出错: {str(e)}")
        return False

def process_file(df, file_id):
    """处理单个文件"""
    txt_path = os.path.join(TXT_FOLDER, f"{file_id}.txt")
    
    # 检查txt文件是否存在
    if not os.path.exists(txt_path):
        print(f"❌ txt文件不存在: {txt_path}")
        return
    
    print(f"🔍 处理文件: {file_id}")
    
    # 先清理文件
    if not clean_file(txt_path):
        return
    
    # 在DataFrame中查找对应的行
    row = df[df['note_id'] == file_id]
    if row.empty:
        print(f"❌ 在Excel中未找到文件ID: {file_id}")
        return
    
    # 提取需要的列数据
    ground_truth_data = []
    ground_truth_data.append("\n\n" + "="*50)
    ground_truth_data.append("GROUND TRUTH DATA FROM EXCEL")
    ground_truth_data.append("="*50)
    
    missing_columns = []
    for col_letter, col_name in COLUMNS_TO_EXTRACT.items():
        try:
            if col_name not in df.columns:
                missing_columns.append(f"{col_name} ({col_letter})")
                continue
                
            value = row[col_name].values[0]
            # 处理NaN值
            if pd.isna(value):
                value = "N/A"
            ground_truth_data.append(f"{col_name} ({col_letter}): {value}")
        except KeyError:
            print(f"⚠️ 列 {col_name} 在Excel中不存在，跳过")
            missing_columns.append(f"{col_name} ({col_letter})")
        except Exception as e:
            print(f"⚠️ 处理列 {col_name} 时出错: {str(e)}")
            missing_columns.append(f"{col_name} ({col_letter}): 错误 - {str(e)}")
    
    if missing_columns:
        print(f"⚠️ 文件 {file_id} 缺少以下列: {', '.join(missing_columns)}")
    
    # 将数据添加到txt文件末尾
    try:
        with open(txt_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
            f.write("\n".join(ground_truth_data))
        
        print(f"✅ 已更新文件: {txt_path}")
    except Exception as e:
        print(f"❌ 更新文件 {txt_path} 时出错: {str(e)}")

def main():
    """主函数"""
    print("开始处理数据...")
    
    # 检查Excel文件是否存在
    if not os.path.exists(EXCEL_PATH):
        print(f"❌ Excel文件不存在: {EXCEL_PATH}")
        return
    
    # 检查txt文件夹是否存在
    if not os.path.exists(TXT_FOLDER):
        print(f"❌ txt文件夹不存在: {TXT_FOLDER}")
        return
    
    try:
        # 读取Excel文件
        print(f"📊 读取Excel文件: {EXCEL_PATH}")
        df = pd.read_excel(EXCEL_PATH)
        
        # 检查Excel文件中是否有所需的列
        missing_cols = []
        for col_letter, col_name in COLUMNS_TO_EXTRACT.items():
            if col_name not in df.columns:
                missing_cols.append(f"{col_name} ({col_letter})")
        
        if missing_cols:
            print(f"⚠️ Excel文件中缺少以下列: {', '.join(missing_cols)}")
            print("⚠️ 将继续处理，但这些列的数据将不会被添加")
        
        # 处理每个文件
        processed_files = 0
        for file_id in FILES_TO_PROCESS:
            process_file(df, file_id)
            processed_files += 1
        
        print(f"✅ 所有文件处理完成 ({processed_files}/{len(FILES_TO_PROCESS)})")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")

if __name__ == "__main__":
    main() 